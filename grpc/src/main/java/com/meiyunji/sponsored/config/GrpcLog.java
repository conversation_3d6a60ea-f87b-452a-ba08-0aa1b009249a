package com.meiyunji.sponsored.config;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * grpc log
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface GrpcLog {

    /**
     * 拦截的方法
     */
//    String[] methods() default {};

    /**
     * 不拦截的方法
     */
//    String[] excludeMethods() default {};

}
