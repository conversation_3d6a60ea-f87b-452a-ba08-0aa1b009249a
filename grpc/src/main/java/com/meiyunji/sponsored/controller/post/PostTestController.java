package com.meiyunji.sponsored.controller.post;

import com.meiyunji.sponsored.common.util.XxlJobParamsUtil;
import com.meiyunji.sponsored.service.post.service.IPostService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2025-04-09 19:01
 */
@RestController
@RequestMapping("/post/test")
@Slf4j
public class PostTestController {

    @Resource
    private IPostService postService;
    @Value(value = "${spring.profiles.active}")
    private String active;

    @RequestMapping("/syncPostProfiles")
    public void test(String params) {
        if (!"alpha".equals(active)) {
            return;
        }
        Map<String, String> parseQueryParams = XxlJobParamsUtil.parseQueryParams(params);
        String puidsStr = parseQueryParams.get("puid");
        List<Integer> puids = new ArrayList<>();
        if (StringUtils.isNotBlank(puidsStr)) {
            puids = Arrays.stream(StringUtils.split(puidsStr, ","))
                    .map(NumberUtils::toInt).filter(each -> each > 0).collect(Collectors.toList());
        }
        postService.syncUserPostProfiles(0, 1, puids);
    }

    @RequestMapping("/syncPosts")
    public void test2(String params) {
        if (!"alpha".equals(active)) {
            return;
        }
        Map<String, String> parseQueryParams = XxlJobParamsUtil.parseQueryParams(params);
        String puidsStr = parseQueryParams.get("puid");
        List<Integer> puids = new ArrayList<>();
        if (StringUtils.isNotBlank(puidsStr)) {
            puids = Arrays.stream(StringUtils.split(puidsStr, ","))
                    .map(NumberUtils::toInt).filter(each -> each > 0).collect(Collectors.toList());
        }
        postService.syncUserPosts(0, 1, puids);
    }

    @RequestMapping("/syncPostReportIncrementData")
    public void test3(Integer puid, Integer shopId, String postProfileId) {
        if (!"alpha".equals(active)) {
            return;
        }
        postService.syncPostReportIncrementData(0, 1, puid, shopId, postProfileId);
    }

    @RequestMapping("/syncPostReportAllData")
    public void test4(Integer puid, Integer shopId, String postProfileId) {
        if (!"alpha".equals(active)) {
            return;
        }
        postService.syncPostReportAllData(0, 1, puid, shopId, postProfileId);
    }

    @RequestMapping("/ping")
    public String testPing(String params) {
        if (!"alpha".equals(active)) {
            return null;
        }
        return "pong:" + params;
    }
}
