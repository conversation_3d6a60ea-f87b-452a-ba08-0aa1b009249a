package com.meiyunji.sponsored.api.dashboard;

import com.meiyunji.sponsored.api.dashboard.utils.DashboardChartUtil;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.grpc.common.entity.DashboardChartPb;
import com.meiyunji.sponsored.grpc.dashboard.*;
import com.meiyunji.sponsored.service.dashboard.dao.IDashboardDao;
import com.meiyunji.sponsored.service.dashboard.dto.*;
import com.meiyunji.sponsored.service.dashboard.service.DashBoardService;
import com.meiyunji.sponsored.service.strategy.enums.AdType;
import com.meiyunji.sponsored.common.util.MultiThreadQueryAndMergeUtil;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@GRpcService
@Slf4j
public class DashboardApiRpcService extends DashboardApiServiceGrpc.DashboardApiServiceImplBase {

    private final DashBoardService dashBoardService;
    private final IDashboardDao dashboardDao;

    @Resource
    private MultiThreadQueryAndMergeUtil multiThreadQueryAndMergeUtil;

    public DashboardApiRpcService(DashBoardService dashBoardService,IDashboardDao dashboardDao) {
        this.dashBoardService = dashBoardService;
        this.dashboardDao = dashboardDao;
    }

    /**
     * 分页查询活动
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void listCampaigns(
            ListCampaignsRequestPb.ListCampaignsRequest request,
            StreamObserver<ListCampaignsResponsePb.ListCampaignsResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            int puid = request.getPuid();
            List<Integer> shopIdList = request.getShopIdList();
            String adType = request.getAdType();
            String name = request.getName();
            List<DashBoardCampaignDto> list;
            String portfolioId = request.getPortfolioId();
            String status = request.getStatus();

            int pageNo = request.getPageNo() == 0 ? 1 : request.getPageNo();
            int pageSize = request.getPageSize() == 0 ? 20 : request.getPageSize();

            Integer totalSize = dashBoardService.countAllCampaigns(puid, shopIdList, adType, name, portfolioId, status);
            int totalPage = (totalSize - 1) / pageSize + 1;
            //如果当前页大于总页数,则显示最后一页
            pageNo = Math.min(pageNo, totalPage);
            //如果当前页小于0,则显示第一页
            pageNo = Math.max(pageNo, 1);
            if (totalSize <= 0) {
                list = new ArrayList<>();
            } else {
                list = dashBoardService.listCampaignsByPuidAndShopId(puid, shopIdList, adType, name, portfolioId,
                        status,pageNo, pageSize);
            }

            ListCampaignsResponsePb.ListCampaignsResponse.Builder builder =
                    ListCampaignsResponsePb.ListCampaignsResponse.newBuilder();

            ListCampaignsResponsePb.ListCampaignsResponse.Data.Builder dataBuilder =
                    ListCampaignsResponsePb.ListCampaignsResponse.Data.newBuilder();
            dataBuilder.setPageNo(pageNo);
            dataBuilder.setPageSize(pageSize);
            dataBuilder.setTotalPage(totalPage);
            dataBuilder.setTotalSize(totalSize);
            dataBuilder.addAllRows(list.stream().map($ -> ListCampaignsResponsePb.ListCampaignsResponse.Row.newBuilder()
                    .setCampaignId($.getCampaignId())
                    .setAdType($.getAdType().name())
                    .setName($.getName())
                    .setShopId($.getShopId()).build()).collect(Collectors.toList()));

            builder.setCode(0);
            builder.setData(dataBuilder.build());
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("error: ", e);
            responseObserver.onError(e);
        }
    }

    /**
     * 实时播报
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void realTimeHourlyData(
            RealTimeHourlyDataRequestPb.RealTimeHourlyDataRequest request,
            StreamObserver<RealTimeHourlyDataResponsePb.RealTimeHourlyDataResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            int puid = request.getPuid();
            List<Integer> shopIdList = request.getShopIdList();
            int hour = request.getHour();
            String currency = request.getCurrency();
            String portfolioId = request.getPortfolioId();
            String status = request.getStatus();
            List<String> campaignIds = request.getCampaignList().stream().map($ -> $.getCampaignId()).
                    collect(Collectors.toList());
            boolean flag = true;
            List<DashBoardCampaignDto> dashBoardCampaignDtoList = new ArrayList();
            if (CollectionUtils.isNotEmpty(campaignIds) || StringUtils.isNotBlank(portfolioId) || StringUtils.isNotBlank(status)){
                dashBoardCampaignDtoList = dashboardDao.listCampaignsByCompidAndPortfidAndState(puid,
                        shopIdList, campaignIds, portfolioId, status);
                flag = false;
            }

            RealTimeHourlyDataResponsePb.RealTimeHourlyDataResponse.Builder builder =
                    RealTimeHourlyDataResponsePb.RealTimeHourlyDataResponse.newBuilder();
            RealTimeHourlyDataResponsePb.RealTimeHourlyDataResponse.Data.Builder dataBuilder =
                    RealTimeHourlyDataResponsePb.RealTimeHourlyDataResponse.Data.newBuilder();
            if (flag || (dashBoardCampaignDtoList != null && dashBoardCampaignDtoList.size() > 0)) {
                List<CampaignInfoDto> campaigns = new ArrayList<>(dashBoardCampaignDtoList.stream().map($ -> {
                    CampaignInfoDto dto = new CampaignInfoDto();
                    dto.setCampaignId($.getCampaignId());
                    dto.setShopId($.getShopId());
                    dto.setAdType($.getAdType());
                    return dto;
                }).collect(Collectors.toList()));

                DashboardHourReportDto dto = new DashboardHourReportDto();
                List<DashboardHourReportDto> dashboardHourReportDtoVector = new ArrayList<>();
                //小时级数据,目前只有SP类型的数据
                if (StringUtils.isNotBlank(request.getAdType()) && !AdType.SP.name().equalsIgnoreCase(request.getAdType())) {
                    dto.setLastUpdateAt(LocalDateTime.now(ZoneId.of("UTC")).minusMinutes(20)
                            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                } else {
                    // 多线程请求, 结果归并
                    dto.setLastUpdateAt(LocalDateTime.now(ZoneId.of("UTC")).minusMinutes(0)
                            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    if (campaigns.size() > 0) {
                        MultiThreadQueryParamDto queryParam = MultiThreadQueryParamDto.builder()
                                .puid(puid)
                                .shopIds(shopIdList)
                                .hour(hour)
                                .currency(currency)
                                .build();
                        // 多线程请求, 结果归并
                        dashboardHourReportDtoVector = multiThreadQueryAndMergeUtil.multiThreadQuery(() -> campaigns, queryParam,
                                dashBoardService::realTimeHourlyData);
                    } else {
                        DashboardHourReportDto threadDashboardHourReportDto = dashBoardService.realTimeHourlyData(puid, shopIdList, hour, currency, campaigns);
                        dashboardHourReportDtoVector.add(threadDashboardHourReportDto);
                    }
                }
                // 处理归并结果
                for (DashboardHourReportDto dashboardHourReportDto : dashboardHourReportDtoVector) {
                    dto.setCost(dashboardHourReportDto.getCost() + dto.getCost());
                    dto.setClicks(dashboardHourReportDto.getClicks() + dto.getClicks());
                    dto.setImpressions(dashboardHourReportDto.getImpressions() + dto.getImpressions());
                    dto.setClickRate(dashboardHourReportDto.getClickRate() + dto.getClickRate());
                    dto.setCpc(dashboardHourReportDto.getCpc() + dto.getCpc());
                    dto.setAcos(dashboardHourReportDto.getAcos() + dto.getAcos());
                    dto.setRoas(dashboardHourReportDto.getRoas() + dto.getRoas());
                    dto.setAdOrder(dashboardHourReportDto.getAdOrder() + dto.getAdOrder());
                    dto.setAdSales(dashboardHourReportDto.getAdSales() + dto.getAdSales());
                    dto.setSalesConversionRate(dashboardHourReportDto.getSalesConversionRate() + dto.getSalesConversionRate());
                    dto.setLastUpdateAt(dashboardHourReportDto.getLastUpdateAt());
                }
                int size = (dashboardHourReportDtoVector.size() == 0 ? 1 : dashboardHourReportDtoVector.size());
                dataBuilder.setClicks(dto.getClicks());
                dataBuilder.setImpressions(dto.getImpressions());
                dataBuilder.setClickRate(dto.getClickRate() / size);
                dataBuilder.setCpc(dto.getCpc());
                dataBuilder.setSalesConversionRate(dto.getSalesConversionRate() / size);
                dataBuilder.setCost(dto.getCost());
                dataBuilder.setAdOrder(dto.getAdOrder());
                dataBuilder.setAdSales(dto.getAdSales());
                dataBuilder.setAcos(dto.getAcos() / size);
                dataBuilder.setRoas(dto.getRoas() / size);
                //小于1分钟显示一分钟, 无数显示20分钟前
                long minutes = Duration.between(LocalDateTime.now(ZoneId.of("UTC")), LocalDateTime.parse(dto.getLastUpdateAt(),
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).toMinutes();
                dataBuilder.setLastUpdateAt(Math.max(1, Math.min(20, Math.abs(minutes))) + "分钟前");
            }

            builder.setCode(Result.SUCCESS);
            builder.setData(dataBuilder.build());

            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("error: ", e);
            responseObserver.onError(e);
        }
    }

    /**
     * 广告贡献
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAdContributes(
            AdContributesRequestPb.AdContributesRequest request,
            StreamObserver<AdContributesResponsePb.AdContributesResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            int puid = request.getPuid();
            List<Integer> shopIdList = request.getShopIdList();
            String adType = request.getAdType();
            String currency = request.getCurrency();
            LocalDate startDate = LocalDate.parse(request.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
            LocalDate endDate = LocalDate.parse(request.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
            String portfolioId = request.getPortfolioId();
            String status = request.getStatus();
            List<String> campaignIds = request.getCampaignList().stream().map($ -> $.getCampaignId()).
                    collect(Collectors.toList());


            AdContributesResponsePb.AdContributesResponse.Builder builder =
                    AdContributesResponsePb.AdContributesResponse.newBuilder();
            AdContributesResponsePb.AdContributesResponse.Data.Builder dataBuilder =
                    AdContributesResponsePb.AdContributesResponse.Data.newBuilder();
            AdContributesDataDto adContributes;

            adContributes = dashBoardService.getAdContributes(
                    puid, shopIdList, adType, currency, startDate, endDate, campaignIds, portfolioId, status);
            //chart图数据填充
            dataBuilder.setShopSale(adContributes.getShopSale().doubleValue());
            dataBuilder.setACoTS(adContributes.getACoTS().doubleValue());
            dataBuilder.setASoTS(adContributes.getASoTS().doubleValue());
            dataBuilder.setPerShopSale(adContributes.getPerShopSale().doubleValue());
            dataBuilder.setPerACoTS(adContributes.getPerACoTS().doubleValue());
            dataBuilder.setPerASoTS(adContributes.getPerASoTS().doubleValue());
            dataBuilder.setShopSaleGrowRate(adContributes.getShopSaleGrowRate().doubleValue());
            dataBuilder.setACoTSGrowRate(adContributes.getACoTSGrowRate().doubleValue());
            dataBuilder.setASoTSGrowRate(adContributes.getASoTSGrowRate().doubleValue());

            //chart图数据填充
            List<AdContributesDto> sortVos = adContributes.getList().stream()
                    .sorted(Comparator.comparing(String::valueOf)).collect(Collectors.toList());
            //日chart图数据
            List<DashboardChartPb.DashboardChart> dailyChart = DashboardChartUtil.getContributesChart(sortVos);
            //周chart图数据
            List<DashboardChartPb.DashboardChart> weeklyChart = DashboardChartUtil.getContributesChart(
                    DashboardChartUtil.getContributesWeekVos(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                            endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), sortVos));
            //月chart图数据
            List<DashboardChartPb.DashboardChart> monthlyChart =
                    DashboardChartUtil.getContributesChart(DashboardChartUtil.getContributesMonthVos(sortVos));

            dataBuilder.addAllDay(dailyChart);
            dataBuilder.addAllWeek(weeklyChart);
            dataBuilder.addAllMonth(monthlyChart);

            builder.setCode(Result.SUCCESS);
            builder.setData(dataBuilder.build());
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("error: ", e);
            responseObserver.onError(e);
        }
    }

    /**
     * 广告分类数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAdvertisingData(
            AdvertisingDataRequestPb.AdvertisingDataRequest request,
            StreamObserver<AdvertisingDataResponsePb.AdvertisingDataResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            int puid = request.getPuid();
            List<Integer> shopIdList = request.getShopIdList();
            String adType = request.getAdType();
            String currency = request.getCurrency();
            LocalDate startDate = LocalDate.parse(request.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
            LocalDate endDate = LocalDate.parse(request.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
            List<CampaignInfoDto> campaigns = new ArrayList<>();
            String portfolioId = request.getPortfolioId();
            String status = request.getStatus();
            List<String> campaignIds = request.getCampaignList().stream().map($ -> $.getCampaignId()).
                    collect(Collectors.toList());

            List<AdvertisingDataResponsePb.AdvertisingDataResponse.Data> dataList = new ArrayList<>();


                List<AdvertisingDataDto> list =
                        dashBoardService.getAdvertisingData(puid, shopIdList, adType, currency, startDate, endDate,  campaignIds, portfolioId, status);
            dataList = list.stream().map($ ->
                    AdvertisingDataResponsePb.AdvertisingDataResponse.Data.newBuilder()
                            .setType($.getType().name())
                            .setCost($.getCost().doubleValue())
                            .setSales($.getSales().doubleValue())
                            .setAdOrder($.getAdOrder())
                            .setAcos($.getAcos().doubleValue())
                            .setRoas($.getRoas().doubleValue())
                            .setSalesConversionRate($.getSalesConversionRate().doubleValue()).build()).collect(Collectors.toList());


            Map<String, AdvertisingDataResponsePb.AdvertisingDataResponse.Data> dataDtoMap = dataList.stream()
                    .collect(Collectors.toMap(AdvertisingDataResponsePb.AdvertisingDataResponse.Data::getType, Function.identity(), (a, b) -> a));
            //填充无数据的类型
            if (!dataDtoMap.containsKey(AdType.SP.name())) {
                AdvertisingDataResponsePb.AdvertisingDataResponse.Data.Builder adBuilder =
                        AdvertisingDataResponsePb.AdvertisingDataResponse.Data.newBuilder();
                adBuilder.setType(AdType.SP.name());
                dataDtoMap.put(AdType.SP.name(), adBuilder.build());
            }
            if (!dataDtoMap.containsKey(AdType.SB.name())) {
                AdvertisingDataResponsePb.AdvertisingDataResponse.Data.Builder adBuilder =
                        AdvertisingDataResponsePb.AdvertisingDataResponse.Data.newBuilder();
                adBuilder.setType(AdType.SB.name());
                dataDtoMap.put(AdType.SB.name(), adBuilder.build());
            }
            if (!dataDtoMap.containsKey(AdType.SD.name())) {
                AdvertisingDataResponsePb.AdvertisingDataResponse.Data.Builder adBuilder =
                        AdvertisingDataResponsePb.AdvertisingDataResponse.Data.newBuilder();
                adBuilder.setType(AdType.SD.name());
                dataDtoMap.put(AdType.SD.name(), adBuilder.build());
            }

            AdvertisingDataResponsePb.AdvertisingDataResponse.Builder builder =
                    AdvertisingDataResponsePb.AdvertisingDataResponse.newBuilder();

            builder.setCode(Result.SUCCESS);
            builder.putAllData(dataDtoMap);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("error: ", e);
            responseObserver.onError(e);
        }
    }

    /**
     * 广告效果
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAdPerformance(
            AdPerformanceDataRequestPb.AdPerformanceDataRequest request,
            StreamObserver<AdPerformanceDataResponsePb.AdPerformanceDataResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            int puid = request.getPuid();
            List<Integer> shopIdList = request.getShopIdList();
            String adType = request.getAdType();
            String currency = request.getCurrency();

            LocalDate startDate = LocalDate.parse(request.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
            LocalDate endDate = LocalDate.parse(request.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
            String portfolioId = request.getPortfolioId();
            String status = request.getStatus();
            List<String> campaignIds = request.getCampaignList().stream().map($ -> $.getCampaignId()).
                    collect(Collectors.toList());

            List<CampaignInfoDto> campaigns = new ArrayList<>();
            AdPerformanceDataResponsePb.AdPerformanceDataResponse.Builder builder =
                    AdPerformanceDataResponsePb.AdPerformanceDataResponse.newBuilder();
            AdPerformanceDataResponsePb.AdPerformanceDataResponse.Data.Builder dataBuilder =
                    AdPerformanceDataResponsePb.AdPerformanceDataResponse.Data.newBuilder();
            AdPerformanceDataDto adPerformance;


            adPerformance = dashBoardService.getAdPerformance(
                    puid, shopIdList, adType, currency, startDate, endDate, campaignIds, portfolioId, status);

            dataBuilder.setImpressions(adPerformance.getImpressions());
            dataBuilder.setClicks(adPerformance.getClicks());
            dataBuilder.setClickRate(adPerformance.getClickRate().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setCpc(adPerformance.getCpc().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setSalesConversionRate(adPerformance.getSalesConversionRate().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setCost(adPerformance.getCost().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setAdOrder(adPerformance.getAdOrder());
            dataBuilder.setAdSales(adPerformance.getAdSales().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setAcos(adPerformance.getAcos().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setRoas(adPerformance.getRoas().setScale(2, RoundingMode.HALF_UP).doubleValue());

            dataBuilder.setPerImpressions(adPerformance.getPerImpressions());
            dataBuilder.setPerClicks(adPerformance.getPerClicks());
            dataBuilder.setPerClickRate(adPerformance.getPerClickRate().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setPerCpc(adPerformance.getPerCpc().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setPerSalesConversionRate(adPerformance.getPerSalesConversionRate().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setPerCost(adPerformance.getPerCost().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setPerAdOrder(adPerformance.getPerAdOrder());
            dataBuilder.setPerAdSales(adPerformance.getPerAdSales().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setPerAcos(adPerformance.getPerAcos().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setPerRoas(adPerformance.getPerRoas().setScale(2, RoundingMode.HALF_UP).doubleValue());

            dataBuilder.setImpressionsGrowRate(adPerformance.getImpressionsGrowRate().doubleValue());
            dataBuilder.setClicksGrowRate(adPerformance.getClicksGrowRate().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setClickRateGrowRate(adPerformance.getClickRateGrowRate().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setCpcGrowRate(adPerformance.getCpcGrowRate().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setSalesConversionRateGrowRate(adPerformance.getSalesConversionRateGrowRate().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setCostGrowRate(adPerformance.getCostGrowRate().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setAdOrderGrowRate(adPerformance.getAdOrderGrowRate().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setAdSalesGrowRate(adPerformance.getAdSalesGrowRate().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setAcosGrowRate(adPerformance.getAcosGrowRate().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setRoasGrowRate(adPerformance.getRoasGrowRate().setScale(2, RoundingMode.HALF_UP).doubleValue());


            //chart图数据填充
            List<AdPerformanceDto> sortVos = adPerformance.getList().stream()
                    .sorted(Comparator.comparing(String::valueOf)).collect(Collectors.toList());
            List<DashboardChartPb.DashboardChart> dailyChart = DashboardChartUtil.getPerformanceChart(sortVos);

            List<DashboardChartPb.DashboardChart> weeklyChart = DashboardChartUtil.getPerformanceChart(
                    DashboardChartUtil.getPerformanceWeekVos(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                            endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), sortVos));

            List<DashboardChartPb.DashboardChart> monthlyChart = DashboardChartUtil.getPerformanceChart(
                    DashboardChartUtil.getPerformanceMonthVos(sortVos));

            dataBuilder.addAllDay(dailyChart);
            dataBuilder.addAllWeek(weeklyChart);
            dataBuilder.addAllMonth(monthlyChart);

            builder.setCode(Result.SUCCESS);
            builder.setData(dataBuilder.build());
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("error: ", e);
            responseObserver.onError(e);
        }
    }


    /**
     * 广告top10数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getCampaignTopTen(
            CampaignTopTenRequestPb.CampaignTopTenRequest request,
            StreamObserver<CampaignTopTenResponsePb.CampaignTopTenResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            int puid = request.getPuid();
            List<Integer> shopIdList = request.getShopIdList();
            String adType = request.getAdType();
            String orderBy = request.getOrderBy();
            String currency = request.getCurrency();

            LocalDate startDate = LocalDate.parse(request.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
            LocalDate endDate = LocalDate.parse(request.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
            List<CampaignInfoDto> campaigns = new ArrayList<>();
            String portfolioId = request.getPortfolioId();
            String status = request.getStatus();
            List<String> campaignIds = request.getCampaignList().stream().map($ -> $.getCampaignId()).
                    collect(Collectors.toList());
            CampaignTopTenResponsePb.CampaignTopTenResponse.Builder builder =
                    CampaignTopTenResponsePb.CampaignTopTenResponse.newBuilder();

            List<CampaignTopTenDto> list = dashBoardService.getCampaignTopTen(
                    puid, shopIdList, adType, currency, orderBy, startDate, endDate, campaignIds, portfolioId, status);
            builder.addAllData(list.stream().map($ -> CampaignTopTenResponsePb.CampaignTopTenResponse.Data.newBuilder()
                    .setCampaignId($.getCampaignId())
                    .setCampaignName($.getCampaignName())
                    .setShopId($.getShopId())
                    .setAdType($.getAdType())
                    .setShopName($.getShopName())
                    .setCostRate($.getCostRate().doubleValue())
                    .setSalesRate($.getSalesRate().doubleValue())
                    .setPerCostRate($.getPerCostRate().doubleValue())
                    .setPerSalesRate($.getPerSalesRate().doubleValue())
                    .build()).collect(Collectors.toList()));


            builder.setCode(Result.SUCCESS);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("error: ", e);
            responseObserver.onError(e);
        }
    }

    /**
     * 查询搜索词列表
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getQueryWords(
            QueryWordsRequestPb.QueryWordsRequest request,
            StreamObserver<QueryWordsResponsePb.QueryWordsResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            int puid = request.getPuid();
            List<Integer> shopIdList = request.getShopIdList();
            String orderBy = request.getOrderBy();
            String currency = request.getCurrency();

            LocalDate startDate = LocalDate.parse(request.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
            LocalDate endDate = LocalDate.parse(request.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
            List<CampaignInfoDto> campaigns = new ArrayList<>();
            if (request.getCampaignCount() > 0) {
                campaigns = request.getCampaignList().stream().map($ -> {
                    CampaignInfoDto dto = new CampaignInfoDto();
                    dto.setCampaignId($.getCampaignId());
                    dto.setShopId($.getShopId());
                    dto.setAdType(AdType.valueOf($.getAdType().toUpperCase()));
                    return dto;
                }).collect(Collectors.toList());
            }

            List<AdQueryWordDto> queryWords = dashBoardService.listQueryWord(
                    puid, shopIdList, currency, orderBy, startDate, endDate, campaigns);

            QueryWordsResponsePb.QueryWordsResponse.Builder builder = QueryWordsResponsePb.QueryWordsResponse.newBuilder();

            builder.setCode(Result.SUCCESS);
            builder.addAllData(queryWords.stream().map($ ->
                    QueryWordsResponsePb.QueryWordsResponse.Data.newBuilder()
                            .setImpressions($.getImpressions())
                            .setClicks($.getClicks())
                            .setCost($.getCost().setScale(2, RoundingMode.HALF_UP).doubleValue())
                            .setAdSales($.getAdSales().setScale(2, RoundingMode.HALF_UP).doubleValue())
                            .setAdOrder($.getAdOrder())
                            .setAcos($.getAcos().setScale(2, RoundingMode.HALF_UP).doubleValue())
                            .setRoas($.getRoas().setScale(2, RoundingMode.HALF_UP).doubleValue())
                            .setSalesConversionRate($.getSalesConversionRate()
                                    .setScale(2, RoundingMode.HALF_UP).doubleValue())
                            .setCpc($.getCpc().setScale(2, RoundingMode.HALF_UP).doubleValue())
                            .setClickRate($.getClickRate().setScale(2, RoundingMode.HALF_UP).doubleValue())
                            .setAdType($.getAdType())
                            .setQuery($.getQuery())
                            .setQueryType($.getQueryType())
                            .setCampaignName($.getCampaignName() == null ? "" : $.getCampaignName())
                            .setCampaignId($.getCampaignId())
                            .setAdGroupId($.getAdGroupId())
                            .setShopId($.getShopId())
                            .setMarketplaceId($.getMarketplaceId())
                            .build()
            ).collect(Collectors.toList()));
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("error: ", e);
            responseObserver.onError(e);
        }
    }
}
