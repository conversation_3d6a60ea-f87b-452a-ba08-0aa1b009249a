package com.meiyunji.sponsored.api.newDashboard;

import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.rpc.newDashboard.*;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardCurrencyEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardModuleEnum;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdPlacementService;
import com.meiyunji.sponsored.service.newDashboard.util.DateTimeUtil;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardBaseReqVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-04-01 11:37
 */
@GRpcService
@Slf4j
public class DashboardAdPlacementRpcService extends RpcDashboardAdPlacementServiceGrpc.RpcDashboardAdPlacementServiceImplBase {
    @Autowired
    private IDashboardAdPlacementService dashboardAdPlacementService;

    @Override
    public void queryAdPlacementCharts(DashboardAdPlacementRequest request, StreamObserver<DashboardAdPlacementResponse> responseObserver) {
        log.info("dashboard query adplacement charts, request data: {}", request);
        StopWatch sw = new StopWatch();
        sw.start();
        DashboardAdPlacementResponse.Builder builder = DashboardAdPlacementResponse.newBuilder();

        DashboardBaseReqVo reqVo = processParam(request);

        log.info("dashboard query adplacement charts, process request data: {}", request);

        if (Objects.isNull(reqVo)) {
            log.error("dashboard query adplacement charts, check param error");
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        } else {
            List<DashboardAdPlacementResponseVo> voList = dashboardAdPlacementService.queryAdPlacementCharts(reqVo);
            builder.addAllData(voList);
            builder.setCode(Result.SUCCESS);

            sw.stop();
            log.info("dashboard query adplacement charts, puid: {}, 耗时: {}秒", request.getPuid(), sw.getTotalTimeSeconds());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private DashboardBaseReqVo processParam(DashboardAdPlacementRequest request) {
        //校验值是否合法
        if (!DashboardCurrencyEnum.currencySet.contains(request.getCurrency())) {
            return null;
        }

        //将grpc请求对象转换未查询实体类
        DashboardBaseReqVo reqVo = new DashboardBaseReqVo();
        BeanUtils.copyProperties(request, reqVo);
        Optional.of(request.getStartDate()).map(DateUtil::toFormatDate).ifPresent(reqVo::setStartDate);
        Optional.of(request.getEndDate()).map(DateUtil::toFormatDate).ifPresent(reqVo::setEndDate);
        reqVo.setPuid(reqVo.getPuid());
        reqVo.setMarketplaceIdList(reqVo.getMarketplaceIdList());
        reqVo.setShopIdList(reqVo.getShopIdList());
        DateTimeUtil.resetTimeRange(reqVo, DashboardModuleEnum.PLACEMENT);
        Optional.of(request.getPortfolioIdsList()).filter(CollectionUtils::isNotEmpty).ifPresent(reqVo::setPortfolioIds);
        Optional.of(request.getCampaignIdsList()).filter(CollectionUtils::isNotEmpty).ifPresent(reqVo::setCampaignIds);
        return reqVo;
    }
}
