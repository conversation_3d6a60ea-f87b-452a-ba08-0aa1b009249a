package com.meiyunji.sponsored.api.sp;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.Int32Value;
import com.google.protobuf.ProtocolStringList;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.adCommon.CommonShowAdPerformanceRequest;
import com.meiyunji.sponsored.rpc.adCommon.CommonShowAdPerformanceResponse;
import com.meiyunji.sponsored.rpc.keywords.AsyncAddKeywordsRequest;
import com.meiyunji.sponsored.rpc.keywords.BatchSuggestedBidRequest;
import com.meiyunji.sponsored.rpc.keywords.KeywordSuggestedBidVo;
import com.meiyunji.sponsored.rpc.sp.keyword.KeywordVo;
import com.meiyunji.sponsored.rpc.sp.keyword.*;
import com.meiyunji.sponsored.rpc.vo.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetObjectTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskConstant;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskMatchTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dto.AdTargetTaskDto;
import com.meiyunji.sponsored.service.cpc.dto.AdTargetTaskDto.AdTargetTaskDetailDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.qo.KeywordSuggestBidBatchQo;
import com.meiyunji.sponsored.service.cpc.service2.IAdTargetTaskService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbKeywordService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcKeywordsService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.SpSuggestService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.enums.StateEnum;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wade
 * @date: 2021/10/27 19:07
 * @describe:
 */
@GRpcService
@Slf4j
public class KeywordSpRpcService extends RPCSpKeywordServiceGrpc.RPCSpKeywordServiceImplBase {

    @Autowired
    private ICpcKeywordsService cpcKeywordsService;
    @Autowired
    private ICpcSbKeywordService cpcSbKeywordService;
    @Autowired
    private IAdTargetTaskService adTargetTaskService;
    @Autowired
    private SpSuggestService spSuggestService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;

    @Override
    public void showAdPerformanceVo(CommonShowAdPerformanceRequest request, StreamObserver<CommonShowAdPerformanceResponse> responseObserver) {
        CommonShowAdPerformanceResponse.Builder builder = CommonShowAdPerformanceResponse.newBuilder();
        //做参数校验
        if (!request.hasShopId()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");

        } else {
            AdPerformanceParam param = new AdPerformanceParam();
            param.setShopId(request.getShopId());
            param.setPuid(request.getPuid());
            param.setCampaignId(request.getCampaignId());
            param.setGroupId(request.getGroupId());
            param.setKeywordId(request.getKeywordId());
            param.setTargetId(request.getTargetId());
            param.setCpcProductId(request.getCpcProductId());
            param.setQuery(request.getQuery());
            param.setPlacement(request.getPlacement());
            param.setStartDate(request.getStartDate());
            param.setEndDate(request.getEndDate());

            if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
                param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
                param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            } else {
                param.setStartDate(param.getStartDate().replace("-", ""));
                param.setEndDate(param.getEndDate().replace("-", ""));
            }
            //处理业务返回结果
            Result<AdPerformanceVo> res = cpcKeywordsService.showKeywordPerformance(request.getPuid(), param);
            builder.setCode(res.getCode());
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
            if (res.success()) {
                //处理data
                AdPerformanceVo data = res.getData();
                if (data != null) {
                    AdPerformanceRpcVo.Builder voBuilder = AdPerformanceRpcVo.newBuilder();
                    if (data.getShopId() != null) {
                        voBuilder.setShopId(data.getShopId());
                    }
                    if (data.getCampaignId() != null) {
                        voBuilder.setCampaignId(data.getCampaignId());
                    }
                    if (data.getGroupId() != null) {
                        voBuilder.setGroupId(data.getGroupId());
                    }
                    if (data.getKeywordId() != null) {
                        voBuilder.setKeywordId(data.getKeywordId());
                    }
                    if (data.getTargetId() != null) {
                        voBuilder.setTargetId(data.getTargetId());
                    }
                    if (data.getAdId() != null) {
                        voBuilder.setAdId(data.getAdId());
                    }
                    if (data.getQuery() != null) {
                        voBuilder.setQuery(data.getQuery());
                    }
                    if (data.getPlacement() != null) {
                        voBuilder.setPlacement(data.getPlacement());
                    }
                    Map<String, CpcCommPageRpcVo> rpcVoMap = Maps.newHashMap();

                    if (MapUtils.isNotEmpty(data.getMap())) {
                        for (Map.Entry<String, CpcCommPageVo> entry : data.getMap().entrySet()) {
                            CpcCommPageVo vo = entry.getValue();
                            //vo转message
                            CpcCommPageRpcVo.Builder cpcRpcVo = CpcCommPageRpcVo.newBuilder();
                            cpcRpcVo.setImpressions(Optional.ofNullable(vo.getImpressions()).orElse(0));
                            cpcRpcVo.setClicks(Optional.ofNullable(vo.getClicks()).orElse(0));
                            cpcRpcVo.setCtr(StringUtils.isNotBlank(vo.getCtr()) ? vo.getCtr() : "0");
                            cpcRpcVo.setCvr(StringUtils.isNotBlank(vo.getCvr()) ? vo.getCvr() : "0");
                            cpcRpcVo.setAcos(StringUtils.isNotBlank(vo.getAcos()) ? vo.getAcos() : "0");
                            cpcRpcVo.setRoas(StringUtils.isNotBlank(vo.getRoas()) ? vo.getRoas() : "0");
                            cpcRpcVo.setAcots(StringUtils.isNotBlank(vo.getAcots()) ? vo.getAcots() : "0");
                            cpcRpcVo.setAdOrderNum(Optional.ofNullable(vo.getAdOrderNum()).orElse(0));
                            cpcRpcVo.setAdCost(StringUtils.isNotBlank(vo.getAdCost()) ? vo.getAdCost() : "0");
                            cpcRpcVo.setAdCostPerClick(StringUtils.isNotBlank(vo.getAdCostPerClick()) ? vo.getAdCostPerClick() : "0");
                            cpcRpcVo.setAdSale(StringUtils.isNotBlank(vo.getAdSale()) ? vo.getAdSale() : "0");

                            rpcVoMap.put(entry.getKey(), cpcRpcVo.build());
                        }

                        voBuilder.putAllMap(rpcVoMap);
                    }
                    builder.setData(voBuilder.build());
                }
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 添加关键词
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void addKeywords(AddSpKeywordsRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-keyword-添加关键词 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (StringUtils.isBlank(request.getGroupId())
                || !request.hasShopId() || !request.hasPuid()
                || CollectionUtils.isEmpty(request.getKeywordList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            AddKeywordsVo keywordsVo = new AddKeywordsVo();
            keywordsVo.setPuid(request.getPuid().getValue());
            keywordsVo.setShopId(request.getShopId().getValue());
            keywordsVo.setUid(request.getUid().getValue());
            keywordsVo.setGroupId(request.getGroupId());
            //处理list
            List<KeywordsRpcVo> keywordList = request.getKeywordList();
            List<KeywordsVo> keyVos = keywordList.stream().filter(Objects::nonNull).map(item -> {
                KeywordsVo keyVo = new KeywordsVo();
                keyVo.setMatchType(item.getMatchType());
                keyVo.setRangeEnd(item.getRangeEnd());
                keyVo.setRangeStart(item.getRangeStart());
                keyVo.setSuggested(item.getSuggested());
                keyVo.setBid(item.getBid());
                keyVo.setKeywordText(StringUtil.replaceSpecialSymbol(item.getKeywordText()));
                return keyVo;
            }).collect(Collectors.toList());
            keywordsVo.setKeywords(keyVos);
            //处理业务返回结果
            Result res = cpcKeywordsService.addKeywords(keywordsVo, request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }

        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 异步添加关键词
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void asyncCreateKeywords(AsyncAddKeywordsRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-keyword-异步添加关键词 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        String error = verifyAsyncParam(request);
        if (StringUtils.isNotBlank(error)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(error);
        } else {
            AdTargetTaskDto adTargetTaskDto = new AdTargetTaskDto();
            adTargetTaskDto.setPuid(request.getPuid().getValue());
            adTargetTaskDto.setShopId(request.getShopId().getValue());
            adTargetTaskDto.setUid(request.getUid().getValue());
            adTargetTaskDto.setType(AdTargetTaskTypeEnum.SP_KEYWORDS.getCode());
            adTargetTaskDto.setLoginIp(request.getLoginIp());
            adTargetTaskDto.setTargetPageType(request.getTargetingPageType().getValue());
            adTargetTaskDto.setSourceAdCampaignId(request.getSourceAdCampaignId());
            adTargetTaskDto.setSourceShopId(request.getSourceShopId());
            List<AdTargetTaskDetailDto> detailList = request.getKeywordList().stream().filter(Objects::nonNull).map(item -> {
                AdTargetTaskDetailDto detail = new AdTargetTaskDetailDto();
                detail.setAdCampaignId(item.getAdCampaignId());
                detail.setAdGroupId(item.getAdGroupId());
                detail.setMatchType(item.getMatchType());
                detail.setRangeEnd(StringUtils.isBlank(item.getRangeEnd()) ? null : new BigDecimal(item.getRangeEnd()));
                detail.setRangeStart(StringUtils.isBlank(item.getRangeStart()) ? null : new BigDecimal(item.getRangeStart()));
                detail.setSuggested(StringUtils.isBlank(item.getSuggested()) ? null : new BigDecimal(item.getSuggested()));
                detail.setBid(new BigDecimal(item.getBid()));
                detail.setTargetObject(item.getKeywordText());
                detail.setTargetObjectDesc(item.getKeywordCn());
                detail.setTargetObjectType(AdTargetObjectTypeEnum.KEYWORD.getCode());
                return detail;
            }).collect(Collectors.toList());
            adTargetTaskDto.setTaskDetails(detailList);
            long taskId = adTargetTaskService.recordTargetTask(adTargetTaskDto);
            adTargetTaskService.executeTask(taskId);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private String verifyAsyncParam(AsyncAddKeywordsRequest request) {
        if (!request.hasShopId() || !request.hasPuid() || !request.hasTargetingPageType()
                || CollectionUtils.isEmpty(request.getKeywordList()) || request.getKeywordList().size() > AdTargetTaskConstant.MAX_TARGET_SIZE) {
            return "请求参数错误";
        }
        List<AsyncAddKeywordsRpcVo> keywordList = request.getKeywordList();
        for (AsyncAddKeywordsRpcVo keyword : keywordList) {
            if (!AdTargetTaskMatchTypeEnum.SP_KEYWORD_SUPPORT_TYPES.contains(keyword.getMatchType())) {
                return "包含未支持的匹配类型";
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 建议关键词
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void suggestKeywords(SpSuggestKeywordsRequest request, StreamObserver<SuggestKeywordsResponse> responseObserver) {
        log.info("sp-keyword-获取建议关键词 request {}", request);
        SuggestKeywordsResponse.Builder builder = SuggestKeywordsResponse.newBuilder();
        if (!request.hasShopId() || !request.hasPuid() || StringUtils.isBlank(request.getGroupId())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result<List<SuggestedKeywordVo>> res = cpcKeywordsService.searchSuggestKeywords(request.getPuid().getValue(), request.getShopId().getValue(), request.getGroupId());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
            if (res.success()) {
                //处理list
                List<SuggestedKeywordVo> data = res.getData();
                List<SuggestedKeywordRpcVo> rpcVos = data.stream().filter(Objects::nonNull).map(item -> {
                    SuggestedKeywordRpcVo.Builder voBuilder = SuggestedKeywordRpcVo.newBuilder();
                    if (item.getKeywordId() != null) {
                        voBuilder.setKeywordId(item.getKeywordId());
                    }
                    if (item.getKeywordText() != null) {
                        voBuilder.setKeywordText(item.getKeywordText());
                    }
                    if (item.getKeywordTextCn() != null) {
                        voBuilder.setKeywordTextCn(item.getKeywordTextCn());
                    }
                    if (item.getMatchType() != null) {
                        voBuilder.setMatchType(item.getMatchType());
                    }
                    if (item.getSuggested() != null) {
                        voBuilder.setSuggested(item.getSuggested());
                    }
                    if (item.getRangeStart() != null) {
                        voBuilder.setRangeStart(item.getRangeStart());
                    }
                    if (item.getRangeEnd() != null) {
                        voBuilder.setRangeEnd(item.getRangeEnd());
                    }
                    if (item.getRangeEnd() != null) {
                        voBuilder.setRangeEnd(item.getRangeEnd());
                    }
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 建议关键词
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void suggestKeywordsByAsin(SpSuggestKeywordsByAsinRequest request, StreamObserver<SuggestKeywordsResponse> responseObserver) {
        log.info("sp-keyword-byasin-获取建议关键词 request {}", request);
        SuggestKeywordsResponse.Builder builder = SuggestKeywordsResponse.newBuilder();
        if (!request.hasShopId() || !request.hasPuid() || CollectionUtils.isEmpty(request.getAsinList()) || request.getAsinList().size() > 1000) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result<List<SuggestedKeywordVo>> res = cpcKeywordsService.searchSuggestKeywordsByAsin(request.getPuid().getValue(), request.getShopId().getValue(), request.getAsinList());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
            if (res.success()) {
                //处理list
                List<SuggestedKeywordVo> data = res.getData();
                List<SuggestedKeywordRpcVo> rpcVos = data.stream().filter(Objects::nonNull).map(item -> {
                    SuggestedKeywordRpcVo.Builder voBuilder = SuggestedKeywordRpcVo.newBuilder();
                    if (item.getKeywordId() != null) {
                        voBuilder.setKeywordId(item.getKeywordId());
                    }
                    if (item.getKeywordText() != null) {
                        voBuilder.setKeywordText(item.getKeywordText());
                    }
                    if (item.getKeywordTextCn() != null) {
                        voBuilder.setKeywordTextCn(item.getKeywordTextCn());
                    }
                    if (item.getMatchType() != null) {
                        voBuilder.setMatchType(item.getMatchType());
                    }
                    if (item.getSuggested() != null) {
                        voBuilder.setSuggested(item.getSuggested());
                    }
                    if (item.getRangeStart() != null) {
                        voBuilder.setRangeStart(item.getRangeStart());
                    }
                    if (item.getRangeEnd() != null) {
                        voBuilder.setRangeEnd(item.getRangeEnd());
                    }
                    if (item.getRangeEnd() != null) {
                        voBuilder.setRangeEnd(item.getRangeEnd());
                    }
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 根据关键词文本取建议竞价
     * 每次指定的关键词数量不能超过10个
     */
    @Override
    public void suggestedBidByText(SpSuggestedBidByTextRequest request, StreamObserver<SuggestedBidByTextResponse> responseObserver) {
        log.info("sp-keyword-根据关键词文本取建议竞价 request {}", request);
        SuggestedBidByTextResponse.Builder builder = SuggestedBidByTextResponse.newBuilder();

        if (StringUtils.isBlank(request.getGroupId())
                || !request.hasShopId()
                || CollectionUtils.isEmpty(request.getKeywordList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            AddKeywordsVo addKeywordsVo = new AddKeywordsVo();
            addKeywordsVo.setPuid(request.getPuid().getValue());
            addKeywordsVo.setShopId(request.getShopId().getValue());
            addKeywordsVo.setUid(request.getUid().getValue());
            addKeywordsVo.setGroupId(request.getGroupId());
            //处理list
            List<KeywordsRpcVo> keywordList = request.getKeywordList();
            List<KeywordsVo> keywordsVoList = keywordList.stream().filter(Objects::nonNull).map(item -> {
                KeywordsVo keywordsVo = new KeywordsVo();
                keywordsVo.setMatchType(item.getMatchType());
                keywordsVo.setRangeEnd(item.getRangeEnd());
                keywordsVo.setRangeStart(item.getRangeStart());
                keywordsVo.setSuggested(item.getSuggested());
                keywordsVo.setBid(item.getBid());
                keywordsVo.setKeywordText(item.getKeywordText());
                return keywordsVo;
            }).collect(Collectors.toList());

            addKeywordsVo.setKeywords(keywordsVoList);
            //处理业务返回结果
            Result<List<SuggestedKeywordVo>> res = cpcKeywordsService.getSuggestedBidByText(addKeywordsVo.getPuid(), addKeywordsVo);
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
            if (res.success()) {
                //处理返回list
                List<SuggestedKeywordVo> data = res.getData();
                List<SuggestedKeywordRpcVo> rpcVo = data.stream().filter(Objects::nonNull).map(item -> {
                    SuggestedKeywordRpcVo.Builder suVo = SuggestedKeywordRpcVo.newBuilder();
                    if (item.getKeywordId() != null) {
                        suVo.setKeywordId(item.getKeywordId());
                    }
                    if (item.getKeywordText() != null) {
                        suVo.setKeywordText(item.getKeywordText());
                    }
                    if (item.getMatchType() != null) {
                        suVo.setMatchType(item.getMatchType());
                    }
                    if (item.getSuggested() != null) {
                        suVo.setSuggested(item.getSuggested());
                    }
                    if (item.getRangeStart() != null) {
                        suVo.setRangeStart(item.getRangeStart());
                    }
                    if (item.getRangeEnd() != null) {
                        suVo.setRangeEnd(item.getRangeEnd());
                    }
                    return suVo.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVo);
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 建议竞价
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getSuggestedBid(SpSuggestedBidRequest request, StreamObserver<SuggestedBidResponse> responseObserver) {
        log.info("sp-keyword-建议竞价 request {}", request);
        SuggestedBidResponse.Builder builder = SuggestedBidResponse.newBuilder();

        if (!request.hasPuid() || !request.hasShopId() || StringUtils.isBlank(request.getKeywordIds())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result<List<SuggestedKeywordVo>> res = cpcKeywordsService.getSuggestedBid(request.getPuid().getValue(), request.getShopId().getValue()
                    , Lists.newArrayList(request.getKeywordIds().split(",")));
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
            //处理list
            if (res.success()) {
                List<SuggestedKeywordVo> data = res.getData();
                List<SuggestedKeywordRpcVo> rpcVo = data.stream().filter(Objects::nonNull).map(item -> {
                    SuggestedKeywordRpcVo.Builder suVo = SuggestedKeywordRpcVo.newBuilder();
                    if (item.getKeywordId() != null) {
                        suVo.setKeywordId(item.getKeywordId());
                    }
                    if (item.getKeywordText() != null) {
                        suVo.setKeywordText(item.getKeywordText());
                    }
                    if (item.getMatchType() != null) {
                        suVo.setMatchType(item.getMatchType());
                    }
                    if (item.getSuggested() != null) {
                        suVo.setSuggested(item.getSuggested());
                    }
                    if (item.getRangeStart() != null) {
                        suVo.setRangeStart(item.getRangeStart());
                    }
                    if (item.getRangeEnd() != null) {
                        suVo.setRangeEnd(item.getRangeEnd());
                    }
                    if (item.getEstimatedImpressionUpper() != null) {
                        suVo.setEstimatedImpressionUpper(item.getEstimatedImpressionUpper());
                    }
                    if (item.getEstimatedImpressionLower() != null) {
                        suVo.setEstimatedImpressionLower(item.getEstimatedImpressionLower());
                    }
                    return suVo.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVo);
            }

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void batchGetSuggestedBid(BatchSuggestedBidRequest request, StreamObserver<SuggestedBidResponse> responseObserver) {
        log.info("sp-keyword-建议竞价 request {}", request);
        SuggestedBidResponse.Builder builder = SuggestedBidResponse.newBuilder();

        if (!request.hasPuid() || !request.hasShopId() || CollectionUtils.isEmpty(request.getKeywordDetailList()) || request.getKeywordDetailList().size() > 3000) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            List<KeywordSuggestedBidVo> requestDetails = request.getKeywordDetailList();
            List<KeywordSuggestedBidDetail> details = new ArrayList<>();
            for (int i = 0; i < requestDetails.size(); i++) {
                KeywordSuggestedBidDetail keywordSuggestedBidDetail = new KeywordSuggestedBidDetail();
                BeanUtils.copyProperties(requestDetails.get(i), keywordSuggestedBidDetail);
                keywordSuggestedBidDetail.setIndex(i);
                details.add(keywordSuggestedBidDetail);
            }
            Result<List<SuggestedKeywordVo>> res = cpcKeywordsService.batchGetSuggestedBid(request.getPuid().getValue(),
                    request.getShopId().getValue(), details);
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
            //处理list
            if (res.success()) {
                List<SuggestedKeywordVo> data = res.getData();
                List<SuggestedKeywordRpcVo> rpcVo = data.stream().filter(Objects::nonNull).map(item -> {
                    SuggestedKeywordRpcVo.Builder suVo = SuggestedKeywordRpcVo.newBuilder();
                    if (item.getKeywordId() != null) {
                        suVo.setKeywordId(item.getKeywordId());
                    }
                    if (item.getKeywordText() != null) {
                        suVo.setKeywordText(item.getKeywordText());
                    }
                    if (item.getMatchType() != null) {
                        suVo.setMatchType(item.getMatchType());
                    }
                    if (item.getSuggested() != null) {
                        suVo.setSuggested(item.getSuggested());
                    }
                    if (item.getRangeStart() != null) {
                        suVo.setRangeStart(item.getRangeStart());
                    }
                    if (item.getRangeEnd() != null) {
                        suVo.setRangeEnd(item.getRangeEnd());
                    }
                    suVo.setIndex(item.getIndex());
                    return suVo.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVo);
            }

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 建议竞价(多店铺)
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getSuggestedBidMultiShop(SpSuggestedBidMultiShopRequest request, StreamObserver<SuggestedBidResponse> responseObserver) {
        log.info("sp-keyword-多店铺建议竞价 request {}", request);
        SuggestedBidResponse.Builder builder = SuggestedBidResponse.newBuilder();

        if (!request.hasPuid() || request.getKeywordCount() <= 0
                || !request.getKeywordList().stream().allMatch(SpSuggestedBidMultiShop::hasKeywordId)
                || !request.getKeywordList().stream().allMatch(SpSuggestedBidMultiShop::hasShopId)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            String type = request.getKeywordList().get(0).getType();
            List<KeywordSuggestBidBatchQo> keywordList = request.getKeywordList().stream().map(k -> {
                KeywordSuggestBidBatchQo suggestedBid = new KeywordSuggestBidBatchQo();
                suggestedBid.setShopId(k.getShopId().getValue());
                suggestedBid.setKeywordId(k.getKeywordId());
                return suggestedBid;
            }).collect(Collectors.toList());
            //处理业务返回结果
            Result<List<SuggestedKeywordVo>> res;
            if (Constants.SB.equalsIgnoreCase(type)) {
                res = cpcSbKeywordService.getSuggestedBidMultiShop(request.getPuid().getValue(), keywordList);
            } else {
                res = cpcKeywordsService.getSuggestedBidMultiShop(request.getPuid().getValue(), keywordList);
            }
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
            //处理list
            if (res.success()) {
                List<SuggestedKeywordVo> data = res.getData();
                List<SuggestedKeywordRpcVo> rpcVo = data.stream().filter(Objects::nonNull).map(item -> {
                    SuggestedKeywordRpcVo.Builder suVo = SuggestedKeywordRpcVo.newBuilder();
                    if (item.getKeywordId() != null) {
                        suVo.setKeywordId(item.getKeywordId());
                    }
                    if (item.getKeywordText() != null) {
                        suVo.setKeywordText(item.getKeywordText());
                        //SB主题广告转换为中文返回
                        if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase(item.getKeywordText())) {
                            suVo.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
                        } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase(item.getKeywordText())) {
                            suVo.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
                        }
                    }
                    if (item.getMatchType() != null) {
                        suVo.setMatchType(item.getMatchType());
                    }
                    if (item.getSuggested() != null) {
                        suVo.setSuggested(item.getSuggested());
                    }
                    if (item.getRangeStart() != null) {
                        suVo.setRangeStart(item.getRangeStart());
                    }
                    if (item.getRangeEnd() != null) {
                        suVo.setRangeEnd(item.getRangeEnd());
                    }
                    if (item.getEstimatedImpressionUpper() != null) {
                        suVo.setEstimatedImpressionUpper(item.getEstimatedImpressionUpper());
                    }
                    if (item.getEstimatedImpressionLower() != null) {
                        suVo.setEstimatedImpressionLower(item.getEstimatedImpressionLower());
                    }
                    return suVo.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVo);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 更新状态
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateState(UpdateSpKeywordStateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-keyword-更新状态 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        String stateValue = StateEnum.getStateValue(request.getState());
        if (!request.hasId() || !request.hasPuid() ||
                !request.hasUid() || StringUtils.isBlank(stateValue)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result res = cpcKeywordsService.updateState(request.getPuid().getValue(), request.getUid().getValue(),
                    request.getId().getValue(), request.getState(), request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 更新竞价
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateBid(updateSpKeywordBidRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-keyword-更新竞价 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasId() || !request.hasPuid() ||
                !request.hasUid() || !request.hasBid() || request.getBid().getValue() <= 0) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务
            Result res = cpcKeywordsService.updateBid(request.getPuid().getValue(), request.getUid().getValue(),
                    request.getId().getValue(), request.getBid().getValue(), request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 归档
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void archive(ArchiveSpKeywordRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-keyword-归档 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasId() || !request.hasPuid() ||
                !request.hasUid()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result res = cpcKeywordsService.archive(request.getPuid().getValue(), request.getUid().getValue(), request.getId().getValue(), request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 批量更新
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateBatch(BatchUpdateSpKeywordRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-keyword-更新竞价 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        List<SpKeywordVo> vosList = request.getVosList();
        if (!request.hasPuid() ||
                !request.hasUid() || !request.hasShopId() || CollectionUtils.isEmpty(vosList) || !request.hasType()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务
            List<SpKeywordsVo> collect = vosList.stream().map(e -> {
                SpKeywordsVo vo = new SpKeywordsVo();
                vo.setPuid(request.getPuid().getValue());
                vo.setUid(request.getUid().getValue());
                vo.setShopId(request.getShopId().getValue());
                vo.setState(e.getState());
                if (e.hasId()) {
                    vo.setDxmKeywordId(e.getId().getValue());
                }
                if (e.hasBid()) {
                    vo.setBid(e.getBid().getValue());
                }
                return vo;
            }).collect(Collectors.toList());


            Result res = cpcKeywordsService.updateBatch(request.getPuid().getValue(), request.getShopId().getValue(), request.getUid().getValue(),
                    collect, request.getType(), request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getData() != null) {
                builder.setData(JSONUtil.objectToJson(res.getData()));
            }
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 批量更新（多店铺）
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateBatchMultiShop(BatchUpdateMultiShopSpKeywordRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-keyword-更新竞价 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        List<MultiShopSpKeywordVo> vosList = request.getVosList();
        if (!request.hasPuid() || !request.hasUid() || CollectionUtils.isEmpty(vosList) || !request.hasType() || !vosList.stream().allMatch(MultiShopSpKeywordVo::hasShopId)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务
            String adType = vosList.get(0).getAdType();
            Result res;
            if (Constants.SB.equalsIgnoreCase(adType)) {
                List<SbUpdateKeywordsVo> collect = vosList.stream().map(e -> {
                    SbUpdateKeywordsVo sbUpdateKeywordsVo = new SbUpdateKeywordsVo();
                    sbUpdateKeywordsVo.setPuid(request.getPuid().getValue());
                    sbUpdateKeywordsVo.setShopId(e.getShopId().getValue());
                    sbUpdateKeywordsVo.setUid(request.getUid().getValue());
                    if (e.hasId()) {
                        sbUpdateKeywordsVo.setId(e.getId().getValue());
                    }
                    if (e.hasState()) {
                        sbUpdateKeywordsVo.setState(e.getState());
                    }
                    if (e.hasBid()) {
                        sbUpdateKeywordsVo.setBid(e.getBid().getValue());
                    }
                    return sbUpdateKeywordsVo;
                }).collect(Collectors.toList());
                res = cpcSbKeywordService.updateBatchMultiShop(request.getPuid().getValue(), request.getUid().getValue(),
                        collect, request.getType(), request.getLoginIp());
            } else {
                List<SpKeywordsVo> collect = vosList.stream().map(e -> {
                    SpKeywordsVo vo = new SpKeywordsVo();
                    vo.setPuid(request.getPuid().getValue());
                    vo.setUid(request.getUid().getValue());
                    vo.setShopId(e.getShopId().getValue());
                    vo.setState(e.getState());
                    if (e.hasId()) {
                        vo.setDxmKeywordId(e.getId().getValue());
                    }
                    if (e.hasBid()) {
                        vo.setBid(e.getBid().getValue());
                    }
                    return vo;
                }).collect(Collectors.toList());
                res = cpcKeywordsService.updateBatchMultiShop(request.getPuid().getValue(), request.getUid().getValue(),
                        collect, request.getType(), request.getLoginIp());
            }
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getData() != null) {
                builder.setData(JSONUtil.objectToJson(res.getData()));
            }
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getKeywordList(GetKeywordListRequest request, StreamObserver<GetKeywordListResponse> responseObserver) {
        GetKeywordListResponse.Builder builder = GetKeywordListResponse.newBuilder();
        if (!request.hasPuid() || request.getShopIdCount() < 1 || request.getKeywordIdsCount() < 1) {
            builder.setCode(Int32Value.of(-1));
            responseObserver.onNext(builder.build());
            return;
        }
        List<KeywordVo> listKeyword = cpcKeywordsService.getListKeyword(request.getPuid().getValue(), request.getShopIdList().stream().map(Int32Value::getValue).collect(Collectors.toList()), request.getKeywordIdsList());

        builder.setCode(Int32Value.of(0));
        if (CollectionUtils.isEmpty(listKeyword)) {
            builder.addAllData(listKeyword);
        }
        responseObserver.onNext(builder.build());
    }

    /**
     * 产品投放-分页获取建议分类
     *
     * @param request          request
     * @param responseObserver responseObserver
     */
    @Override
    public void pageSuggestCategoriesByAsin(PageSuggestCategoriesByAsinRequest request, StreamObserver<PageSuggestCategoriesByAsinResponse> responseObserver) {
        log.info("pageSuggestCategoriesByAsin req:{}", JSON.toJSONString(request.getAllFields()));
        PageSuggestCategoriesByAsinResponse.Builder respBuilder = PageSuggestCategoriesByAsinResponse.newBuilder();
        //1，参数校验
        if (!request.hasPuid() || !request.hasShopId() || !request.hasPageNo() || !request.hasPageSize() || CollectionUtils.isEmpty(request.getAsinListList())) {
            respBuilder.setCode(Result.ERROR);
            respBuilder.setMsg("参数有误");
            responseObserver.onNext(respBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        //2，校验权限
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(request.getShopId(), request.getPuid());
        if (shop == null) {
            respBuilder.setCode(Result.ERROR);
            respBuilder.setMsg("没有CPC授权");
            responseObserver.onNext(respBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(request.getPuid(), request.getShopId());
        if (profile == null) {
            respBuilder.setCode(Result.ERROR);
            respBuilder.setMsg("没有站点对应的配置信息");
            responseObserver.onNext(respBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        //3，结果返回
        int pageNo = request.getPageNo();
        int pageSize = request.getPageSize();
        ProtocolStringList asinListList = request.getAsinListList();
        List<String> asinList = new ArrayList<>(asinListList.size());
        asinList.addAll(asinListList);
        PageSuggestCategoriesByAsinInfo response = spSuggestService.pageSuggestCategoriesByAsin(pageNo, pageSize, asinList, shop, profile);
        respBuilder.setCode(Result.SUCCESS);
        respBuilder.setData(response);
        responseObserver.onNext(respBuilder.build());
        responseObserver.onCompleted();
    }

    /**
     * 关键词投放-分页获取建议关键词
     *
     * @param request          request
     * @param responseObserver responseObserver
     */
    @Override
    public void pageSuggestKeywordsByAsin(PageSuggestKeywordsByAsinRequest request, StreamObserver<PageSuggestKeywordsByAsinResponse> responseObserver) {
        log.info("pageSuggestKeywordsByAsin req:{}", JSON.toJSONString(request.getAllFields()));
        PageSuggestKeywordsByAsinResponse.Builder respBuilder = PageSuggestKeywordsByAsinResponse.newBuilder();
        //1，参数校验
        if (!request.hasPuid() || !request.hasShopId() || !request.hasPageNo() || !request.hasPageSize() || CollectionUtils.isEmpty(request.getAsinListList())) {
            respBuilder.setCode(Result.ERROR);
            respBuilder.setMsg("参数有误");
            responseObserver.onNext(respBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        int pageNo = request.getPageNo();
        int pageSize = request.getPageSize();
        ProtocolStringList asinListList = request.getAsinListList();
        List<String> asinList = new ArrayList<>(asinListList.size());
        asinList.addAll(asinListList);

        //2，校验权限
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(request.getShopId(), request.getPuid());
        if (shop == null) {
            respBuilder.setCode(Result.ERROR);
            respBuilder.setMsg("没有CPC授权");
            responseObserver.onNext(respBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(request.getPuid(), request.getShopId());
        if (profile == null) {
            respBuilder.setCode(Result.ERROR);
            respBuilder.setMsg("没有站点对应的配置信息");
            responseObserver.onNext(respBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        //3，结果返回
        PageSuggestKeywordsByAsinInfo response = spSuggestService.pageSuggestKeywordsByAsin(pageNo, pageSize, asinList, shop, profile, request.getSearchVal());
        respBuilder.setCode(Result.SUCCESS);
        respBuilder.setData(response);
        responseObserver.onNext(respBuilder.build());
        responseObserver.onCompleted();
    }

    /**
     * 投放-查询投放建议竞价
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getTargetBidRecommendations(GetTargetBidRecommendationsRequest request, StreamObserver<GetTargetBidRecommendationsResponse> responseObserver) {
        log.info("getTargetBidRecommendations puid:{}, shopId:{}, asins:{}, targetingExpression:{}, placementProductPage:{}, placementTop:{}, placementRestOfSearch:{}, biddingStrategy:{}, campaignId:{}, adGroupId:{}"
            , request.getPuid(), request.getShopId(), request.getAsinsList(), request.getTargetingExpressionList(), request.getPlacementProductPage(), request.getPlacementTop(), request.getPlacementRestOfSearch(), request.getBiddingStrategy(), request.getCampaignId(), request.getAdGroupId());
        GetTargetBidRecommendationsResponse.Builder respBuilder = GetTargetBidRecommendationsResponse.newBuilder();
        //1，参数校验
        boolean existedGroup = request.hasCampaignId() && request.hasAdGroupId();
        String errMsg = checkGetTargetBidRecommendationsParams(request, existedGroup);
        if (StringUtils.isNotBlank(errMsg)) {
            respBuilder.setCode(Result.ERROR);
            respBuilder.setMsg(errMsg);
            responseObserver.onNext(respBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        //2，校验权限
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(request.getShopId(), request.getPuid());
        if (shop == null) {
            respBuilder.setCode(Result.ERROR);
            respBuilder.setMsg("没有CPC授权");
            responseObserver.onNext(respBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(request.getPuid(), request.getShopId());
        if (profile == null) {
            respBuilder.setCode(Result.ERROR);
            respBuilder.setMsg("没有站点对应的配置信息");
            responseObserver.onNext(respBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        //3，结果返回
        ProtocolStringList asinListList = request.getAsinsList();
        List<String> asinList = new ArrayList<>(asinListList.size());
        asinList.addAll(asinListList);
        String biddingStrategy = request.getBiddingStrategy();
        int placementProductPage = request.hasPlacementProductPage() ? request.getPlacementProductPage() : -1;
        int placementTop = request.hasPlacementTop() ? request.getPlacementTop() : -1;
        int placementRestOfSearch = request.hasPlacementRestOfSearch() ? request.getPlacementRestOfSearch() : -1;
        String campaignId = request.hasCampaignId() ? request.getCampaignId() : null;
        String adGroupId = request.hasAdGroupId() ? request.getAdGroupId() : null;
        List<TargetingExpression> targetingExpressionList = request.getTargetingExpressionList();
        GetTargetBidRecommendationsInfo response = spSuggestService.getTargetBidRecommendations(asinList, placementProductPage, placementTop, placementRestOfSearch, shop, profile, biddingStrategy, campaignId, adGroupId, targetingExpressionList);
        respBuilder.setCode(Result.SUCCESS);
        respBuilder.setData(response);
        responseObserver.onNext(respBuilder.build());
        responseObserver.onCompleted();
    }

    private String checkGetTargetBidRecommendationsParams(GetTargetBidRecommendationsRequest request, boolean existedGroup) {
        if (!request.hasPuid() || !request.hasShopId() || CollectionUtils.isEmpty(request.getTargetingExpressionList())) {
            return "参数错误";
        }
        if (existedGroup) {
            if (!request.hasCampaignId() || !request.hasAdGroupId()) {
                return "参数错误：campaignId或adGroupId不能为空";
            }
            return null;
        }
        //新建广告组情况下
        if (CollectionUtils.isEmpty(request.getAsinsList()) || !request.hasBiddingStrategy()) {
            return "参数错误：asins或biddingStrategy不能为空";
        }
        return null;
    }

}
