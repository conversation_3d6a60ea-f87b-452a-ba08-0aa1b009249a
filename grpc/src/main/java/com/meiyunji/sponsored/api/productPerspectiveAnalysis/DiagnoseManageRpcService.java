package com.meiyunji.sponsored.api.productPerspectiveAnalysis;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.asinInfoQuery.AsinListResponseVo;
import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.asinInfoQuery.InitAsinInfoResponseVo;
import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.diagnoseManage.*;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.IDiagnoseManageService;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.*;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-09-06  18:41
 */
@GRpcService
@Slf4j
public class DiagnoseManageRpcService extends RpcDiagnoseManageServiceGrpc.RpcDiagnoseManageServiceImplBase {

    @Autowired
    private IDiagnoseManageService diagnoseManageService;

    @Override
    public void listDiagnose(ListDiagnoseRequest request, StreamObserver<ListDiagnoseResponse> responseObserver) {
        ListDiagnoseResponse.Builder builder = ListDiagnoseResponse.newBuilder();
        ListDiagnoseReqVo reqVo = buildRequest(request);
        Page<ListDiagnoseDto> page = diagnoseManageService.listDiagnose(reqVo);
        if (page == null) {
            builder.setCode(Result.ERROR);
            builder.setMsg("诊断优化查询失败");
        } else {
            ListDiagnoseResponseVo.Builder voBuilder = buildResponse(page);
            builder.setData(voBuilder.build());
            builder.setCode(Result.SUCCESS);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void saveDiagnose(SaveDiagnoseRequest request, StreamObserver<CommonDiagnoseResponse> responseObserver) {
        CommonDiagnoseResponse.Builder builder = CommonDiagnoseResponse.newBuilder();
        SaveDiagnoseReqVo reqVo = new SaveDiagnoseReqVo(request.getPuid(), request.getUid(), (byte) request.getModuleType(), request.getFilterField());
        boolean success = diagnoseManageService.saveDiagnose(reqVo);
        if (success) {
            builder.setCode(Result.SUCCESS);
        } else {
            builder.setCode(Result.ERROR);
            builder.setMsg("诊断优化保存失败");
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    @Override
    public void deleteDiagnose(DeleteDiagnoseRequest request, StreamObserver<CommonDiagnoseResponse> responseObserver) {
        CommonDiagnoseResponse.Builder builder = CommonDiagnoseResponse.newBuilder();
        DeleteDiagnoseReqVo reqVo = new DeleteDiagnoseReqVo(request.getId(), request.getPuid(), request.getUid());
        boolean success = diagnoseManageService.deleteDiagnose(reqVo);
        if (success) {
            builder.setCode(Result.SUCCESS);
        } else {
            builder.setCode(Result.ERROR);
            builder.setMsg("诊断优化删除失败");
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void positionDiagnose(PositionDiagnoseRequest request, StreamObserver<CommonDiagnoseResponse> responseObserver) {
        CommonDiagnoseResponse.Builder builder = CommonDiagnoseResponse.newBuilder();
        PosotionDiagnoseReqVo reqVo = new PosotionDiagnoseReqVo(request.getId(), request.getPuid(), request.getUid(), (byte) request.getModuleType(), (byte) request.getOperateType());
        boolean success = diagnoseManageService.posotionDiagnose(reqVo);
        if (success) {
            builder.setCode(Result.SUCCESS);
        } else {
            builder.setCode(Result.ERROR);
            builder.setMsg("定位失败");
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private ListDiagnoseReqVo buildRequest(ListDiagnoseRequest request) {
        ListDiagnoseReqVo reqVo = new ListDiagnoseReqVo();
        reqVo.setPageNo(request.getPageNo());
        reqVo.setPageSize(request.getPageSize());
        reqVo.setPuid(request.getPuid());
        reqVo.setUid(request.getUid());
        reqVo.setModuleType((byte) request.getModuleType());
        if (StringUtils.isNotBlank(request.getMarketplaceId())) {
            reqVo.setMarketplaceId(request.getMarketplaceId());
        }
        reqVo.setShopIdList(request.getShopIdList());
        reqVo.setSearchType(request.getSearchType());
        reqVo.setSearchValue(request.getSearchValue());
        if (StringUtils.isNotBlank(request.getStartDate())) {
            reqVo.setStartDate(request.getStartDate());
        }
        if (StringUtils.isNotBlank(request.getEndDate())) {
            reqVo.setEndDate(request.getEndDate());
        }
        reqVo.setType(request.getType());
        return reqVo;
    }

    private ListDiagnoseResponseVo.Builder buildResponse(Page<ListDiagnoseDto> page) {
        ListDiagnoseResponseVo.Builder voBuilder = ListDiagnoseResponseVo.newBuilder();
        voBuilder.setPageNo(page.getPageNo());
        voBuilder.setPageSize(page.getPageSize());
        voBuilder.setTotalPage(page.getTotalPage());
        voBuilder.setTotalSize(page.getTotalSize());
        if (!CollectionUtils.isEmpty(page.getRows())) {
            List<ListDiagnoseVo> collect = page.getRows().stream().map(x -> {
                ListDiagnoseVo.Builder vo = ListDiagnoseVo.newBuilder();
                vo.setId(x.getId());
                vo.setModuleType(x.getModuleType());
                vo.setFilterField(x.getFilterField());
                vo.setPositionStatus(x.getPositionStatus() == 1);
                vo.setFilterCount(x.getFilterCount());
                return vo.build();
            }).collect(Collectors.toList());
            voBuilder.addAllRows(collect);
        }
        return voBuilder;
    }

}
