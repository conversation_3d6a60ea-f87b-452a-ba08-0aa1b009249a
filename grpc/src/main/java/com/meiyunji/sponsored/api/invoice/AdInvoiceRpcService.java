package com.meiyunji.sponsored.api.invoice;


import com.amazon.advertising.invoices.CommonCurrencyAmount;
import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.amazon.sellerpartner.base.MarketplaceIdEnum;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.invoice.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdInvoiceDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdInvoiceSummaryDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdInvoice;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdInvoiceSummary;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdInvoiceService;
import com.meiyunji.sponsored.service.cpc.vo.InvoicePageParam;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @describe: 广告信用卡费用
 */
@GRpcService
@Slf4j
public class AdInvoiceRpcService extends RPCAdInvoiceServiceGrpc.RPCAdInvoiceServiceImplBase {

    @Autowired
    private IAmazonAdInvoiceSummaryDao summaryDao;
    @Autowired
    private IAmazonAdInvoiceDao invoiceDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdInvoiceService amazonAdInvoiceService;

    @Override
    public void pushInvoice(PushInvoiceRequest request, StreamObserver<PushInvoiceResponse> responseObserver) {
        PushInvoiceResponse.Builder builder = PushInvoiceResponse.newBuilder();

        if (!request.hasShopId() || !request.hasPuid() || StringUtils.isBlank(request.getStartDate()) || StringUtils.isBlank(request.getEndDate())) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {
            List<AmazonAdInvoice> invoiceList = invoiceDao.getListByDate(request.getPuid(), request.getShopId(), request.getStartDate(), request.getEndDate());

            List<AmazonAdInvoiceSummary> invoiceSummaryList = summaryDao.getListTask(request.getPuid(), request.getShopId(), request.getStartDate(), request.getEndDate());
            Map<String , AmazonAdInvoiceSummary> summaryMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(invoiceSummaryList)) {
                for (AmazonAdInvoiceSummary summary : invoiceSummaryList) {
                    if (!summaryMap.containsKey(summary.getInvoiceId())) {
                        summaryMap.put(summary.getInvoiceId(), summary);
                    }
                }

            }
            builder.setCode(Result.SUCCESS);
            if (CollectionUtils.isNotEmpty(invoiceList)) {
                List<CpcInvoiceTaskDtoRpcVo> rpcVos = invoiceList.stream().filter(Objects::nonNull).map(item -> {
                    CpcInvoiceTaskDtoRpcVo.Builder voBuilder = CpcInvoiceTaskDtoRpcVo.newBuilder();
                    if (item.getStatus()!= null) {
                        voBuilder.setStatus(item.getStatus());
                    }
                    if (item.getPaymentMethod() != null) {
                        voBuilder.setPaymentMethod(String.valueOf(item.getPaymentMethod()));
                    } else {
                        if (summaryMap.containsKey(item.getInvoiceId())) {
                            AmazonAdInvoiceSummary summary = summaryMap.get(item.getInvoiceId());
                            if (summary != null && summary.getPaymentMethod() != null) {
                                voBuilder.setPaymentMethod(String.valueOf(summary.getPaymentMethod()));
                            }
                        }
                    }
                    if (item.getAmountDue()!=null) {
                        List<CommonCurrencyAmount> amountList = JSONUtil.jsonToArray(item.getAmountDue(), CommonCurrencyAmount.class);
                        if (CollectionUtils.isNotEmpty(amountList)) {
                            CommonCurrencyAmount commonCurrencyAmount = amountList.get(0);
                            if (commonCurrencyAmount != null) {
                                CommonCurrencyAmountRpcVo.Builder vo = CommonCurrencyAmountRpcVo.newBuilder();
                                if (commonCurrencyAmount.getCurrencyCode() != null) {
                                    vo.setCurrencyCode(commonCurrencyAmount.getCurrencyCode());
                                }
                                if (commonCurrencyAmount.getAmount() != null) {
                                    vo.setAmount(commonCurrencyAmount.getAmount());
                                }
                                voBuilder.setAmountRpcVo(vo.build());
                            }
                        }
                    }
                    if (item.getTaxAmountDue() != null) {
                        List<CommonCurrencyAmount> taxAmountList = JSONUtil.jsonToArray(item.getTaxAmountDue(), CommonCurrencyAmount.class);
                        if (CollectionUtils.isNotEmpty(taxAmountList)) {
                            CommonCurrencyAmount amount = taxAmountList.get(0);
                            if (amount != null) {
                                CommonCurrencyAmountRpcVo.Builder vo = CommonCurrencyAmountRpcVo.newBuilder();
                                if (amount.getCurrencyCode() != null) {
                                    vo.setCurrencyCode(amount.getCurrencyCode());
                                }
                                if (amount.getAmount() != null) {
                                    vo.setAmount(amount.getAmount());
                                }
                                voBuilder.setTaxAmountRpcVo(vo.build());
                            }
                        }
                    }
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();


    }

    @Override
    public void invoicePage(InvoicePageRequest request, StreamObserver<InvoicePageResponse> responseObserver) {
        InvoicePageResponse.Builder builder = InvoicePageResponse.newBuilder();
        long s = System.currentTimeMillis();
        log.info("请求广告发票列表页参数：{}",request);
        if (request.getShopIdsCount() < 1 || !request.hasPuid()
                || ((StringUtils.isBlank(request.getStartDate()) || StringUtils.isBlank(request.getEndDate())) && StringUtils.isBlank(request.getUpdateTime()))) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {
            InvoicePageParam param = new InvoicePageParam();
            param.setPageNo(request.getPageNum());
            param.setPageSize(request.getPageSize());
            param.setDateType(request.getDateType());
            if(StringUtils.isNotBlank(request.getStartDate())){
                param.setStartDate(request.getStartDate().replace("-", ""));

            }
            if(StringUtils.isNotBlank(request.getEndDate())){
                param.setEndDate(request.getEndDate().replace("-", ""));
            }
            param.setOrderField(request.getOrderField());
            param.setOrderType(request.getOrderType());
            param.setPuid(request.getPuid());
            param.setShopIds(request.getShopIdsList());
            param.setPaymentMethod(request.getPaymentMethodList());
            param.setMarketplaceIds(request.getMarketplaceIdsList());
            param.setStatus(request.getStatusList());
            param.setSearchValue(request.getSearchValue());
            param.setSearchType(request.getSearchType());
            param.setUpdateTime(request.getUpdateTime());
            Page<AmazonAdInvoiceSummary> pageList = summaryDao.getPageList(param.getPuid(), param, false);

            builder.setCode(Result.SUCCESS);
            InvoicePageResponse.InvoicePageData.Builder dataBuilder = InvoicePageResponse.InvoicePageData.newBuilder();
            if (pageList != null) {
                dataBuilder.setTotalPage(pageList.getTotalPage());
                dataBuilder.setTotalSize(pageList.getTotalSize());

                if (CollectionUtils.isNotEmpty(pageList.getRows())) {
                    List<Integer> shopIds = pageList.getRows().stream().map(AmazonAdInvoiceSummary::getShopId).collect(Collectors.toList());
                    List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(param.getPuid(), shopIds);
                    Map<Integer, ShopAuth> shopMap = shopAuths.stream().collect(Collectors.toMap(ShopAuth::getId, e -> e));
                    List<InvoicePageRowData> collect = pageList.getRows().stream().map(e -> convertPageRow(e, shopMap.get(e.getShopId()))).collect(Collectors.toList());
                    dataBuilder.addAllRows(collect);
                }
            }

            builder.setData(dataBuilder.build());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
        log.info("获取广告发票, puid ：{}, 总耗时 ：{} ", request.getPuid(), System.currentTimeMillis() - s);
    }

    @Override
    public void invoiceDetail(InvoiceDetailRequest request, StreamObserver<InvoiceDetailResponse> responseObserver) {

        InvoiceDetailResponse.Builder builder = InvoiceDetailResponse.newBuilder();
        long s = System.currentTimeMillis();
        log.info("请求广告发票详情参数：{}",request);
        if (!request.hasPuid() || !request.hasShopId() || StringUtils.isBlank(request.getInvoiceId()) ) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {


            builder.setCode(Result.SUCCESS);
            InvoiceDetailData invoiceDetailDataByInvoiceId = amazonAdInvoiceService.getInvoiceDetailDataByInvoiceId(request.getPuid(), request.getShopId(), request.getInvoiceId());
            if (invoiceDetailDataByInvoiceId != null) {
                builder.setData(invoiceDetailDataByInvoiceId);
            } else {
                builder.setCode(4004);
                builder.setMsg("未找到详情数据");
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
        log.info("获取广告发票详情, puid ：{}， shopId:{}, invoiceId:{} 总耗时 ：{} ", request.getPuid(), request.getShopId(), request.getInvoiceId(), System.currentTimeMillis() - s);
    }


    @Override
    public void summaryInvoice(InvoicePageRequest request, StreamObserver<SummaryInvoiceResponse> responseObserver) {
        SummaryInvoiceResponse.Builder builder = SummaryInvoiceResponse.newBuilder();
        log.info("请求广告发票汇总参数：{}",request);
        if (request.getShopIdsCount() < 1 || !request.hasPuid() || StringUtils.isBlank(request.getStartDate()) || StringUtils.isBlank(request.getEndDate())) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {
            InvoicePageParam param = new InvoicePageParam();
            param.setPageNo(request.getPageNum());
            param.setPageSize(request.getPageSize());
            param.setDateType(request.getDateType());
            if(StringUtils.isNotBlank(request.getStartDate())){
                param.setStartDate(request.getStartDate().replace("-", ""));

            }
            if(StringUtils.isNotBlank(request.getEndDate())){
                param.setEndDate(request.getEndDate().replace("-", ""));
            }
            param.setOrderField(request.getOrderField());
            param.setOrderType(request.getOrderType());
            param.setPuid(request.getPuid());
            param.setShopIds(request.getShopIdsList());
            param.setPaymentMethod(request.getPaymentMethodList());
            param.setMarketplaceIds(request.getMarketplaceIdsList());
            param.setStatus(request.getStatusList());
            param.setSearchValue(request.getSearchValue());
            param.setSearchType(request.getSearchType());
            SummaryInvoiceData summaryInvoice = null;
            try {
                summaryInvoice = amazonAdInvoiceService.getSummaryInvoice(param.getPuid(), param);
            } catch (Exception e){
                log.error("获取广告发票汇总异常",e);
            }


            builder.setCode(Result.SUCCESS);
            InvoicePageResponse.InvoicePageData.Builder dataBuilder = InvoicePageResponse.InvoicePageData.newBuilder();
            if (summaryInvoice != null) {
                builder.setData(summaryInvoice);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    private InvoicePageRowData  convertPageRow(AmazonAdInvoiceSummary invoiceSummary,ShopAuth shopAuth){
        InvoicePageRowData.Builder rowData = InvoicePageRowData.newBuilder();
        if(StringUtils.isNotBlank(invoiceSummary.getFromDate())){
            rowData.setFromDate(DateUtil.toFormatDate(invoiceSummary.getFromDate()));
        }
        if(StringUtils.isNotBlank(invoiceSummary.getInvoiceDate())){
            rowData.setInvoiceDate(DateUtil.toFormatDate(invoiceSummary.getInvoiceDate()));
        }
        rowData.setInvoiceAmount(invoiceSummary.getInvoiceAmount() == null ? "0.00":invoiceSummary.getInvoiceAmount().setScale(2, RoundingMode.HALF_UP).toString());
        rowData.setInvoiceId(invoiceSummary.getInvoiceId());
        if(invoiceSummary.getPaymentMethod() != null){
            rowData.setPaymentMethod(invoiceSummary.getPaymentMethod());
        }
        if(invoiceSummary.getMarketplaceId() != null){
            AmznEndpoint byMarketplaceId = AmznEndpoint.getByMarketplaceId(invoiceSummary.getMarketplaceId());
            rowData.setMarketplaceId(invoiceSummary.getMarketplaceId());
            if(byMarketplaceId != null){
                rowData.setMarketplaceName(byMarketplaceId.getMarketplaceCN());
                rowData.setCurrencyCode(byMarketplaceId.getCurrencyCode().value());
            }


        }
        rowData.setShopId(invoiceSummary.getShopId());
        if(shopAuth != null && shopAuth.getName() != null){
            rowData.setShopName(shopAuth.getName());
        }
        if(invoiceSummary.getStatus() != null){
            rowData.setStatus(invoiceSummary.getStatus());
        }
        if (invoiceSummary.getUpdateTime() != null) {
            rowData.setUpdateTime(DateUtil.getDateTime(invoiceSummary.getUpdateTime()));
        }
        if(StringUtils.isNotBlank(invoiceSummary.getToDate())){
            rowData.setToDate(DateUtil.toFormatDate(invoiceSummary.getToDate()));
        }

        return rowData.build();
    }

    @Override
    public void invoiceDetailPage(InvoicePageRequest request, StreamObserver<InvoiceDetailPageResponse> responseObserver) {
        InvoiceDetailPageResponse.Builder builder = InvoiceDetailPageResponse.newBuilder();
        log.info("请求广告发票列表页参数：{}",request);
        if (request.getShopIdsCount() < 1 || !request.hasPuid()
                || ((StringUtils.isBlank(request.getStartDate()) || StringUtils.isBlank(request.getEndDate())) && StringUtils.isBlank(request.getUpdateTime()))) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {
            InvoicePageParam param = new InvoicePageParam();
            param.setPageNo(request.getPageNum());
            param.setPageSize(request.getPageSize());
            param.setDateType(request.getDateType());
            if(StringUtils.isNotBlank(request.getStartDate())){
                param.setStartDate(request.getStartDate().replace("-", ""));

            }
            if(StringUtils.isNotBlank(request.getEndDate())){
                param.setEndDate(request.getEndDate().replace("-", ""));
            }
            param.setOrderField(request.getOrderField());
            param.setOrderType(request.getOrderType());
            param.setPuid(request.getPuid());
            param.setShopIds(request.getShopIdsList());
            param.setPaymentMethod(request.getPaymentMethodList());
            param.setMarketplaceIds(request.getMarketplaceIdsList());
            param.setStatus(request.getStatusList());
            param.setSearchValue(request.getSearchValue());
            param.setSearchType(request.getSearchType());
            param.setUpdateTime(request.getUpdateTime());

            builder.setCode(Result.SUCCESS);
            InvoiceDetailPageResponse.InvoicePageData invoiceDetailPage = amazonAdInvoiceService.getInvoiceDetailPage(request.getPuid(), param, true);
            if(invoiceDetailPage != null){
                builder.setData(invoiceDetailPage);
            }

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void invoiceDetailListByInvoiceIds(InvoiceDetailListRequest request, StreamObserver<InvoiceDetailListResponse> responseObserver) {
        InvoiceDetailListResponse.Builder builder = InvoiceDetailListResponse.newBuilder();
        log.info("请求广告发票详情参数：{}",request);
        if (!request.hasPuid() || request.getShopIdCount() < 1 || request.getInvoiceIdsCount() < 1 ) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {


            builder.setCode(Result.SUCCESS);
            List<String> collect = new ArrayList<>(request.getInvoiceIdsList());
            List<InvoiceDetailData> invoiceDetailDataByInvoiceId = amazonAdInvoiceService.getListInvoiceDetailDataByInvoiceIds(request.getPuid(), request.getShopIdList(),collect );
            if (invoiceDetailDataByInvoiceId != null) {
                builder.addAllData(invoiceDetailDataByInvoiceId);
            } else {
                builder.setCode(4004);
                builder.setMsg("未找到详情数据");
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    @Override
    public void invoiceProductByInvoiceId(InvoiceProductPageRequest request, StreamObserver<InvoiceProductPageResponse> responseObserver) {
        log.info("请求广告发票产品参数：{}", request);
        long s = System.currentTimeMillis();
        InvoiceProductPageResponse.Builder builder = InvoiceProductPageResponse.newBuilder();

        if (!request.hasPuid() || !request.hasShopId() || StringUtils.isBlank(request.getInvoiceId())) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {
            builder.setCode(Result.SUCCESS);
            InvoiceProductPageResponse.InvoiceProductPageData invoiceDetailDataByInvoiceId = amazonAdInvoiceService.getInvoiceProductByInvoiceId(request.getPuid(),
                    request.getShopId(), request.getInvoiceId(), request.getCampaignIdsList());
            if (invoiceDetailDataByInvoiceId != null) {
                builder.setData(invoiceDetailDataByInvoiceId);
            } else {
                builder.setCode(4004);
                builder.setMsg("未找到详情数据");
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
        log.info("获取广告发票, puid ：{}， shopId:{}, invoiceId:{} 总耗时 ：{} ", request.getPuid(), request.getShopId(), request.getInvoiceId(), System.currentTimeMillis() - s);
    }


    @Override
    public void invoiceUpdateTimeByInvoiceId(InvoiceUpdateTimeRequest request, StreamObserver<InvoiceUpdateTimeResponse> responseObserver) {
        log.info("请求广告发票最大更新时间参数：{}",request);
        long s = System.currentTimeMillis();
        InvoiceUpdateTimeResponse.Builder builder = InvoiceUpdateTimeResponse.newBuilder();

        if (!request.hasPuid() || !request.hasShopId() || StringUtils.isBlank(request.getInvoiceId()) ) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {


            builder.setCode(Result.SUCCESS);
            String invoiceUpdateTimeByInvoiceId = amazonAdInvoiceService.getInvoiceUpdateTimeByInvoiceId(request.getPuid(), request.getShopId(), request.getInvoiceId());
            if (invoiceUpdateTimeByInvoiceId != null) {
                builder.setData(invoiceUpdateTimeByInvoiceId);
            } else {
                builder.setCode(4004);
                builder.setMsg("未找到详情数据");
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
        log.info("获取广告发票updateTime, puid ：{}， shopId:{}, invoiceId:{} 总耗时 ：{} ", request.getPuid(), request.getShopId(), request.getInvoiceId(), System.currentTimeMillis() - s);
    }

}
