package com.meiyunji.sponsored.common.filter;

import com.alibaba.druid.filter.FilterChain;
import com.alibaba.druid.filter.FilterEventAdapter;
import com.alibaba.druid.proxy.jdbc.ConnectionProxy;
import com.alibaba.druid.proxy.jdbc.PreparedStatementProxy;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.util.JdbcConstants;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.springjdbc.SqlTypeParser;
import com.meiyunji.sponsored.common.util.SqlConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * sql拦截器 sql安全
 */
@Slf4j
@Component
public class SfSqlFilter  extends FilterEventAdapter {


    @Autowired
    private SqlConfigUtil sqlConfigUtil;

    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    /**
     * 白名单sql
     */
//    private static final Set<String> WHITE_SQL = new HashSet<>(Lists.newArrayList("sql"));

    @Override
    public PreparedStatementProxy connection_prepareStatement(
            FilterChain chain, ConnectionProxy connection, String sql) throws SQLException {
        // 拦截 SQL
        if (sqlConfigUtil.getLogEnable()) {
            log.info("sql = {}", sql);
        }
        if (sqlConfigUtil.getStatEnable()) {
            this.interceptSql(sql, sqlConfigUtil.getThrowError());
        }
        // 检查sellerid字段
        if (sqlConfigUtil.getSellerIdCheckEnable()) {
            this.interceptSellerIdSql(sql, sqlConfigUtil.getThrowError());
        }
        return super.connection_prepareStatement(chain, connection, sql);
    }

    private void interceptSql(String sql, boolean throwError) {
        try {
            // 白名单sql
            if (dynamicRefreshNacosConfiguration.getDruidFilterWhiteSqls().contains(sql)) {
                return;
            }
            // 1. 解析 SQL
            List<SQLStatement> sqlStatements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
            if (CollectionUtils.isEmpty(sqlStatements)) {
                return;
            }
            // 只取第一个
            SQLStatement statement = sqlStatements.get(0);
            // 只解析 update或者delete语句
            if (!SqlTypeParser.isUpdateOrDeleteSql(statement)) {
                return;
            }
            log.info("interceptSql = {}", sql);
            boolean bool = SqlTypeParser.containsFieldInWhere(statement, "puid");
            if (!bool) {
                BizServiceException bizServiceException = new BizServiceException("SQL异常非法!");
                log.error("sql不存在puid字段 sql = {}, error = {}", sql, ExceptionUtils.getStackTrace(bizServiceException));
                if (throwError) {
                    throw bizServiceException;
                }
            }
        } catch (Exception e) {
            // 解析失败时跳过（例如复杂 SQL）
            log.error("解析SQL错误 sql = {}, error = {}", sql, ExceptionUtils.getStackTrace(e));
            if (e instanceof BizServiceException) {
                if (throwError) {
                    throw new BizServiceException("SQL异常非法!");
                }
            }
        }
    }

    /**
     * 拦截针对特定表的查询，检查是否包含sellerid字段
     *
     * @param sql SQL语句
     * @param throwError 是否抛出异常
     */
    private void interceptSellerIdSql(String sql, boolean throwError) {
        try {
            // 白名单sql
            if (dynamicRefreshNacosConfiguration.getDruidFilterWhiteSqls().contains(sql)) {
                return;
            }

            // 获取需要检查sellerid的表名集合
            Set<String> targetTables = sqlConfigUtil.getSellerIdCheckTablesSet();
            if (targetTables.isEmpty()) {
                return;
            }

            // 1. 解析 SQL
            List<SQLStatement> sqlStatements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
            if (CollectionUtils.isEmpty(sqlStatements)) {
                return;
            }
            // 只取第一个
            SQLStatement statement = sqlStatements.get(0);

            // 只解析 SELECT语句
            if (!SqlTypeParser.isSelectSql(statement)) {
                return;
            }

            log.info("interceptSellerIdSql = {}", sql);

            // 检查是否涉及目标表且缺少sellerid字段
            boolean needsSellerIdField = SqlTypeParser.needsFieldInWhere(statement, targetTables, "seller_id");
            if (needsSellerIdField) {
                BizServiceException bizServiceException = new BizServiceException("SQL异常：查询特定表时必须包含sellerid条件!");
                log.error("sql查询特定表时不存在sellerid字段 sql = {}, targetTables = {}, error = {}",
                    sql, targetTables, ExceptionUtils.getStackTrace(bizServiceException));
                if (throwError) {
                    throw bizServiceException;
                }
            }
        } catch (Exception e) {
            // 解析失败时跳过（例如复杂 SQL）
            log.error("解析sellerid SQL错误 sql = {}, error = {}", sql, ExceptionUtils.getStackTrace(e));
            if (e instanceof BizServiceException) {
                if (throwError) {
                    throw new BizServiceException("SQL异常：查询特定表时必须包含sellerid条件!");
                }
            }
        }
    }
}
