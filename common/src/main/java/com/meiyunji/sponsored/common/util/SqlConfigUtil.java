package com.meiyunji.sponsored.common.util;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * sql相关配置
 */
@Component
@Data
@RefreshScope
public class SqlConfigUtil {

    /**
     * 是否打印sql日志(打印所有sql日志)
     */
    @Value(value = "${db.config.log.enable:false}")
    private Boolean logEnable;

    /**
     * 是否进行统计 update 和 delete 语句 (测试环境默认开启 生产环境关闭)
     */
    @Value(value = "${db.config.stat.enable:false}")
    private Boolean statEnable;

    /**
     * 是否抛出异常 (上一个为true 此配置才有用 决定是否抛出异常 中断程序)
     */
    @Value(value = "${db.config.stat.throwError:false}")
    private Boolean throwError;

    /**
     * 是否启用sellerid字段检查 (针对特定表的SELECT语句)
     */
    @Value(value = "${db.config.sellerid.check.enable:false}")
    private Boolean sellerIdCheckEnable;

    /**
     * 需要检查sellerid字段的表名列表 (逗号分隔)
     */
    @Value(value = "${db.config.sellerid.check.tables:}")
    private String sellerIdCheckTables;
}
