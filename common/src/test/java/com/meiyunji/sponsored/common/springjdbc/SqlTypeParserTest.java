package com.meiyunji.sponsored.common.springjdbc;

import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.util.JdbcConstants;
import org.junit.jupiter.api.Test;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SqlTypeParser 测试类
 */
public class SqlTypeParserTest {

    @Test
    public void testIsSelectStatement() {
        String selectSql = "SELECT * FROM users WHERE id = 1";
        assertTrue(SqlTypeParser.isSelectStatement(selectSql));

        String updateSql = "UPDATE users SET name = 'test' WHERE id = 1";
        assertFalse(SqlTypeParser.isSelectStatement(updateSql));
    }

    @Test
    public void testExtractTableNames() {
        String sql = "SELECT u.name, p.title FROM users u JOIN posts p ON u.id = p.user_id WHERE u.sellerid = 123";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(2, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("posts"));
    }

    @Test
    public void testContainsFieldInWhereForSelect() {
        String sqlWithSellerid = "SELECT * FROM users WHERE sellerid = 123";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sqlWithSellerid, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sellerid"));

        String sqlWithoutSellerid = "SELECT * FROM users WHERE name = 'test'";
        statements = SqlTypeParser.parseSql(sqlWithoutSellerid, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertFalse(SqlTypeParser.containsFieldInWhere(statements.get(0), "sellerid"));
    }

    @Test
    public void testNeedsFieldInWhere() {
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        targetTables.add("orders");

        // 测试涉及目标表但缺少sellerid字段的情况
        String sqlNeedsSellerid = "SELECT * FROM users WHERE name = 'test'";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sqlNeedsSellerid, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertTrue(SqlTypeParser.needsFieldInWhere(statements.get(0), targetTables, "sellerid"));

        // 测试涉及目标表且包含sellerid字段的情况
        String sqlHasSellerid = "SELECT * FROM users WHERE sellerid = 123";
        statements = SqlTypeParser.parseSql(sqlHasSellerid, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertFalse(SqlTypeParser.needsFieldInWhere(statements.get(0), targetTables, "sellerid"));

        // 测试不涉及目标表的情况
        String sqlOtherTable = "SELECT * FROM products WHERE name = 'test'";
        statements = SqlTypeParser.parseSql(sqlOtherTable, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertFalse(SqlTypeParser.needsFieldInWhere(statements.get(0), targetTables, "sellerid"));
    }

    @Test
    public void testContainsFieldInWhereWithAlias() {
        String sqlWithAlias = "SELECT u.name FROM users u WHERE u.sellerid = 123";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sqlWithAlias, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sellerid"));
    }

    @Test
    public void testComplexJoinQuery() {
        String complexSql = "SELECT u.name, o.total FROM users u " +
                "LEFT JOIN orders o ON u.id = o.user_id " +
                "WHERE u.sellerid = 123 AND o.status = 'active'";
        
        List<SQLStatement> statements = SqlTypeParser.parseSql(complexSql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(2, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("orders"));

        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sellerid"));
    }

    @Test
    public void testEmptyTargetTables() {
        String sql = "SELECT * FROM users WHERE name = 'test'";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        Set<String> emptyTables = new HashSet<>();
        assertFalse(SqlTypeParser.needsFieldInWhere(statements.get(0), emptyTables, "sellerid"));
        assertFalse(SqlTypeParser.needsFieldInWhere(statements.get(0), null, "sellerid"));
    }
}
