package com.meiyunji.sponsored.service.strategy.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AdvertiseStrategyTargetVo extends AdvertiseStrategySdTargetDetailVo {
    private Long id;
    private int puid;
    private int shopId;
    private String marketplaceId;
    private String adType;
    private String targetType;
    private String groupId;
    private String campaignId;
    private String groupName;
    private String campaignName;
    private String portfolioId;
    private String portfolioName;
    private BigDecimal biddingValue;
    private BigDecimal rangeStart;
    private BigDecimal rangeEnd;
    private String targetingValue;
    private String targetingState;
    private String targetId;
    private String matchType;
    private String matchName;
    private String adTargetType;
    private String servingStatus;
    private String servingStatusName;
    private String adGoal;
    private String costType;
    private String adFormat;
}
