package com.meiyunji.sponsored.service.autoRuleTask.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.autoRuleTask.dao.AutoRuleTaskDao;
import com.meiyunji.sponsored.service.autoRuleTask.po.AutoRuleTask;
import com.meiyunji.sponsored.service.autoRuleTask.vo.QueryTaskHourglassParam;
import com.meiyunji.sponsored.service.strategyTask.po.AdvertiseStrategyTask;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-08  10:15
 */
@Repository
public class AutoRuleTaskDaoImpl extends BaseShardingDaoImpl<AutoRuleTask> implements AutoRuleTaskDao {

    @Override
    public List<AutoRuleTask> queryListByTemplateId(QueryTaskHourglassParam param) {
        StringBuilder sql = new StringBuilder("select * from t_auto_rule_task where puid = ? and shop_id = ? and old_template_id = ? and create_time > DATE_SUB(CURDATE(), INTERVAL 6 DAY) ");
        List<Object> args = new ArrayList<>();
        args.add(param.getPuid());
        args.add(param.getShopId());
        args.add(param.getTemplateId());
        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" ORDER BY create_time desc");
        sql.append(whereSql);
        return getJdbcTemplate(param.getPuid()).query(sql.toString(), getRowMapper(), args.toArray());
    }

    @Override
    public List<AutoRuleTask> queryListByTemplateIdAsc(Integer puid, Integer shopId, Long templateId) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("old_template_id", templateId)
                .equalTo("state",0)
                .orderBy("create_time")
                .build();
        return listByCondition(puid,conditionBuilder);
    }

    @Override
    public Integer queryCountByTemplateId(int puid, int shopId, Long templateId, Integer taskAction) {
        StringBuilder sql = new StringBuilder("select count(t.id) from t_auto_rule_task t where t.puid =? and  t.old_template_id = ? and t.task_action = ? and state = 0 ");
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(templateId);
        args.add(taskAction);
        return getJdbcTemplate(puid).queryForObject(sql.toString(),Integer.class,args.toArray());
    }

    @Override
    public Integer queryCountByTemplateId(int puid, Long templateId) {
        StringBuilder sql = new StringBuilder("select count(t.id) from t_auto_rule_task t where t.puid =? and  t.old_template_id = ?  and state = 0 ");
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(templateId);
        return getJdbcTemplate(puid).queryForObject(sql.toString(),Integer.class,args.toArray());
    }

    @Override
    public int updateStateByPrimaryKey(int puid, AutoRuleTask record) {
        String sql = "update t_auto_rule_task set state=?  where puid = ? and id = ?";
        List<Object> argsList = new ArrayList<>();
        argsList.add(record.getState());
        argsList.add(puid);
        argsList.add(record.getId());
        return getJdbcTemplate(puid).update(sql,argsList.toArray());
    }

    @Override
    public int batchInsert(int puid, List<AutoRuleTask> list) {
        StringBuilder sql = new StringBuilder("insert into t_auto_rule_task (id,puid, shop_id," +
                "      marketplace_id, task_action," +
                "      `old_template_id`, `new_template_id`, item_type, count, " +
                "      state, `status`, `operation`, login_ip, " +
                "      `create_id`,update_id,create_time,update_time" +
                "      )" +
                "    values ");
        List<Object> argsList = Lists.newArrayList();
        for (AutoRuleTask autoRuleTask : list) {
            sql.append("(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, now(), now()),");
            argsList.add(autoRuleTask.getId());
            argsList.add(puid);
            argsList.add(autoRuleTask.getShopId());
            argsList.add(autoRuleTask.getMarketplaceId());
            argsList.add(autoRuleTask.getTaskAction());
            argsList.add(autoRuleTask.getOldTemplateId());
            argsList.add(autoRuleTask.getNewTemplateId());
            argsList.add(autoRuleTask.getItemType());
            argsList.add(autoRuleTask.getCount());
            argsList.add(autoRuleTask.getState());
            argsList.add(autoRuleTask.getStatus());
            argsList.add(autoRuleTask.getOperation());
            argsList.add(autoRuleTask.getLoginIp());
            argsList.add(autoRuleTask.getCreateId());
            argsList.add(autoRuleTask.getUpdateId());
        }
        sql.deleteCharAt(sql.length() - 1);
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<AutoRuleTask> queryListByTemplateIdList(Integer puid, Integer shopId, List<Long> templateIdList) {
        StringBuilder sql = new StringBuilder("select * from t_auto_rule_task where puid = ? and shop_id = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(shopId);
        if (CollectionUtils.isNotEmpty(templateIdList)) {
            sql.append(SqlStringUtil.dealInList("old_template_id", templateIdList, args));
        }
        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" ORDER BY update_time desc");
        sql.append(whereSql);
        return getJdbcTemplate(puid).query(sql.toString(), getRowMapper(), args.toArray());
    }

    @Override
    public List<AutoRuleTask> queryListByTemplateIdList(Integer puid, List<Integer> shopIdList, List<Long> templateIdList) {
        StringBuilder sql = new StringBuilder("select * from t_auto_rule_task where puid = ?  and state = 0");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));
        }
        if (CollectionUtils.isNotEmpty(templateIdList)) {
            sql.append(SqlStringUtil.dealInList("old_template_id", templateIdList, args));
        }
        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" ORDER BY update_time desc");
        sql.append(whereSql);
        return getJdbcTemplate(puid).query(sql.toString(), getRowMapper(), args.toArray());
    }

    @Override
    public List<AutoRuleTask> queryListByTemplateIdList(Integer puid, List<Integer> shopIdList, List<Long> templateIdList, List<Long> taskIdList) {
        StringBuilder sql = new StringBuilder("select * from t_auto_rule_task where puid = ?  and state = 0");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));
        }
        if (CollectionUtils.isNotEmpty(taskIdList)) {
            sql.append(SqlStringUtil.dealInList("id", taskIdList, args));
        }
        if (CollectionUtils.isNotEmpty(templateIdList)) {
            sql.append(SqlStringUtil.dealInList("old_template_id", templateIdList, args));
        }
        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" ORDER BY id");
        sql.append(whereSql);
        return getJdbcTemplate(puid).query(sql.toString(), getRowMapper(), args.toArray());
    }

    @Override
    public List<AutoRuleTask> queryListByTemplateIdList(Integer puid, Integer shopId, Long templateId, Long taskId) {
        StringBuilder sql = new StringBuilder("select * from t_auto_rule_task where puid = ?  and shop_id = ? and state in (0,-1)");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(shopId);
        if (taskId != null) {
            sql.append(" and id = ?");
            args.add(taskId);
        }
        if (templateId != null) {
            sql.append(" and old_template_id = ?");
            args.add(templateId);
        }
        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" ORDER BY id");
        sql.append(whereSql);
        return getJdbcTemplate(puid).query(sql.toString(), getRowMapper(), args.toArray());
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        if (shopId != null) {
            sql.append("and shop_id = ?");
            argsList.add(shopId);
        }
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }
}
