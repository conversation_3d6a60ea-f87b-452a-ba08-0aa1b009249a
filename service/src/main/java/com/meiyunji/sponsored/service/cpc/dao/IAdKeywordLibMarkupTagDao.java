package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.dto.AdTagRelationIdDTO;
import com.meiyunji.sponsored.service.cpc.po.AdKeywordLibMarkupTag;
import com.meiyunji.sponsored.service.cpc.vo.AdKeywordLibMarkupTagUidVo;
import com.meiyunji.sponsored.service.cpc.vo.AdKeywordLibMarkupTagVo;
import com.meiyunji.sponsored.service.cpc.vo.ClearAdTagVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * AmazonAdCampaign
 * <AUTHOR>
 */
public interface IAdKeywordLibMarkupTagDao extends IBaseShardingDao<AdKeywordLibMarkupTag> {


    void insertList(List<AdKeywordLibMarkupTag> list);

    List<String> getRelationIds(Integer puid, Integer uid, String type, List<Integer> tagIds);
    List<String> getRelationIdsByUid(Integer uid, String type, List<Integer> tagIds);

    List<AdTagRelationIdDTO> getRelationIdsAndTagIds(Integer puid, Integer uid, String type);


    int deleteByTagId(Integer puid, Integer uid, Long tagId);


//    int deleteByRelationId(Integer puid, Integer uid, String type, String adType, String targetType, List<String> relationId);

    int deleteAllByRelationId(Integer puid, Integer uid, String type, String adType, String targetType, List<String> relationId, List<Long> tagIdList);


    List<AdKeywordLibMarkupTagVo> getRelationVos(Integer puid, Integer uid, String type, String adType, String targetType, Long tagId, List<String> relationIds);

    List<AdKeywordLibMarkupTag> getMarkupTagByRelationId(Integer puid, Integer uid, String type, List<String> relationIds);
    List<AdKeywordLibMarkupTag> getMarkupTagByRelationIdAndCreateId(Integer puid, Integer uid, String type, List<String> relationIds);

    // 清除指定数据关联的指定标签
    int deleteByRelationIdAndAdTagId(Integer puid, ClearAdTagVo clearAdTagVo);

    List<AdKeywordLibMarkupTagUidVo> getCountByTagId(Integer puid, Integer uid, List<Integer> uidList);

    List<AdKeywordLibMarkupTagUidVo> getAllCountByTagId(Integer puid, Integer uid, List<Integer> uidList);

    int countKeywordByTagId(Integer puid, Integer uid, List<Integer> uidList, List<Long> adTagId);

    List<AdKeywordLibMarkupTagUidVo> getAllAsinCountByTagId(int puid, int uid, List<Integer> uidList);

    int countAsinByTagId(int puid, int uid, List<Integer> uidList, List<Long> tagIdList);
}