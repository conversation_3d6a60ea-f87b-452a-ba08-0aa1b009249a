package com.meiyunji.sponsored.service.stream.strgtegy;

import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbCampaignApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcTargetingApiService;
import com.meiyunji.sponsored.service.stream.enums.StreamConstants;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamTaskRetryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2024/1/17 15:29
 */
@Service
public class ManageStreamSpTargetStreamProcess extends AbstractManageStreamRetryProcessStrategy {


    @Resource
    private CpcTargetingApiService cpcTargetingApiService;

    @Resource
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    public ManageStreamSpTargetStreamProcess(IAmazonManagementStreamTaskRetryService amazonManagementStreamTaskRetryService, DynamicRefreshConfiguration dynamicRefreshConfiguration) {
        super(amazonManagementStreamTaskRetryService, dynamicRefreshConfiguration);
    }

    @Override
    public int getMaxCount() {
        return StreamConstants.SP_MAX_TARGET_IDS_COUNT;
    }

    @Override
    public void doExecute(ShopAuth shopAuth, String ids) {
        cpcTargetingApiService.syncTargetings(shopAuth, null, null, ids, null, true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
    }
}
