package com.meiyunji.sponsored.service.cpc.constants;

/**
 * @author: lijin
 * @email: <EMAIL>
 * @date: 2024-06-17 14:14:01
 */
public enum TaskExecutionRecordTaskType {
    SPQUERY("SpQuery", "商品推广请求"),
    SBQUERY("SbQuery", "品牌推广请求"),
    SPTARGETING("SpTargeting", "SpTargeting"),
    SPKEYWORD("SpKeyword", "商品广告关键词"),
    SBKEYWORD("SbKeyword", "品牌广告关键词"),
    SPANDSBKEYWORD("SpSbKeyword", "广告sp,sb关键词词组数量统计"),
    ADMANEGETAGINIT("AdManageTagInit", "广告管理标签初始化"),
    DELETE_INVALID_SHOP_DATA("deleteInvalidShopData", "删除无效店铺数据"),
    INIT_NEW_USER_TAG("initNewUserTag", "初始化新用户标签组权限");

    private String type;

    private String desc;

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    TaskExecutionRecordTaskType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
