package com.meiyunji.sponsored.service.kafka;

import lombok.Getter;
import org.springframework.kafka.core.KafkaTemplate;

/**
 * @author: wade
 * @date: 2022/1/13 10:02
 * @describe:
 */
public class DorisRoutineLoadProducer {

    @Getter
    private final String topic;

    //配置doris生产者和消费者
    private final KafkaTemplate<String, String> kafkaTemplate;

    public DorisRoutineLoadProducer(String topic, KafkaTemplate<String, String> kafkaTemplate) {
        this.topic = topic;
        this.kafkaTemplate = kafkaTemplate;
    }

    public void send(Object messageContent) throws Exception {
        kafkaTemplate.send(topic, messageContent.toString());
    }

}
