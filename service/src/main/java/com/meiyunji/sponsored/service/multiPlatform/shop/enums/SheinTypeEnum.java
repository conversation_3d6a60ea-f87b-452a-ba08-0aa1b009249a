package com.meiyunji.sponsored.service.multiPlatform.shop.enums;

public enum SheinTypeEnum {
    SELF("SELF", "自主运营"),
    HALF("HALF", "半托管"),
    AGENT("AGENT", "代运营");

    private String code;
    private String name;

    SheinTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static SheinTypeEnum getByCode(String code) {
        for (SheinTypeEnum value : SheinTypeEnum.values()) {
            if (value.code.equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

}
