package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingSphereDao;
import com.meiyunji.sponsored.rpc.syncReport.AdTypeEnum;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdProductPerspectiveBO;
import com.meiyunji.sponsored.service.cpc.bo.ReportMonitorBo;
import com.meiyunji.sponsored.service.cpc.dto.ProductReportParentAsinUpdateDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProductReport;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AsinOrderNumDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.*;
import com.meiyunji.sponsored.service.reportDiffMonitor.dto.ShopDTO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * AmazonAdProductReport
 * <AUTHOR>
 */

public interface IAmazonAdProductReportDao extends IBaseShardingSphereDao<AmazonAdProductReport> {
    /**
     * 批量插入数据
     * @param puid
     * @param list
     */
    void insertList(Integer puid, List<AmazonAdProductReport> list);

    void insertDorisList(Integer puid, List<AmazonAdProductReport> list);

    /**
     * @param puid
     * @param search
     * @param page
     * @return
     */
    Page getPageList(Integer puid, SearchVo search, Page page);


    /**
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param param
     * @return
     */
    AmazonAdProductReport getSumReport(Integer puid, Integer shopId, String marketplaceId, ReportParam param);

    List<AmazonAdProductReport> getSumReports(Integer puid, Integer shopId, String marketplaceId, ReportParam param);

    /**
     * 同步报告汇总
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @return
     */
    List<AmazonAdProductReport> getListReportByDate(Integer puid, Integer shopId, String marketplaceId, String startDate, String endDate);

    /**
     * 推送cpc任务数据
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param startDate
     * @param endDate
     * @return
     */
    List<AmazonAdProductReport> getTaskSumReport(Integer puid, Integer shopId, String marketplaceId, String startDate, String endDate);


    /**
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param param
     * @return
     */
    List<AmazonAdProductReport> getChartList(Integer puid, Integer shopId, String marketplaceId, ReportParam param);

    /**
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param param
     * @param page
     * @return
     */
    Page detailPageList(Integer puid, Integer shopId, String marketplaceId, ReportParam param, Page page);

    /**
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param tabType
     * @param tabId
     * @return
     */
    List<Map<String, Object>> getCampaignNames(int puid, Integer shopId, String marketplaceId, String tabType, String tabId);

    /**
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param itemId
     * @param tabType
     * @param tabId
     * @return
     */
    List<Map<String, Object>> getAdGroupNames(int puid, Integer shopId, String marketplaceId, String itemId, String tabType, String tabId);

    List<Map<String, Object>> getCampaignOrAdGroupNames(int puid, Integer shopId, String marketplaceId, String campaignId);

    /**
     * 按广告分组获取活动的汇总数据
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param startStr
     * @param endStr
     * @param adIds
     * @return
     */
    List<AmazonAdProductReport> listSumReports(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, List<String> adIds);

    /**
     * 获取指定广告的汇总数据
     * @param puid
     * @param shopId
     * @param startDate
     * @param endDate
     * @param adId
     * @return
     */
    List<AmazonAdProductReport> listReports(Integer puid, Integer shopId, String startDate, String endDate, String adId);


    List<AmazonAdProductReport> getListByDate(Integer puid, Integer shopId, String marketplaceId, String startTime, String endTime, String filed);

    List<AmazonAdProductReport> getProductListByParentAsinIsNull(Integer puid, Integer shopId, String marketplaceId, Date date);

    List<String> getSkuPageByDate(Integer puid, Integer shopId, String marketplaceId, long start, long limit, String dateStr);

    void batchUpdateParentAsin(Integer puid, Integer shopId, List<AmazonAdProductReport> newList);

    void batchUpdateParentAsin(Integer puid, Integer shopId, String marketplaceId, List<ProductReportParentAsinUpdateDto> updateList, String dateStr);

    List<AdHomePerformancedto> listSumReportByAdIds(Integer puid, Integer shopId, String startStr, String endStr, AdProductPageParam param, List<String> adIds);

    List<AdHomePerformancedto> getSpReportByDate(Integer puid, Integer shopId, String startStr, String endStr, AdProductPageParam param);

    List<AdHomePerformancedto> getSpReportByAdIdList(Integer puid, Integer shopId, String startStr, String endStr,List<String> adIdList);

    /**
     *
     * @param puid
     * @param shopId
     * @param date  更新数据前的时间
     * @return
     */
    List<String> getProductListByUpdateTime(Integer puid, Integer shopId, Date date);

    /**
     * 获取已购买ASIN页面的产品数据
     * @param param
     * @return
     */
    List<AdAsinProductDto> getAsinPageProductList(AdAsinPageParam param, List<String> asinList);

    /**
     * 获取已购买ASIN关联页面的产品数据
     * @param param
     * @return
     */
    AdAsinProductDto getAsinPageProduct(AdAsinPageParam param);

    /**
     * 取已购买ASIN页面的汇总数据
     * @param param
     * @param asinList
     * @return
     */
    List<AdAsinAggregateDto> getAsinAggregateDto(AdAsinPageParam param, List<String> asinList);


    AmazonAdProductReport getListByCampaignIdAndGroupId(Integer puid, Integer shopId, String campaignId, String groupId);

    /**
     * 汇总广告组数据
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param startDate
     * @param endDate
     * @return
     */
    List<AmazonAdProductReport> listSumReportsByAdGroup(Integer puid, Integer shopId, String marketplaceId, LocalDate startDate, LocalDate endDate);

    List<AmazonAdProductReport> getReportByAdId(Integer puid, Integer shopId, String startDate, String endDate, String marketplaceId, String adId);

    /**
     * 广告组合独立页获取订单量
     *
     * @param puid
     * @param shopId
     * @param startDate
     * @param endDate
     * @param marketplaceId
     * @param asins
     * @param campaignIds
     * @return
     */
    List<AmazonAdProductReport> getSales(Integer puid, Integer shopId, String startDate, String endDate,List<String> asins,List<String> campaignIds);

    /**
     * 查询近7天广告订单量最高的asin
     * @param reqVo
     * @return
     */
    List<AsinOrderNumDto> getTopOrderNumAsinRecentLast7Day(InitAsinInfoReqVo reqVo);

    /**
     * 根据店铺，站点查询asin近7天订单量数据
     * @param reqVo
     * @return
     */
    List<InitAsinInfoDto> getInitAsinInfoList(InitAsinInfoReqVo reqVo);

    /**
     * 商品透视分析-查询日期范围内有报告的asin
     * @param puid
     * @param shopIdList
     * @param marketPlaceId
     * @param asin
     * @return
     */
    List<AmazonAdProductPerspectiveBO> productPerspectiveBoListByAsin(Integer puid, List<Integer> shopIdList, String marketPlaceId, String asin, String startDate, String endDate);


    /**
     * 查询某时间内的一个asin
     * @param reqVo
     * @return
     */
    List<InitOneAsinVo> getOneByTime(InitAsinInfoReqVo reqVo);

    /**
     * 分页获取某个时间内的asin列表
     * @param reqVo
     * @return
     */
    Page<AsinListDto> getAsinPageByPuidAndShopId(AsinListReqVo reqVo);


    List<AmazonAdProductReport> getReportByAdIdsGroupByCountDate(Integer puid, Integer shopId, String startDate, String endDate, String marketplaceId, List<String> adIds);

    List<String> getAdIdsByProduct(int puid, int shopId, String marketplaceId, String start, List<String> adId);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

    /**
     * 查询某种类型广告在某个时间段内的广告组指标汇总
     *
     * @param shopList       shopList
     * @param adType         广告类型
     * @param startCountDate 开始时间
     * @param endCountDate   结束时间
     * @return 汇总指标
     */
    List<ReportMonitorBo> getReportLevelMonitorBoList(List<ShopDTO> shopList, AdTypeEnum adType, String startCountDate, String endCountDate);

    List<InvoiceProductDto> getInvoiceProductList(Integer puid, Integer shopId, String marketplaceId, List<String> campaignId, String start, String end);

    LocalDateTime getInvoiceMaxUpdateTime(Integer puid, Integer shopId, String marketplaceId, List<String> campaignId, String start, String end);
}