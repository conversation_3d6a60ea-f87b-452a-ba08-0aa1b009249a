package com.meiyunji.sponsored.service.wordFrequency.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingSphereDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.enums.MatchValueEnum;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordRootTopBo;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordRootTranslatorBo;
import com.meiyunji.sponsored.service.wordFrequency.dao.IWordRootKeywordSpDao;
import com.meiyunji.sponsored.service.wordFrequency.dto.WordFrequencyAdMetricDto;
import com.meiyunji.sponsored.service.wordFrequency.dto.WordRootTopDto;
import com.meiyunji.sponsored.service.wordFrequency.enums.WordRoot;
import com.meiyunji.sponsored.service.wordFrequency.po.WordRootKeywordSp;
import com.meiyunji.sponsored.service.wordFrequency.qo.GetWordRootAggregateDataQo;
import com.meiyunji.sponsored.service.wordFrequency.qo.GetWordRootDataQo;
import com.meiyunji.sponsored.service.wordFrequency.vo.GetWordRootAggregateDataVo;
import com.meiyunji.sponsored.service.wordFrequency.vo.GetWordRootDataVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author: zhulukun
 * @email: <EMAIL>
 * @date: 2023-11-06  14:24
 */
@Repository
public class WordRootKeywordSpDaoImpl extends BaseShardingSphereDaoImpl<WordRootKeywordSp> implements IWordRootKeywordSpDao {

    @Override
    public void batchInsertOrUpdate(Integer puid, List<WordRootKeywordSp> list) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_word_root_keyword_sp` (`puid`,`shop_id`,`query_id`,`word_frequency_type`,`keyword_text`,")
                .append("`word_root`,`keyword_id`, `query`, `create_at`,`update_at`) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for (WordRootKeywordSp wordRootKeywordSp : list) {
            sql.append(" (?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(wordRootKeywordSp.getShopId());
            argsList.add(wordRootKeywordSp.getQueryId());
            argsList.add(wordRootKeywordSp.getWordFrequencyType());
            argsList.add(wordRootKeywordSp.getKeywordText());
            argsList.add(wordRootKeywordSp.getWordRoot());
            argsList.add(wordRootKeywordSp.getKeywordId());
            argsList.add(wordRootKeywordSp.getQuery());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `puid`=values(puid),`shop_id`=values(shop_id),")
                .append("`query_id`=values(query_id),word_frequency_type=values(word_frequency_type)," +
                        "keyword_text=values(keyword_text),`word_root`=values(word_root),`keyword_id`=values(keyword_id),`query`=values(query)");
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<WordRootTopBo> listByQueryIdList(WordRootTopDto dto) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder keywordSb = new StringBuilder("select word_root, count(distinct query_id) count from `t_amazon_word_root_keyword_sp` ");
        this.getWhereSqlByQueryList(dto, keywordSb, argsList);

        StringBuilder targetSb = new StringBuilder("select word_root, count(distinct query_id) count from `t_amazon_word_root_targeting_sp` ");
        this.getWhereSqlByQueryList(dto, targetSb, argsList);

        StringBuilder sqlSb = new StringBuilder();
        sqlSb.append(" select word_root, sum(count) count from ( ")
                .append(keywordSb)
                .append(" UNION ALL ")
                .append(targetSb)
                .append(" ) c group by word_root ")
                .append(" order by count desc ");
        if (dto.getTop() != null) {
            sqlSb.append(" limit ").append(dto.getTop().toString());
        }

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(dto.getPuid(), hintManager).query(sqlSb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordRootTopBo.class));
        } finally {
            hintManager.close();
        }
    }

    @Override
    public Page<GetWordRootDataVo> pageList(GetWordRootDataQo qo) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSb = new StringBuilder("select wordRoot, wordRootCN, SUM(`frequency`) `frequency`, ")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(adCost) adCost,SUM(adOrderNum) adOrderNum,SUM(adSelfSaleNum) adSelfSaleNum,")
                .append("SUM(adSale) adSale, SUM(adSales) `adSales`,SUM(adSaleNum) `adSaleNum`, SUM(orderNum) orderNum")
                .append(" from ");
        StringBuilder countSqlSb = new StringBuilder("select count(*) from ( ");

        StringBuilder fromAfterSql = new StringBuilder(" ( ");
        StringBuilder keywordSqlSb = new StringBuilder("select word_root wordRoot, word_root_cn wordRootCN, count(distinct r.query_id) `frequency`, ")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) adCost,SUM(sale_num) adOrderNum,SUM(ad_order_num) adSelfSaleNum,")
                .append("SUM(total_sales) adSale, SUM(ad_sales) `adSales`,SUM(ad_sale_num) `adSaleNum`, SUM(order_num) orderNum")
                .append(" from t_amazon_word_root_keyword_sp w left join t_cpc_query_keyword_report r")
                .append(" on w.puid = r.puid and w.shop_id = r.shop_id and w.query_id = r.query_id ");
        this.getWhereSqlByKeywordPageList(qo, keywordSqlSb, argsList);
        fromAfterSql.append(keywordSqlSb);

        fromAfterSql.append(" UNION ALL ");

        StringBuilder targetSqlSb = new StringBuilder("select word_root wordRoot, word_root_cn wordRootCN, count(distinct r.query_id) `frequency`, ")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) adCost,SUM(sale_num) adOrderNum,SUM(ad_order_num) adSelfSaleNum,")
                .append("SUM(total_sales) adSale, SUM(ad_sales) `adSales`,SUM(ad_sale_num) `adSaleNum`, SUM(order_num) orderNum")
                .append(" from t_amazon_word_root_targeting_sp w left join t_cpc_query_targeting_report r")
                .append(" on w.puid = r.puid and w.shop_id = r.shop_id and w.query_id = r.query_id ");
        this.getWhereSqlByTargetPageList(qo, targetSqlSb, argsList);
        fromAfterSql.append(targetSqlSb);
        fromAfterSql.append(" ) c  group by wordRoot ");

        this.getHavingSqlByPageList(qo, fromAfterSql, argsList);

        sqlSb.append(fromAfterSql);
        countSqlSb.append(sqlSb).append(" ) s ");

        //排序
        this.getOrderByPageList(qo, sqlSb);

        Object[] array = argsList.toArray();
        return getPageResultByClass(qo.getPuid(), qo.getPageNo(), qo.getPageSize(), countSqlSb.toString(), array, sqlSb.toString(), array, GetWordRootDataVo.class);
    }

    @Override
    public WordFrequencyAdMetricDto getPageListAdMetricDto(GetWordRootDataQo qo) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSb = new StringBuilder("select SUM(frequency) sumFrequency, SUM(adCost) sumCost, SUM(adSale) sumAdSale, SUM(adOrderNum) sumAdOrderNum, SUM(orderNum) sumOrderNum from (");

        sqlSb.append("select wordRoot, SUM(`frequency`) `frequency`, ")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(adCost) adCost,SUM(adOrderNum) adOrderNum,SUM(adSelfSaleNum) adSelfSaleNum,")
                .append("SUM(adSale) adSale, SUM(adSales) `adSales`,SUM(adSaleNum) `adSaleNum`, SUM(orderNum) orderNum")
                .append(" from ");

        StringBuilder fromAfterSql = new StringBuilder(" ( ");
        StringBuilder keywordSqlSb = new StringBuilder("select word_root wordRoot, count(distinct r.query_id) `frequency`, ")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) adCost,SUM(sale_num) adOrderNum,SUM(ad_order_num) adSelfSaleNum,")
                .append("SUM(total_sales) adSale, SUM(ad_sales) `adSales`,SUM(ad_sale_num) `adSaleNum`, SUM(order_num) orderNum")
                .append(" from t_amazon_word_root_keyword_sp w left join t_cpc_query_keyword_report r")
                .append(" on w.puid = r.puid and w.shop_id = r.shop_id and w.query_id = r.query_id ");
        this.getWhereSqlByKeywordPageList(qo, keywordSqlSb, argsList);
        fromAfterSql.append(keywordSqlSb);

        fromAfterSql.append(" UNION ALL ");

        StringBuilder targetSqlSb = new StringBuilder("select word_root wordRoot, count(distinct r.query_id) `frequency`, ")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) adCost,SUM(sale_num) adOrderNum,SUM(ad_order_num) adSelfSaleNum,")
                .append("SUM(total_sales) adSale, SUM(ad_sales) `adSales`,SUM(ad_sale_num) `adSaleNum`, SUM(order_num) orderNum")
                .append(" from t_amazon_word_root_targeting_sp w left join t_cpc_query_targeting_report r")
                .append(" on w.puid = r.puid and w.shop_id = r.shop_id and w.query_id = r.query_id ");
        this.getWhereSqlByTargetPageList(qo, targetSqlSb, argsList);
        fromAfterSql.append(targetSqlSb);
        fromAfterSql.append(" ) c group by wordRoot ");

        this.getHavingSqlByPageList(qo, fromAfterSql, argsList);
        sqlSb.append(fromAfterSql).append(" ) s");

        HintManager hintManager = HintManager.getInstance();
        try {
            List<WordFrequencyAdMetricDto> list = getJdbcTemplate(qo.getPuid(), hintManager).query(sqlSb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordFrequencyAdMetricDto.class));
            return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
        } finally {
            hintManager.close();
        }
    }

    @Override
    public GetWordRootAggregateDataVo getPageListAggregateData(GetWordRootAggregateDataQo qo) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSb = new StringBuilder("select SUM(`frequency`) `frequency`, ")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(adCost) adCost,SUM(adOrderNum) adOrderNum,SUM(adSelfSaleNum) adSelfSaleNum,")
                .append("SUM(adSale) adSale, SUM(adSales) `adSales`,SUM(adSaleNum) `adSaleNum`, SUM(orderNum) orderNum")
                .append(" from (");

        sqlSb.append("select wordRoot, SUM(`frequency`) `frequency`, ")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(adCost) adCost,SUM(adOrderNum) adOrderNum,SUM(adSelfSaleNum) adSelfSaleNum,")
                .append("SUM(adSale) adSale, SUM(adSales) `adSales`,SUM(adSaleNum) `adSaleNum`, SUM(orderNum) orderNum")
                .append(" from ");

        StringBuilder fromAfterSql = new StringBuilder(" ( ");
        StringBuilder keywordSqlSb = new StringBuilder("select word_root wordRoot, count(distinct r.query_id) `frequency`, ")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) adCost,SUM(sale_num) adOrderNum,SUM(ad_order_num) adSelfSaleNum,")
                .append("SUM(total_sales) adSale, SUM(ad_sales) `adSales`,SUM(ad_sale_num) `adSaleNum`, SUM(order_num) orderNum")
                .append(" from t_amazon_word_root_keyword_sp w left join t_cpc_query_keyword_report r")
                .append(" on w.puid = r.puid and w.shop_id = r.shop_id and w.query_id = r.query_id ");
        this.getWhereSqlByKeywordPageList(qo, keywordSqlSb, argsList);
        fromAfterSql.append(keywordSqlSb);

        fromAfterSql.append(" UNION ALL ");

        StringBuilder targetSqlSb = new StringBuilder("select word_root wordRoot, count(distinct r.query_id) `frequency`, ")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) adCost,SUM(sale_num) adOrderNum,SUM(ad_order_num) adSelfSaleNum,")
                .append("SUM(total_sales) adSale, SUM(ad_sales) `adSales`,SUM(ad_sale_num) `adSaleNum`, SUM(order_num) orderNum")
                .append(" from t_amazon_word_root_targeting_sp w left join t_cpc_query_targeting_report r")
                .append(" on w.puid = r.puid and w.shop_id = r.shop_id and w.query_id = r.query_id ");
        this.getWhereSqlByTargetPageList(qo, targetSqlSb, argsList);
        fromAfterSql.append(targetSqlSb);
        fromAfterSql.append(" ) c group by wordRoot ");
        this.getHavingSqlByPageList(qo, fromAfterSql, argsList);

        sqlSb.append(fromAfterSql).append(" ) s");

        HintManager hintManager = HintManager.getInstance();
        try {
            List<GetWordRootAggregateDataVo> list = getJdbcTemplate(qo.getPuid(), hintManager).query(sqlSb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(GetWordRootAggregateDataVo.class));
            return CollectionUtils.isNotEmpty(list) ? list.get(0) : new GetWordRootAggregateDataVo();
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<WordRootTranslatorBo> listTranslatorBoByShopId(Integer puid, Integer shopId, String start, String end, int limit) {
        StringBuilder sb = new StringBuilder("select id, word_root wordRoot from " + this.getJdbcHelper().getTable());
        sb.append(" where puid = ? and shop_id = ? and create_at >= ? and create_at <= ? and word_root_cn is null limit " + limit);
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(start);
        argsList.add(end);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordRootTranslatorBo.class));
        } finally {
            hintManager.close();
        }
    }

    @Override
    public void batchUpdateWordRootCn(Integer puid, List<WordRootTranslatorBo> updateList) {
        StringBuilder sql = new StringBuilder("update " + this.getJdbcHelper().getTable() + " set word_root_cn = ? where id = ?");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (WordRootTranslatorBo bo : updateList) {
            batchArg = new Object[]{bo.getWordRootCn(), bo.getId()};
            batchArgs.add(batchArg);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).batchUpdate(sql.toString(), batchArgs);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> listQueryIdByWordRootAndQueryIdList(Integer puid, Integer shopId, String wordRoot, List<String> queryId) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder keywordSb = new StringBuilder("select query_id from t_amazon_word_root_keyword_sp");
        keywordSb.append(" where puid = ? and shop_id = ? and word_root = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(wordRoot);
        if (CollectionUtils.isNotEmpty(queryId)) {
            keywordSb.append(SqlStringUtil.dealInList("query_id", queryId, argsList));
        }

        StringBuilder targetSb = new StringBuilder("select query_id from t_amazon_word_root_targeting_sp ");
        targetSb.append(" where puid = ? and shop_id = ? and word_root = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(wordRoot);
        if (CollectionUtils.isNotEmpty(queryId)) {
            targetSb.append(SqlStringUtil.dealInList("query_id", queryId, argsList));
        }

        StringBuilder sb = new StringBuilder();
        sb.append("select distinct query_id from (")
                .append(keywordSb)
                .append(" union all ")
                .append(targetSb)
                .append(" ) c ");

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sb.toString(), argsList.toArray(), String.class);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append(" and shop_id = ? ");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    private void getOrderByPageList(GetWordRootDataQo qo, StringBuilder sb) {
        if (StringUtils.isNotBlank(qo.getOrderField()) && StringUtils.isNotBlank(qo.getOrderValue())) {
            String orderField = this.getOrderField(qo.getOrderField());
            if (StringUtils.isNotBlank(orderField)) {
                sb.append(" order by ").append(orderField);
                if (OrderTypeEnum.desc.getType().equals(qo.getOrderValue())) {
                    sb.append(" desc ");
                }
            }
        } else {
            sb.append(" order by frequency desc ");
        }
    }

    private void getWhereSqlByKeywordPageList(GetWordRootAggregateDataQo qo, StringBuilder sb, List<Object> argsList) {
        sb.append(" where w.puid = ? and w.shop_id = ? and r.count_date >= ? and r.count_date <= ? ");
        argsList.add(qo.getPuid());
        argsList.add(qo.getShopId());
        argsList.add(qo.getStartDate());
        argsList.add(qo.getEndDate());
        List<String> matchTypeList = StringUtil.stringToList(qo.getMatchType(), StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        //不带匹配类型条件查询不走下面逻辑
        // start
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isNotBlank(MatchValueEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            //如果匹配条件为(紧密匹配，宽泛匹配),则不查询数据
            //把matchTypes作为条件查不出数据
            if (CollectionUtils.isEmpty(matchTypes)) {
                sb.append(" group by w.word_root having 1 = 0 ");
                return;
            }
        }
        if (StringUtils.isNotBlank(qo.getWordFrequencyType())) {
            List<Integer> wordFrequencyTypeList = new ArrayList<>();
            for (String wordFrequencyType : qo.getWordFrequencyType().split(StringUtil.SPLIT_COMMA)) {
                WordRoot.WordFrequencyType wordFrequencyTypeEnum = WordRoot.WordFrequencyType.getWordFrequencyType(Integer.valueOf(wordFrequencyType));
                if (wordFrequencyTypeEnum != null) {
                    wordFrequencyTypeList.add(wordFrequencyTypeEnum.getType());
                }
            }
            if (CollectionUtils.isNotEmpty(wordFrequencyTypeList)) {
                sb.append(SqlStringUtil.dealInList("w.word_frequency_type", wordFrequencyTypeList, argsList));
            }
        }
        sb.append(" and r.query not REGEXP '" + Constants.ASIN_REGEX + "' ");
        if (CollectionUtils.isNotEmpty(matchTypes)) {
            sb.append(SqlStringUtil.dealInList("r.match_type", matchTypes, argsList));
        }
        if (CollectionUtils.isNotEmpty(qo.getCampaignIdList())) {
            sb.append(SqlStringUtil.dealInList("r.campaign_id", qo.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(qo.getGroupIds())) {
            List<String> list = StringUtil.splitStr(qo.getGroupIds());
            sb.append(SqlStringUtil.dealInList("r.ad_group_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(qo.getGroupIdList())) {
            sb.append(SqlStringUtil.dealInList("r.ad_group_id", qo.getGroupIdList(), argsList));
        }

        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchField()) && StringUtils.isNotBlank(qo.getSearchType())) {
            GetWordRootDataQo.SearchFieldEnum searchFieldEnum = GetWordRootDataQo.SearchFieldEnum.getSearchField(qo.getSearchField());
            if (searchFieldEnum != null) {
                if (SearchTypeEnum.BLUR.getValue().equals(qo.getSearchType())) { //模糊搜索
                    sb.append(" and ").append(searchFieldEnum.getField()).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(qo.getSearchValue().trim()) + "%");
                } else {//默认精确
                    List<String> searchValueList = StringUtil.splitStr(qo.getSearchValue().trim(), StringUtil.SPECIAL_COMMA);
                    if (searchValueList.size() > 1) {
                        sb.append(SqlStringUtil.dealInList(searchFieldEnum.getField(), searchValueList, argsList));
                    } else {
                        sb.append(" and ").append(searchFieldEnum.getField()).append(" = ?");
                        argsList.add(searchValueList.get(0));
                    }
                }
            }
        }
        sb.append(" group by w.word_root ");
    }

    private String getWhereSqlByTargetPageList(GetWordRootAggregateDataQo qo, StringBuilder sb, List<Object> argsList) {
        sb.append(" where w.puid = ? and w.shop_id = ? and r.count_date >= ? and r.count_date <= ? ");
        argsList.add(qo.getPuid());
        argsList.add(qo.getShopId());
        argsList.add(qo.getStartDate());
        argsList.add(qo.getEndDate());
        sb.append(" and r.query not REGEXP '" + Constants.ASIN_REGEX + "' ");
        List<String> matchTypeList = StringUtil.stringToList(qo.getMatchType(),StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        List<String> matchAsinTypes = Lists.newArrayList();
        /**
         *   根据匹配类型（matchType）查对应表的数据
         *   matchType in ('close-match','loose-match')'广泛匹配','词组匹配','精准匹配' 不查询该表数据
         *   matchType in ('broad','phrase','exact')'紧密匹配'，'宽泛匹配'查询该表数据
         *   matchTypes：符合查询该表数据的条件('紧密匹配'，'宽泛匹配')数组
         *   MatchValueEnum值是('广泛匹配','词组匹配','精准匹配')的枚举
         */
        //不带匹配类型条件查询不走下面逻辑
        // start
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isBlank(MatchValueEnum.getMatchValue(matchType))) {
                    if (!matchType.contains("=")){
                        matchTypes.add(matchType);
                    }
                }
                if (matchType.contains("=")) {
                    matchAsinTypes.add(matchType);
                }
            }
            //如果匹配条件不是('紧密匹配'，'宽泛匹配'),则不查询数据
            if (CollectionUtils.isEmpty(matchTypes) && CollectionUtils.isEmpty(matchAsinTypes)) {
                sb.append(" group by w.word_root having 1=0 ");
                return sb.toString();
            }
        }
        if (CollectionUtils.isNotEmpty(matchTypes) || CollectionUtils.isNotEmpty(matchAsinTypes)) {
            sb.append(" and (");
            if (CollectionUtils.isNotEmpty(matchTypes) && CollectionUtils.isNotEmpty(matchAsinTypes)) {
                sb.append(SqlStringUtil.dealInListNotAnd("r.targeting_expression", matchTypes, argsList));
                sb.append(" or ");
                sb.append(SqlStringUtil.dealPrefixLikeListOr("r.targeting_expression", matchAsinTypes, argsList));
            } else if (CollectionUtils.isNotEmpty(matchTypes)) {
                sb.append(SqlStringUtil.dealInListNotAnd("r.targeting_expression", matchTypes, argsList));
            } else {
                sb.append(SqlStringUtil.dealPrefixLikeListOr("r.targeting_expression", matchAsinTypes, argsList));
            }
            sb.append(")");
        }
        if (StringUtils.isNotBlank(qo.getWordFrequencyType())) {
            List<Integer> wordFrequencyTypeList = new ArrayList<>();
            for (String wordFrequencyType : qo.getWordFrequencyType().split(StringUtil.SPLIT_COMMA)) {
                WordRoot.WordFrequencyType wordFrequencyTypeEnum = WordRoot.WordFrequencyType.getWordFrequencyType(Integer.valueOf(wordFrequencyType));
                if (wordFrequencyTypeEnum != null) {
                    wordFrequencyTypeList.add(wordFrequencyTypeEnum.getType());
                }
            }
            if (CollectionUtils.isNotEmpty(wordFrequencyTypeList)) {
                sb.append(SqlStringUtil.dealInList("w.word_frequency_type", wordFrequencyTypeList, argsList));
            }
        }
        if (CollectionUtils.isNotEmpty(qo.getCampaignIdList())) {
            sb.append(SqlStringUtil.dealInList("r.campaign_id", qo.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(qo.getGroupIds())) {
            List<String> list = StringUtil.splitStr(qo.getGroupIds());
            sb.append(SqlStringUtil.dealInList("r.ad_group_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(qo.getGroupIdList())) {
            sb.append(SqlStringUtil.dealInList("r.ad_group_id", qo.getGroupIdList(), argsList));
        }
        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchField()) && StringUtils.isNotBlank(qo.getSearchType())) {
            GetWordRootDataQo.SearchFieldEnum searchFieldEnum = GetWordRootDataQo.SearchFieldEnum.getSearchField(qo.getSearchField());
            if (searchFieldEnum != null) {
                if (SearchTypeEnum.BLUR.getValue().equals(qo.getSearchType())) { //模糊搜索
                    sb.append(" and ").append(searchFieldEnum.getField()).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(qo.getSearchValue().trim()) + "%");
                } else {//默认精确
                    List<String> searchValueList = StringUtil.splitStr(qo.getSearchValue().trim(), StringUtil.SPECIAL_COMMA);
                    if (searchValueList.size() > 1) {
                        sb.append(SqlStringUtil.dealInList(searchFieldEnum.getField(), searchValueList, argsList));
                    } else {
                        sb.append(" and ").append(searchFieldEnum.getField()).append(" = ?");
                        argsList.add(searchValueList.get(0));
                    }
                }
            }
        }
        sb.append(" group by w.word_root ");
        return sb.toString();
    }

    private void getHavingSqlByPageList(GetWordRootAggregateDataQo qo, StringBuilder sb, List<Object> argsList) {
        if (!qo.getUseAdvanced()) {
            return;
        }

        BigDecimal shopSales = qo.getShopSales() != null ? qo.getShopSales() : BigDecimal.valueOf(0);

        sb.append(" having 1=1 ");
        //频率
        if (qo.getFrequencyMin() != null) {
            sb.append(" and frequency >= ? ");
            argsList.add(qo.getFrequencyMin());
        }
        if (qo.getFrequencyMax() != null) {
            sb.append(" and frequency <= ? ");
            argsList.add(qo.getFrequencyMax());
        }
        //花费
        if (qo.getCostMin() != null) {
            sb.append(" and adCost >= ? ");
            argsList.add(qo.getCostMin());
        }
        if (qo.getCostMax() != null) {
            sb.append(" and adCost <= ? ");
            argsList.add(qo.getCostMax());
        }
        //展示量
        if (qo.getImpressionsMin() != null) {
            sb.append(" and impressions >= ? ");
            argsList.add(qo.getImpressionsMin());
        }
        if (qo.getImpressionsMax() != null) {
            sb.append(" and impressions <= ? ");
            argsList.add(qo.getImpressionsMax());
        }
        //点击量
        if (qo.getClicksMin() != null) {
            sb.append(" and clicks >= ? ");
            argsList.add(qo.getClicksMin());
        }
        if (qo.getClicksMax() != null) {
            sb.append(" and clicks <= ? ");
            argsList.add(qo.getClicksMax());
        }
        //广告订单量
        if (qo.getOrderNumMin() != null) {
            sb.append(" and adOrderNum >= ? ");
            argsList.add(qo.getOrderNumMin());
        }
        if (qo.getOrderNumMax() != null) {
            sb.append(" and adOrderNum <= ? ");
            argsList.add(qo.getOrderNumMax());
        }
        //本广告产品订单量
        if (qo.getAdSaleNumMin() != null) {
            sb.append(" and ifnull(adSaleNum, 0) >= ? ");
            argsList.add(qo.getAdSaleNumMin());
        }
        if (qo.getAdSaleNumMax() != null) {
            sb.append(" and ifnull(adSaleNum, 0) <= ? ");
            argsList.add(qo.getAdSaleNumMax());
        }
        //广告销售额
        if (qo.getSalesMin() != null) {
            sb.append(" and adSale >= ? ");
            argsList.add(qo.getSalesMin());
        }
        if (qo.getSalesMax() != null) {
            sb.append(" and adSale <= ? ");
            argsList.add(qo.getSalesMax());
        }
        //本广告产品销售额
        if (qo.getAdSalesMin() != null) {
            sb.append(" and ifnull(adSales, 0) >= ? ");
            argsList.add(qo.getAdSalesMin());
        }
        if (qo.getAdSalesMax() != null) {
            sb.append(" and ifnull(adSales, 0) <= ? ");
            argsList.add(qo.getAdSalesMax());
        }
        //广告销量
        if (qo.getAdSalesTotalMin() != null) {
            sb.append(" and ifnull(orderNum, 0) >= ? ");
            argsList.add(qo.getAdSalesTotalMin());
        }
        if (qo.getAdSalesTotalMax() != null) {
            sb.append(" and ifnull(orderNum, 0) <= ? ");
            argsList.add(qo.getAdSalesTotalMax());
        }
        //本广告产品销量
        if (qo.getAdSelfSaleNumMin() != null) {
            sb.append(" and ifnull(adSelfSaleNum, 0) >= ? ");
            argsList.add(qo.getAdSelfSaleNumMin());
        }
        if (qo.getAdSelfSaleNumMax() != null) {
            sb.append(" and ifnull(adSelfSaleNum, 0) <= ? ");
            argsList.add(qo.getAdSelfSaleNumMax());
        }
        //ctr点击率（clicks/impressions）
        if (qo.getClickRateMin() != null) {
            sb.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
            argsList.add(qo.getClickRateMin());
        }
        if (qo.getClickRateMax() != null) {
            sb.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
            argsList.add(qo.getClickRateMax());
        }
        //cvr订单转化率
        if (qo.getSalesConversionRateMin() != null) {
            sb.append(" and ROUND(ifnull(adOrderNum/clicks,0),4) >= ?");
            argsList.add(qo.getSalesConversionRateMin());
        }
        if (qo.getSalesConversionRateMax() != null) {
            sb.append(" and ROUND(ifnull(adOrderNum/clicks,0),4) <= ?");
            argsList.add(qo.getSalesConversionRateMax());
        }
        //CPA
        if (qo.getCpaMin() != null) {
            sb.append(" and ROUND(ifnull(adCost/adOrderNum,0), 2) >= ?");
            argsList.add(qo.getCpaMin());
        }
        if (qo.getCpaMax() != null) {
            sb.append(" and ROUND(ifnull(adCost/adOrderNum,0), 2) <= ?");
            argsList.add(qo.getCpaMax());
        }
        //cpc  平均点击费用
        if (qo.getCpcMin() != null) {
            sb.append(" and ROUND(ifnull(adCost/clicks,0),2) >= ?");
            argsList.add(qo.getCpcMin());
        }
        if (qo.getCpcMax() != null) {
            sb.append(" and ROUND(ifnull(adCost/clicks,0),2) <= ?");
            argsList.add(qo.getCpcMax());
        }
        //acos
        if (qo.getAcosMin() != null) {
            sb.append(" and ROUND(ifnull(adCost/adSale,0),4) >= ?");
            argsList.add(qo.getAcosMin());
        }
        if (qo.getAcosMax() != null) {
            sb.append(" and ROUND(ifnull(adCost/adSale,0),4) <= ?");
            argsList.add(qo.getAcosMax());
        }
        // roas
        if (qo.getRoasMin() != null) {
            sb.append(" and ROUND(ifnull(adSale/adCost,0),2) >= ?");
            argsList.add(qo.getRoasMin());
        }
        if (qo.getRoasMax() != null) {
            sb.append(" and ROUND(ifnull(adSale/adCost,0),2) <= ?");
            argsList.add(qo.getRoasMax());
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(adCost,0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(qo.getAcotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(adCost,0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(qo.getAcotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        // asots 需要乘以店铺销售额
        if (qo.getAsotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(adSale,0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(qo.getAsotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        // asots  需要乘以店铺销售额
        if (qo.getAsotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(adSale,0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(qo.getAsotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        //其他产品广告订单量
        if (qo.getAdOtherOrderNumMin() != null) {
            sb.append(" and ifnull(adOrderNum - adSaleNum, 0) >= ?");
            argsList.add(qo.getAdOtherOrderNumMin());
        }
        if (qo.getAdOtherOrderNumMax() != null) {
            sb.append(" and ifnull(adOrderNum - adSaleNum, 0) <= ?");
            argsList.add(qo.getAdOtherOrderNumMax());
        }
        //其他产品广告销售额
        if (qo.getAdOtherSalesMin() != null) {
            sb.append(" and ifnull(adSale - adSales, 0) >= ?");
            argsList.add(qo.getAdOtherSalesMin());
        }
        if (qo.getAdOtherSalesMax() != null) {
            sb.append(" and ifnull(adSale - adSales, 0) <= ?");
            argsList.add(qo.getAdOtherSalesMax());
        }
        //其他产品广告销量
        if (qo.getAdOtherSaleNumMin() != null) {
            sb.append(" and ifnull(orderNum - adSelfSaleNum, 0) >= ?");
            argsList.add(qo.getAdOtherSaleNumMin());
        }
        if (qo.getAdOtherSaleNumMax() != null) {
            sb.append(" and ifnull(orderNum - adSelfSaleNum, 0) <= ?");
            argsList.add(qo.getAdOtherSaleNumMax());
        }
    }

    private void getWhereSqlByQueryList(WordRootTopDto dto, StringBuilder sb, List<Object> argsList) {
        sb.append(" where puid = ? and shop_id = ? ");
        argsList.add(dto.getPuid());
        argsList.add(dto.getShopId());

        if (dto.getWordFrequencyType() != null) {
            WordRoot.WordFrequencyType wordFrequencyType = WordRoot.WordFrequencyType.getWordFrequencyType(dto.getWordFrequencyType());
            if (wordFrequencyType != null) {
                sb.append(" and word_frequency_type = ? ");
                argsList.add(dto.getWordFrequencyType());
            }
        }

        if (CollectionUtils.isNotEmpty(dto.getIdList())) {
            sb.append(SqlStringUtil.dealInList("query_id", dto.getIdList(), argsList));
        }

        sb.append(" group by word_root ");
    }

    public String getOrderField(String field) {
        switch (field) {
            case "adCost":
                return " sum(adCost) ";
            case "impressions":
                return " sum(impressions) ";
            case "clicks":
                return " sum(clicks) ";
            //广告订单量
            case "adOrderNum":
                return " sum(adOrderNum) ";
            //本广告产品订单量
            case "adSaleNum":
                return " sum(adSaleNum) ";
            //广告销售额
            case "adSale":
                return " sum(adSale) ";
            //本广告产品销售额
            case "adSales":
                return " sum(adSales) ";
            //广告销量
            case "orderNum":
                return " sum(orderNum) ";
            //本广告产品销量
            case "adSelfSaleNum":
                return " sum(adSelfSaleNum) ";
            //广告点击率ctr
            case "ctr":
                return " ifnull(sum(clicks)/sum(impressions),0) ";
            //广告转化率cvr
            case "cvr":
                return " ifnull(sum(adOrderNum)/sum(clicks),0) ";
            case "cpa":
                return " ifnull(sum(adCost)/sum(adOrderNum),0) ";
            case "adCostPerClick":
                return " ifnull(sum(adCost)/sum(clicks),0) ";
            case "acos":
                return " ifnull(sum(adCost)/sum(adSale),0) ";
            case "roas":
                return " ifnull(sum(adSale)/sum(adCost),0) ";
            case "acots":
                return " sum(adCost) ";
            case "asots":
                return " sum(adSale) ";
            //其他产品广告订单量
            case "adOtherOrderNum":
                return " ifnull(sum(adOrderNum) - sum(adSaleNum),0) ";
            //其他产品广告销售额
            case "adOtherSales":
                return " ifnull(sum(adSale) - sum(adSales),0) ";
            //其他产品广告销量
            case "adOtherSaleNum":
                return " ifnull(sum(orderNum) - sum(adSelfSaleNum),0) ";
            default:
                return " sum(frequency) ";
        }
    }
}
