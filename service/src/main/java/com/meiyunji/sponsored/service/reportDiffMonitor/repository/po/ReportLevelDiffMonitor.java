package com.meiyunji.sponsored.service.reportDiffMonitor.repository.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 报告数据层级差异监控表
 *
 * @Author: hejh
 * @Date: 2024/5/30 10:57
 */
@Data
@DbTable(value = "t_report_level_diff_monitor")
public class ReportLevelDiffMonitor implements Serializable {
    /**
     * 主键
     */
    @DbColumn(value = "id", autoIncrement = true, key = true)
    private Long id;
    /**
     * 广告类型，sp、sb、sd
     *
     * @see com.meiyunji.sponsored.rpc.syncReport.AdTypeEnum
     */
    @DbColumn(value = "ad_type")
    private String adType;
    /**
     * 广告层级类型：2-组、3-投放
     *
     * @see com.meiyunji.sponsored.service.reportDiffMonitor.enums.LevelTypeEnum
     */
    @DbColumn(value = "level_type")
    private Integer levelType;
    /**
     * 统计日期：yyyyMMdd
     */
    @DbColumn(value = "count_date")
    private String countDate;
    /**
     * 实验对象json，shopIdList
     */
    @DbColumn(value = "experiment_json")
    private String experimentJson;
    /**
     * 监控指标json
     */
    @DbColumn(value = "metric_json")
    private String metricJson;
    /**
     * 差异值
     */
    @DbColumn(value = "diff")
    private BigDecimal diff;
    /**
     * 差异值百分比格式
     */
    @DbColumn(value = "diff_percent")
    private String diffPercent;
    /**
     * 创建时间
     */
    @DbColumn(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @DbColumn(value = "update_time")
    private LocalDateTime updateTime;

}
