package com.meiyunji.sponsored.service.util;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface CurrencyConversion {

    /**
     * 需要换算
     *
     * @return needConversion
     */
    boolean needConversion() default true;

    /**
     * 换算精度
     *
     * @return conversionScale
     */
    int conversionScale() default 10;

    /**
     * 保存精度
     *
     * @return saveScale
     */
    int saveScale() default 4;
}
