package com.meiyunji.sponsored.service.reportHour.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.sellerpartner.base.JSONUtil;
import com.meiyunji.sellfox.ams.api.entry.HourlyReportDataPb;
import com.meiyunji.sellfox.ams.api.service.*;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.grpc.common.AdHourReportRequest;
import com.meiyunji.sponsored.grpc.entry.AdReportChartRpcVoPb;
import com.meiyunji.sponsored.grpc.entry.ReportDateModelPb;
import com.meiyunji.sponsored.rpc.adAggregateHour.AdPageBasicData;
import com.meiyunji.sponsored.rpc.adCommon.AdCampaignHourRpcVo;
import com.meiyunji.sponsored.rpc.adCommon.GetCampaignBudgetHourReportResponse;
import com.meiyunji.sponsored.rpc.adCommon.GetCampaignHourReportResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.budgetUsage.dao.IAmazonAdBudgetUsageDao;
import com.meiyunji.sponsored.service.budgetUsage.entity.AmazonAdBudgetUsage;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.config.nacos.AdManageLimitConfig;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonMarketingStreamDataDao;
import com.meiyunji.sponsored.service.cpc.dto.CampaignHourlyReportSelectDto;
import com.meiyunji.sponsored.service.cpc.dto.FeedHourlySelectDTO;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonMarketingStreamData;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdFeedReportService;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.dashboard.dto.MultiThreadQueryParamDto;
import com.meiyunji.sponsored.service.doris.dao.IOdsAdManageOperationLogDao;
import com.meiyunji.sponsored.service.dashboard.dto.MultiThreadQueryParamDtoMultiShop;
import com.meiyunji.sponsored.service.doris.dao.IDwsSaleProfitShopDayDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductReportDao;
import com.meiyunji.sponsored.service.enums.ShopTypeEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogFromEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.doris.dao.impl.OdsAmazonAdFlowConversionDaoImpl;
import com.meiyunji.sponsored.service.log.po.OperationContent;
import com.meiyunji.sponsored.service.multiple.common.utils.MultipleUtils;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AggregateIdsTemporary;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.CampaignAggregateHourParam;
import com.meiyunji.sponsored.service.reportHour.constants.HourConvert;
import com.meiyunji.sponsored.service.reportHour.service.IAmazonAdCampaignHourReportService;
import com.meiyunji.sponsored.service.reportHour.utils.AggregationDataUtil;
import com.meiyunji.sponsored.service.reportHour.utils.ReportChartUtil;
import com.meiyunji.sponsored.service.reportHour.vo.*;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import com.meiyunji.sponsored.service.util.PbUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2022/12/5 16:28
 */
@Service
public class AmazonAdCampaignHourReportServiceImpl implements IAmazonAdCampaignHourReportService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Qualifier("adFeedBlockingStub")
    @Autowired
    private AmsApiGrpc.AmsApiBlockingStub adFeedBlockingStub;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Resource
    private IAmazonAdBudgetUsageDao amazonAdBudgetUsageDao;
    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;

    @Resource
    private CpcPageIdsHandler cpcPageIdsHandler;

    @Resource
    private MultiThreadQueryAndMergeUtil multiThreadQueryAndMergeUtil;

    @Resource
    private IAmazonAdFeedReportService amazonAdFeedReportService;
    @Autowired
    private IAmazonMarketingStreamDataDao amazonMarketingStreamDataDao;
    @Autowired
    private IOdsAmazonAdProductDao odsAmazonAdProductDao;
    @Autowired
    private IOdsAmazonAdProductReportDao odsAmazonAdProductReportDao;
    @Autowired
    private OdsAmazonAdFlowConversionDaoImpl odsAmazonAdFlowConversionDao;

    @Autowired
    private IOdsAdManageOperationLogDao odsAdManageOperationLogDao;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Resource
    private IDwsSaleProfitShopDayDao dwsSaleProfitShopDayDao;

    @Resource
    private IOdsAmazonAdCampaignAllReportDao odsAmazonAdCampaignAllReportDao;

    @Resource
    private AdManageLimitConfig adManageLimitConfig;

    @Deprecated
    @Override
    public GetCampaignHourReportResponse.CampaignHour getListGprc(int puid, CampaignHourParam param, ReportDateModelPb.ReportDateModel dateModel) {

        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }

        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        //取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate().replace("-", ""), param.getEndDate().replace("-", ""));
        List<AdCampaignHourVo> list = Lists.newArrayList();
        if (dateModel == ReportDateModelPb.ReportDateModel.HOURLY) {
            //产品透视分析ASIN维度
//            if (StringUtils.isNotBlank(param.getAsin())) {
//                list = getListByAsin(puid, param);
//            } else {
                list = getList(puid, param);
//            }
        } else if (dateModel == ReportDateModelPb.ReportDateModel.DAILY) {
            list = getDayList(param.getPuid(), param);
        } else if (dateModel == ReportDateModelPb.ReportDateModel.WEEKLY) {
            list = getWeekList(param.getPuid(), param);
        } else if (dateModel == ReportDateModelPb.ReportDateModel.MONTHLY) {
            list = getMonthList(param.getPuid(), param);
        }
        GetCampaignHourReportResponse.CampaignHour.Builder builder1 = GetCampaignHourReportResponse.CampaignHour.newBuilder();
        if (CollectionUtils.isNotEmpty(list)) {
            boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                    Constants.isADOrderField(param.getOrderField(), AdProductHourVo.class);
            if (isSorted) {
                PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
            }
        }
        builder1.addAllList(list.stream().filter(Objects::nonNull)
                .map(key -> PbUtil.toCampaignHourReportPb(key, shopSalesByDate, isVc)).collect(Collectors.toList()));
        builder1.setSummary(PbUtil.toCampaignHourReportPb(summary(list, shopSalesByDate), shopSalesByDate, isVc));
        builder1.addAllChart(ReportChartUtil.getCampaignHourChartData(list, false));
        //对比数据,chart图数据
        if (param.getIsCompare() != null && param.getIsCompare() == 1) {
            List<AdCampaignHourVo> compareHourVos = list.stream().map(item -> {
                AdCampaignHourVo vo = new AdCampaignHourVo();
                vo.setLabel(item.getLabel());
                vo.setClicks(item.getClicksCompare());
                vo.setImpressions(item.getImpressionsCompare());
                vo.setAdSale(item.getAdSaleCompare());
                vo.setAdCost(item.getAdCostCompare());
                vo.setAdOrderNum(item.getAdOrderNumCompare());
                return vo;
            }).collect(Collectors.toList());
            builder1.addAllChart(ReportChartUtil.getCampaignHourChartData(compareHourVos, true));
        }

        return builder1.build();
    }

    @Override
    public GetCampaignHourReportResponse.CampaignHour getAggregateList(int puid, List<String> aggregateIds,
                                                                       CampaignAggregateHourParamVO param, ReportDateModelPb.ReportDateModel dateModel) {
        // 兼容旧单店铺逻辑
        if(CollectionUtils.isEmpty(param.getShopIdList()) && param.getShopId() != null){
            param.setShopIdList(CollectionUtil.newArrayList(param.getShopId()));
        }
        // 店铺状态校验
        List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(param.getPuid(), param.getShopIdList());
        if (CollectionUtils.isEmpty(shopAuths)) {
            throw new SponsoredBizException("店铺未授权");
        }
        boolean isVc = shopAuths.stream().anyMatch(e -> ShopTypeEnum.VC.getCode().equals(e.getType()));
        // 多店鋪,超过限制广告数量返回，防止doris cpu过高
        if((shopAuths.size() > 1 && aggregateIds.size() >= adManageLimitConfig.getHourLimit()) || shopAuths.size() > 40){
            if(shopAuths.size() > 40){
                throw new SponsoredBizException("当前店铺数量超过40个，请减少后重新查询!");
            }else{
                throw new SponsoredBizException("当前所选数据量过大，请减少店铺查询!");
            }
        }
        // 取店铺销售额
        BigDecimal shopSalesByDate = dwsSaleProfitShopDayDao.sumShopSaleByDateRange(param.getPuid(), param.getShopIdList(), param.getStartDate(), param.getEndDate(), MultipleUtils.changeRate(shopAuths));
        List<AdCampaignHourVo> list = Lists.newArrayList();
        List<AdCampaignHourVo> compares = Lists.newArrayList();
        CampaignHourParam paramOld = new CampaignHourParam();
        BeanUtils.copyProperties(param, paramOld);
        boolean bool = true;
        if (dateModel == ReportDateModelPb.ReportDateModel.HOURLY) {
            list = getAggregateHourList(shopAuths, aggregateIds, param);
            bool = false;
        } else if (dateModel == ReportDateModelPb.ReportDateModel.DAILY) {
            list = getAggregateDayList(param.getPuid(), aggregateIds, paramOld, compares);
        } else if (dateModel == ReportDateModelPb.ReportDateModel.WEEKLY) {
            list = getAggregateWeekList(param.getPuid(), aggregateIds, paramOld, compares);
        } else if (dateModel == ReportDateModelPb.ReportDateModel.MONTHLY) {
            list = getAggregateMonthList(param.getPuid(), aggregateIds, paramOld, compares);
        }
        GetCampaignHourReportResponse.CampaignHour.Builder builder1 = GetCampaignHourReportResponse.CampaignHour.newBuilder();


        if (CollectionUtils.isNotEmpty(list)) {
            for (AdCampaignHourVo vo : list) {
                vo.setAcots(shopSalesByDate.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(shopSalesByDate, 4, RoundingMode.HALF_UP));
                vo.setAsots(shopSalesByDate.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().multiply(new BigDecimal("100")).divide(shopSalesByDate, 4, RoundingMode.HALF_UP));
            }
            boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                    Constants.isADOrderField(param.getOrderField(), AdCampaignHourVo.class);
            if (isSorted) {
                PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
            }
        }
        builder1.setCurrency(MultipleUtils.getCurrency(shopAuths));
        builder1.addAllList(list.stream().filter(Objects::nonNull)
                .map(key -> PbUtil.toCampaignHourReportPb(key, shopSalesByDate, isVc)).collect(Collectors.toList()));
        AdCampaignHourVo summaryVO = summary(list, shopSalesByDate, bool);
        if (Integer.valueOf(1).equals(param.getIsCompare()) && dateModel != ReportDateModelPb.ReportDateModel.HOURLY) {
            AdCampaignHourVo compareVO = summary(compares, shopSalesByDate, bool);
            summaryVO.compareDataSet(compareVO);
        }
        builder1.setSummary(PbUtil.toCampaignHourReportPb(summaryVO, shopSalesByDate, isVc));
        builder1.addAllChart(ReportChartUtil.getCampaignHourChartData(list, false));
        //对比数据,chart图数据
        if (param.getIsCompare() != null && param.getIsCompare() == 1) {
            List<AdCampaignHourVo> compareHourVos = list.stream().map(item -> {
                AdCampaignHourVo vo = new AdCampaignHourVo();
                vo.setLabel(item.getLabel());
                vo.setClicks(item.getClicksCompare());
                vo.setImpressions(item.getImpressionsCompare());
                vo.setAdSale(item.getAdSaleCompare());
                vo.setAdCost(item.getAdCostCompare());
                vo.setAdOrderNum(item.getAdOrderNumCompare());
                vo.setAdSaleNum(item.getAdSaleNumCompare());
                vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                vo.setAcos(item.getAcosCompare());
                vo.setRoas(item.getRoasCompare());
                vo.setCtr(item.getCtrCompare());
                vo.setCvr(item.getCvrCompare());
                return vo;
            }).collect(Collectors.toList());
            builder1.addAllChart(ReportChartUtil.getCampaignHourChartData(compareHourVos, true));
        }
        return builder1.build();
    }

    @Override
    public GetCampaignHourReportResponse.CampaignHour getAggregateHourList(int puid, CampaignAggregateHourParam param) {
        List<ShopAuth> shopAuths = shopAuthDao.listByPuidAndMarketplace(puid, Collections.singletonList(param.getMarketplaceId()), param.getShopIdList());
        if (CollectionUtils.isEmpty(shopAuths)) {
            return null;
        }
        boolean isVc = shopAuths.stream().anyMatch(e -> ShopTypeEnum.VC.getCode().equals(e.getType()));
        AggregateIdsTemporary aggregateIdsTemporary = cpcPageIdsHandler.getAggregateIdsTemporary(param.getPageSign(), param.getAggregateType());
        if (aggregateIdsTemporary != null) {
            param.setCampaignIdList(aggregateIdsTemporary.getIdList());
            param.setAdIdList(aggregateIdsTemporary.getAdIdList());
        }

        //取店铺销售额
        List<AdCampaignHourVo> list = Lists.newArrayList();
        CampaignHourParam paramOld = new CampaignHourParam();
        BeanUtils.copyProperties(param, paramOld);
        if (ReportDateModelPb.ReportDateModel.HOURLY == param.getDateModel()) {
            list = getAggregateHourList(shopAuths, param);
        }
        GetCampaignHourReportResponse.CampaignHour.Builder builder1 = GetCampaignHourReportResponse.CampaignHour.newBuilder();
        if (CollectionUtils.isNotEmpty(list)) {
            boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                    Constants.isADOrderField(param.getOrderField(), AdProductHourVo.class);
            if (isSorted) {
                PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
            }
        }
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getPuid(), param.getShopIdList(), param.getStartDate().replace("-",""), param.getEndDate().replace("-",""));
        builder1.addAllList(list.stream().filter(Objects::nonNull)
                .map(key -> PbUtil.toCampaignHourReportPb(key, shopSalesByDate,isVc)).collect(Collectors.toList()));
        builder1.setSummary(PbUtil.toCampaignHourReportPb(summary(list, shopSalesByDate), shopSalesByDate, isVc));
        builder1.addAllChart(ReportChartUtil.getCampaignHourChartData(list, false));
        //对比数据,chart图数据
        if (param.getIsCompare() != null && param.getIsCompare() == 1) {
            List<AdCampaignHourVo> compareHourVos = list.stream().map(item -> {
                AdCampaignHourVo vo = new AdCampaignHourVo();
                vo.setLabel(item.getLabel());
                vo.setClicks(item.getClicksCompare());
                vo.setImpressions(item.getImpressionsCompare());
                vo.setAdSale(item.getAdSaleCompare());
                vo.setAdCost(item.getAdCostCompare());
                vo.setAdOrderNum(item.getAdOrderNumCompare());
                vo.setAdSaleNum(item.getAdSaleNumCompare());
                vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                vo.setAcos(item.getAcosCompare());
                vo.setRoas(item.getRoasCompare());
                vo.setCtr(item.getCtrCompare());
                vo.setCvr(item.getCvrCompare());
                return vo;
            }).collect(Collectors.toList());
            builder1.addAllChart(ReportChartUtil.getCampaignHourChartData(compareHourVos, true));
        }

        return builder1.build();
    }

    @Override
    public GetCampaignBudgetHourReportResponse.CampaignHour getBudgetListGprc(int puid, CampaignHourParam param, ReportDateModelPb.ReportDateModel dateModel) {

        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }

        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        //取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate().replace("-", ""), param.getEndDate().replace("-", ""));
        List<AdCampaignHourVo> list = Lists.newArrayList();
        if (dateModel == ReportDateModelPb.ReportDateModel.HOURLY) {
            list = getBudgetList(puid, param);
        }
//        else if (dateModel == ReportDateModelPb.ReportDateModel.DAILY) {
//            list = getDayList(param.getPuid(),param);
//        } else if (dateModel == ReportDateModelPb.ReportDateModel.WEEKLY) {
//            list = getWeekList(param.getPuid(), param);
//        } else if (dateModel == ReportDateModelPb.ReportDateModel.MONTHLY) {
//            list = getMonthList(param.getPuid(), param);
//        }
        List<AmazonAdBudgetUsage> amazonAdBudgetUsageList = amazonAdBudgetUsageDao.listByDate(puid, param.getShopId(),
                LocalDateTimeUtil.strToLocalDate(param.getStartDate(), "yyyy-MM-dd"),
                LocalDateTimeUtil.strToLocalDate(param.getEndDate(), "yyyy-MM-dd"), param.getCampaignId());

        GetCampaignBudgetHourReportResponse.CampaignHour.Builder builder1 = GetCampaignBudgetHourReportResponse.CampaignHour.newBuilder();
        if (CollectionUtils.isNotEmpty(amazonAdBudgetUsageList)) {
            if (dynamicRefreshConfiguration.verifyDorisPageByPuid(puid, dynamicRefreshConfiguration.getDorisAdManageBudgetLog())) {
                List<AdManageOperationLog> sellfoxAndAutoLog = this.listSellfoxAndAutoLog(puid, param.getShopId(), param.getCampaignId(), param.getStartDate(), param.getEndDate());
                List<AdManageOperationLog> amazonLog = this.listAmazonLog(puid, param.getShopId(), param.getCampaignId(), param.getStartDate(), param.getEndDate());
                list = ReportChartUtil.handleCampaignBudgetUsageData(sellfoxAndAutoLog, amazonLog, amazonAdBudgetUsageList, list, param.getStartDate(), param.getEndDate());
            } else {
                list = ReportChartUtil.handleCampaignBudgetUsageData(amazonAdBudgetUsageList, list, param.getStartDate(), param.getEndDate());
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                    Constants.isADOrderField(param.getOrderField(), AdCampaignHourVo.class);
            if (isSorted) {
                String orderField = param.getOrderField();
                if ("date".equalsIgnoreCase(param.getOrderField())) {
                    orderField = "label";
                } else if ("budgetAdjust".equalsIgnoreCase(param.getOrderField())) {
                    orderField = "budgetAdjustMax";
                }
                PageUtil.sortedByOrderField(list, orderField, param.getOrderType());
            }
        }


        builder1.addAllList(list.stream().filter(Objects::nonNull)
                .map(key -> PbUtil.toCampaignHourReportPb(key, shopSalesByDate, isVc)).collect(Collectors.toList()));
        builder1.setSummary(PbUtil.toCampaignHourReportPb(summary(list, shopSalesByDate), shopSalesByDate, isVc));
        List<AdReportChartRpcVoPb.AdReportChartRpcVo> adReportChartRpcVoList = ReportChartUtil.getCampaignHourChartData(list, false);
        if (null != amazonAdBudgetUsageList && amazonAdBudgetUsageList.size() > 0) {
            adReportChartRpcVoList.addAll(ReportChartUtil.getCampaignBudgetChartData(amazonAdBudgetUsageList, param.getStartDate(), false));
        }
        builder1.addAllChart(adReportChartRpcVoList);
        //对比数据,chart图数据
//        if (param.getIsCompare() != null && param.getIsCompare() == 1) {
//            List<AdCampaignHourVo> compareHourVos = list.stream().map(item-> {
//                AdCampaignHourVo vo = new AdCampaignHourVo();
//                vo.setLabel(item.getLabel());
//                vo.setClicks(item.getClicksCompare());
//                vo.setImpressions(item.getImpressionsCompare());
//                vo.setAdSale(item.getAdSaleCompare());
//                vo.setAdCost(item.getAdCostCompare());
//                vo.setAdOrderNum(item.getAdOrderNumCompare());
//                return vo;
//            }).collect(Collectors.toList());
//            List<AdReportChartRpcVoPb.AdReportChartRpcVo> adReportChartRpcVoList1 = ReportChartUtil.getCampaignHourChartData(compareHourVos, true);
//            adReportChartRpcVoList1.addAll(ReportChartUtil.getCampaignBudgetChartData(amazonAdBudgetUsageList, true));
//            builder1.addAllChart(adReportChartRpcVoList1);
//        }

        return builder1.build();
    }

    /**
     * 获取赛狐和自动化日志
     */
    private List<AdManageOperationLog> listSellfoxAndAutoLog(Integer puid, Integer shopId, String campaignId, String startDate, String endDate) {
        List<String> campaignIds = com.google.common.collect.Lists.newArrayList(campaignId);
        List<AdManageOperationLog> list = new ArrayList<>();
        List<AdManageOperationLog> adManageOperationLogs = odsAdManageOperationLogDao.listBudgetBetweenDate(puid, shopId, OperationLogFromEnum.SELLFOX.getOperationType(), campaignIds, startDate, endDate);
        if (CollectionUtils.isNotEmpty(adManageOperationLogs)) {
            adManageOperationLogs.forEach(this::dealValueAuto);
            list.addAll(adManageOperationLogs);
        }
        List<AdManageOperationLog> adManageOperationLogList = odsAdManageOperationLogDao.listBudgetBetweenDate(puid, shopId, OperationLogFromEnum.AUTO.getOperationType(), campaignIds, startDate, endDate);
        if (CollectionUtils.isNotEmpty(adManageOperationLogList)) {
            adManageOperationLogList.forEach(this::dealValueAuto);
            list.addAll(adManageOperationLogList);
        }
        return list;
    }

    /**
     * 处理值 |每日预算|50.00|30.00|   50 新值 30旧值
     * @param adManageOperationLog
     */
    private void dealValueSellfoxAndAmazon(AdManageOperationLog adManageOperationLog) {
        String message = adManageOperationLog.getMessage();
        if (StringUtils.isNotBlank(message)) {
            List<String> messageList = Arrays.stream(message.split("\\|")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            // 新值
            if (1 < messageList.size()) {
                adManageOperationLog.setNewValue(new BigDecimal(messageList.get(1)).toString());
            }
            // 旧值
            if (2 < messageList.size()) {
                adManageOperationLog.setPreviousValue(new BigDecimal(messageList.get(2)).toString());
            }
        }
    }

    private void dealValueAuto(AdManageOperationLog adManageOperationLog) {
        String message = adManageOperationLog.getOperationContent();
        if (StringUtils.isNotBlank(message)) {
            List<OperationContent> operationContent = JSONUtil.jsonToArray(message, OperationContent.class);
            if (CollectionUtils.isNotEmpty(operationContent)) {
                Optional<OperationContent> content = operationContent.stream().filter(k -> "每日预算".equals(k.getName())).findFirst();
                if (content.isPresent()) {
                    OperationContent operationContent1 = content.get();
                    adManageOperationLog.setNewValue(operationContent1.getNewValue());
                    adManageOperationLog.setPreviousValue(operationContent1.getPreviousValue());
                }
            }
        }
    }

    /**
     * 获取亚马逊日志
     */
    private List<AdManageOperationLog> listAmazonLog(Integer puid, Integer shopId, String campaignId, String startDate, String endDate) {
        List<String> campaignIds = com.google.common.collect.Lists.newArrayList(campaignId);
        List<AdManageOperationLog> adManageOperationLogs = odsAdManageOperationLogDao.listBudgetBetweenDateAmazon(puid, shopId, campaignIds, startDate, endDate);
        adManageOperationLogs.forEach(this::dealValueSellfoxAndAmazon);
        return adManageOperationLogs;
    }

    private AdCampaignHourVo summary(List<AdCampaignHourVo> list, BigDecimal shopSales, boolean bool) {
        if (bool) {
            return summary(list, shopSales);
        }
        AdCampaignHourVo vo = new AdCampaignHourVo();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        for (AdCampaignHourVo ad : list) {
            vo.setClicks(MathUtil.add(ad.getClicks(), vo.getClicks()));
            vo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), vo.getAdOrderNum()));
            vo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), vo.getOtherAdOrderNum()));
            vo.setAdSale(MathUtil.add(ad.getAdSale(), vo.getAdSale()));
            vo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), vo.getAdSelfSale()));
            vo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), vo.getAdOtherSale()));
            vo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), vo.getAdSaleNum()));
            vo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), vo.getAdOtherSaleNum()));
            vo.setImpressions(MathUtil.add(vo.getImpressions(), ad.getImpressions()));
            vo.setAdCost(MathUtil.add(ad.getAdCost(), vo.getAdCost()));
            vo.setAdCostCompare(MathUtil.add(vo.getAdCostCompare(), ad.getAdCostCompare()));
            vo.setClicksCompare(MathUtil.add(vo.getClicksCompare(), ad.getClicksCompare()));
            vo.setImpressionsCompare(MathUtil.add(vo.getImpressionsCompare(), ad.getImpressionsCompare()));
            vo.setAdSaleNumCompare(MathUtil.add(vo.getAdSaleNumCompare(), ad.getAdSaleNumCompare()));
            vo.setAdSaleCompare(MathUtil.add(vo.getAdSaleCompare(), ad.getAdSaleCompare()));
            vo.setAdOrderNumCompare(MathUtil.add(vo.getAdOrderNumCompare(), ad.getAdOrderNumCompare()));

            vo.setViewableImpressions(MathUtil.add(vo.getViewableImpressions(), ad.getViewableImpressions()));
            vo.setOrdersNewToBrand(MathUtil.add(vo.getOrdersNewToBrand(), ad.getOrdersNewToBrand()));
            vo.setUnitsOrderedNewToBrand(MathUtil.add(vo.getUnitsOrderedNewToBrand(), ad.getUnitsOrderedNewToBrand()));
            vo.setSalesNewToBrand(MathUtil.add(vo.getSalesNewToBrand(), ad.getSalesNewToBrand()));
            vo.setVcpmCost(MathUtil.add(vo.getVcpmCost(), ad.getVcpmCost()));
            vo.setVcpmImpressions(MathUtil.add(vo.getVcpmImpressions(), ad.getVcpmImpressions()));
            vo.setTotalClicks(MathUtil.add(vo.getTotalClicks(), ad.getTotalClicks()));
            vo.setTotalImpressions(MathUtil.add(vo.getTotalImpressions(), ad.getTotalImpressions()));
            vo.setTotalAdSale(MathUtil.add(vo.getTotalAdSale(), ad.getTotalAdSale()));
            vo.setTotalAdSelfSale(MathUtil.add(vo.getTotalAdSelfSale(), ad.getTotalAdSelfSale()));
        }
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setAcots(shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(shopSales, 4, RoundingMode.HALF_UP));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setAsots(shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().multiply(new BigDecimal("100")).divide(shopSales, 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));

        vo.setAdCostPerClickCompare(MathUtil.divideByZero(vo.getAdCostCompare(), BigDecimal.valueOf(vo.getClicksCompare())));
        vo.setAcosCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                vo.getAdCostCompare().multiply(new BigDecimal("100")).divide(vo.getAdSaleCompare(), 4, RoundingMode.HALF_UP));
        vo.setRoasCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                vo.getAdSaleCompare().divide(vo.getAdCostCompare(), 4, RoundingMode.HALF_UP));
        vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicksCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressionsCompare())));
        vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNumCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicksCompare())));

        vo.setVcpm(MathUtil.divideByThousand(vo.getVcpmCost(), vo.getVcpmImpressions()));
        vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getTotalImpressions()));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getTotalClicks(), vo.getViewableImpressions()));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getTotalAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
        vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getTotalAdSale().subtract(vo.getTotalAdSelfSale()), BigDecimal.valueOf(vo.getOtherAdOrderNum())));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));

        vo.afterPropertiesSet();//为各对比率属性设值
        if (BigDecimal.ZERO.compareTo(vo.getAdCost()) == 0) {
            vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setAdCostPercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        if (BigDecimal.ZERO.compareTo(vo.getAdSale()) == 0) {
            vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setAdSalePercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getAdOrderNum() == 0) {
            vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setAdOrderNumPercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getAdSaleNum() == 0) {
            vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setOrderNumPercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        return vo;
    }


    private AdCampaignHourVo summary(List<AdCampaignHourVo> list, BigDecimal shopSales) {
        AdCampaignHourVo vo = new AdCampaignHourVo();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        for (AdCampaignHourVo ad : list) {
            vo.setClicks(MathUtil.add(ad.getClicks(), vo.getClicks()));
            vo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), vo.getAdOrderNum()));
            vo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), vo.getOtherAdOrderNum()));
            vo.setAdSale(MathUtil.add(ad.getAdSale(), vo.getAdSale()));
            vo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), vo.getAdSelfSale()));
            vo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), vo.getAdOtherSale()));
            vo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), vo.getAdSaleNum()));
            vo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), vo.getAdOtherSaleNum()));
            vo.setImpressions(MathUtil.add(vo.getImpressions(), ad.getImpressions()));
            vo.setAdCost(MathUtil.add(ad.getAdCost(), vo.getAdCost()));
            vo.setAdCostCompare(MathUtil.add(vo.getAdCostCompare(), ad.getAdCostCompare()));
            vo.setClicksCompare(MathUtil.add(vo.getClicksCompare(), ad.getClicksCompare()));
            vo.setImpressionsCompare(MathUtil.add(vo.getImpressionsCompare(), ad.getImpressionsCompare()));
            vo.setAdSaleNumCompare(MathUtil.add(vo.getAdSaleNumCompare(), ad.getAdSaleNumCompare()));
            vo.setAdSaleCompare(MathUtil.add(vo.getAdSaleCompare(), ad.getAdSaleCompare()));
            vo.setAdOrderNumCompare(MathUtil.add(vo.getAdOrderNumCompare(), ad.getAdOrderNumCompare()));

            vo.setViewableImpressions(MathUtil.add(vo.getViewableImpressions(), ad.getViewableImpressions()));
            vo.setOrdersNewToBrand(MathUtil.add(vo.getOrdersNewToBrand(), ad.getOrdersNewToBrand()));
            vo.setUnitsOrderedNewToBrand(MathUtil.add(vo.getUnitsOrderedNewToBrand(), ad.getUnitsOrderedNewToBrand()));
            vo.setSalesNewToBrand(MathUtil.add(vo.getSalesNewToBrand(), ad.getSalesNewToBrand()));
            vo.setVcpmCost(MathUtil.add(vo.getVcpmCost(), ad.getVcpmCost()));
            vo.setVcpmImpressions(MathUtil.add(vo.getVcpmImpressions(), ad.getVcpmImpressions()));
        }
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setAcots(shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(shopSales, 4, RoundingMode.HALF_UP));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setAsots(shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().multiply(new BigDecimal("100")).divide(shopSales, 4, BigDecimal.ROUND_HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));

        vo.setAdCostPerClickCompare(MathUtil.divideByZero(vo.getAdCostCompare(), BigDecimal.valueOf(vo.getClicksCompare())));
        vo.setAcosCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                vo.getAdCostCompare().multiply(new BigDecimal("100")).divide(vo.getAdSaleCompare(), 4, RoundingMode.HALF_UP));
        vo.setRoasCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                vo.getAdSaleCompare().divide(vo.getAdCostCompare(), 4, RoundingMode.HALF_UP));
        vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicksCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressionsCompare())));
        vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNumCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicksCompare())));


        vo.setVcpm(MathUtil.divideByThousand(vo.getVcpmCost(), vo.getVcpmImpressions()));
        vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getImpressions()));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getClicks(), vo.getViewableImpressions()));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
        vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getOtherAdOrderNum())));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));

        vo.afterPropertiesSet();//为各对比率属性设值
        if (BigDecimal.ZERO.compareTo(vo.getAdCost()) == 0) {
            vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setAdCostPercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        if (BigDecimal.ZERO.compareTo(vo.getAdSale()) == 0) {
            vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setAdSalePercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getAdOrderNum() == 0) {
            vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setAdOrderNumPercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getAdSaleNum() == 0) {
            vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setOrderNumPercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        return vo;
    }

    private AdCampaignHourVo summary(List<AdCampaignHourVo> list) {
        AdCampaignHourVo vo = new AdCampaignHourVo();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        for (AdCampaignHourVo ad : list) {
            vo.setClicks(MathUtil.add(ad.getClicks(), vo.getClicks()));
            vo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), vo.getAdOrderNum()));
            vo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), vo.getOtherAdOrderNum()));
            vo.setAdSale(MathUtil.add(ad.getAdSale(), vo.getAdSale()));
            vo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), vo.getAdSelfSale()));
            vo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), vo.getAdOtherSale()));
            vo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), vo.getAdSaleNum()));
            vo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), vo.getAdOtherSaleNum()));
            vo.setImpressions(MathUtil.add(vo.getImpressions(), ad.getImpressions()));
            vo.setAdCost(MathUtil.add(ad.getAdCost(), vo.getAdCost()));
            vo.setAdCostCompare(MathUtil.add(vo.getAdCostCompare(), ad.getAdCostCompare()));
            vo.setClicksCompare(MathUtil.add(vo.getClicksCompare(), ad.getClicksCompare()));
            vo.setImpressionsCompare(MathUtil.add(vo.getImpressionsCompare(), ad.getImpressionsCompare()));
            vo.setAdSaleNumCompare(MathUtil.add(vo.getAdSaleNumCompare(), ad.getAdSaleNumCompare()));
            vo.setAdSaleCompare(MathUtil.add(vo.getAdSaleCompare(), ad.getAdSaleCompare()));
            vo.setAdOrderNumCompare(MathUtil.add(vo.getAdOrderNumCompare(), ad.getAdOrderNumCompare()));
            //sb、sd才有
            vo.setViewableImpressions(MathUtil.add(vo.getViewableImpressions(), ad.getViewableImpressions()));
            vo.setOrdersNewToBrand(MathUtil.add(vo.getOrdersNewToBrand(), ad.getOrdersNewToBrand()));
            vo.setUnitsOrderedNewToBrand(MathUtil.add(vo.getUnitsOrderedNewToBrand(), ad.getUnitsOrderedNewToBrand()));
            vo.setSalesNewToBrand(MathUtil.add(vo.getSalesNewToBrand(), ad.getSalesNewToBrand()));
        }
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));
        vo.afterPropertiesSet();//为各对比率属性设值
        if (vo.getAdCostPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdCostPercentage()) == 0) {
            vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdCostPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getAdSalePercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdSalePercentage()) == 0) {
            vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdSalePercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getAdOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdOrderNumPercentage()) == 0) {
            vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdOrderNumPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getOrderNumPercentage()) == 0) {
            vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setOrderNumPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        return vo;
    }

    private AdCampaignHourRpcVo convertToGrpc(AdCampaignHourVo adCampaignHourVo) {
        if (adCampaignHourVo == null) {
            return null;
        }

        AdCampaignHourRpcVo.Builder builderVo = AdCampaignHourRpcVo.newBuilder();
        if (adCampaignHourVo.getAcots() != null) {
            builderVo.setAcots(adCampaignHourVo.getAcots().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getAdCost() != null) {
            builderVo.setAdCost(adCampaignHourVo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getAdCostCompare() != null) {
            builderVo.setAdCostCompare(adCampaignHourVo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getAdCostCompareRate() != null) {
            builderVo.setAdCostCompareRate(adCampaignHourVo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getRoas() != null) {
            builderVo.setRoas(adCampaignHourVo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getCpa() != null) {
            builderVo.setCpa(adCampaignHourVo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getAdCostPerClick() != null) {
            builderVo.setAdCostPerClick(adCampaignHourVo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getAcos() != null) {
            builderVo.setAcos(adCampaignHourVo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getAsots() != null) {
            builderVo.setAsots(adCampaignHourVo.getAsots().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getLabel() != null) {
            builderVo.setTitle(adCampaignHourVo.getLabel());
        }
        if (adCampaignHourVo.getImpressions() != null) {
            builderVo.setImpressions(adCampaignHourVo.getImpressions());
        }
        if (adCampaignHourVo.getImpressionsCompare() != null) {
            builderVo.setImpressionsCompare(adCampaignHourVo.getImpressionsCompare());
        }
        if (adCampaignHourVo.getImpressionsCompareRate() != null) {
            builderVo.setImpressionsCompareRate(adCampaignHourVo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getClicks() != null) {
            builderVo.setClicks(adCampaignHourVo.getClicks());
        }
        if (adCampaignHourVo.getClicksCompare() != null) {
            builderVo.setClicksCompare(adCampaignHourVo.getClicksCompare());
        }
        if (adCampaignHourVo.getClicksCompareRate() != null) {
            builderVo.setClicksCompareRate(adCampaignHourVo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getCtr() != null) {
            builderVo.setCtr(adCampaignHourVo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getCvr() != null) {
            builderVo.setCvr(adCampaignHourVo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getAdOrderNum() != null) {
            builderVo.setAdOrderNum(adCampaignHourVo.getAdOrderNum());
        }
        if (adCampaignHourVo.getAdOrderNumCompare() != null) {
            builderVo.setAdOrderNumCompare(adCampaignHourVo.getAdOrderNumCompare());
        }
        if (adCampaignHourVo.getAdOrderNumCompareRate() != null) {
            builderVo.setAdOrderNumCompareRate(adCampaignHourVo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getSelfAdOrderNum() != null) {
            builderVo.setSelfAdOrderNum(adCampaignHourVo.getSelfAdOrderNum());
        }
        if (adCampaignHourVo.getOtherAdOrderNum() != null) {
            builderVo.setOtherAdOrderNum(adCampaignHourVo.getOtherAdOrderNum());
        }
        if (adCampaignHourVo.getAdSale() != null) {
            builderVo.setAdSale(adCampaignHourVo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getAdSaleCompare() != null) {
            builderVo.setAdSaleCompare(adCampaignHourVo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getAdSaleCompareRate() != null) {
            builderVo.setAdSaleCompareRate(adCampaignHourVo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getAdSelfSale() != null) {
            builderVo.setAdSelfSale(adCampaignHourVo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getAdOtherSale() != null) {
            builderVo.setAdOtherSale(adCampaignHourVo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getAdSaleNum() != null) {
            builderVo.setAdSaleNum(adCampaignHourVo.getAdSaleNum());
        }
        if (adCampaignHourVo.getAdSaleNumCompare() != null) {
            builderVo.setAdSaleNumCompare(adCampaignHourVo.getAdSaleNumCompare());
        }
        if (adCampaignHourVo.getAdSaleNumCompareRate() != null) {
            builderVo.setAdSaleNumCompareRate(adCampaignHourVo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (adCampaignHourVo.getAdSelfSaleNum() != null) {
            builderVo.setAdSelfSaleNum(adCampaignHourVo.getAdSelfSaleNum());
        }
        if (adCampaignHourVo.getAdOtherSaleNum() != null) {
            builderVo.setAdOtherSaleNum(adCampaignHourVo.getAdOtherSaleNum());
        }
        // 花费占比
        builderVo.setAdCostPercentage(adCampaignHourVo.getAdCostPercentage() != null ? adCampaignHourVo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(adCampaignHourVo.getAdSalePercentage() != null ? adCampaignHourVo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(adCampaignHourVo.getAdOrderNumPercentage() != null ? adCampaignHourVo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(adCampaignHourVo.getOrderNumPercentage() != null ? adCampaignHourVo.getOrderNumPercentage().toString() : "0.00");

        return builderVo.build();
    }


    @Override
    public List<AdCampaignHourVo> getList(int puid, CampaignHourParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllDao.getByCampaignId(puid, param.getShopId(), param.getCampaignId());
        if (amazonAdCampaignAll != null) {
            if (Constants.SB.equalsIgnoreCase(amazonAdCampaignAll.getType()) || Constants.SD.equalsIgnoreCase(amazonAdCampaignAll.getType())) {
                return amazonAdFeedReportService.listAdCampaignHourByAdHourCampaignTypeAndCostType(shopAuth, param, amazonAdCampaignAll.getType(), StringUtils.isNotBlank(amazonAdCampaignAll.getCostType()) ? amazonAdCampaignAll.getCostType() : Constants.SD_REPORT_CPC);
            }
        }
        //获取小时级数据
        FeedHourlySelectDTO builder = new FeedHourlySelectDTO();
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setMarketplaceId(shopAuth.getMarketplaceId());
        builder.setStart(LocalDate.parse(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), param.getStartDate()), DateTimeFormatter.ISO_LOCAL_DATE));
        builder.setEnd(LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE));
        builder.setType(param.getType());
        if (StringUtils.isNotBlank(param.getWeeks())) {
            builder.setWeekdayList(StringUtil.splitInt(param.getWeeks(), ","));
        }
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            builder.setCampaignIds(com.google.common.collect.Lists.newArrayList(param.getCampaignId()));
        }

        boolean returnEmptyList = false;
        if (StringUtils.isNotBlank(param.getFindType()) && StringUtils.isNotBlank(param.getFindValue())) {
            List<String> adIdList = odsAmazonAdProductReportDao.adIdListByProduct(puid, Collections.singletonList(shopAuth.getId()), shopAuth.getMarketplaceId(), param.getFindType(), param.getFindValue(), param.getStartDate(), param.getEndDate());
            if (CollectionUtils.isEmpty(adIdList)) {
                returnEmptyList = true;
            }
            builder.setAdIds(adIdList);
        }

        List<AmazonMarketingStreamData> amazonMarketingStreamDataList = (returnEmptyList ? new ArrayList<>() : amazonMarketingStreamDataDao.listByHourly(builder));

        //获取小时对比数据
        List<AmazonMarketingStreamData> compareResponse = null;
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = new HashMap<>();
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            builder.setStart(LocalDate.parse(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), param.getStartDateCompare()), DateTimeFormatter.ISO_LOCAL_DATE));
            builder.setEnd(LocalDate.parse(param.getEndDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE));
            compareResponse = amazonMarketingStreamDataDao.listByHourly(builder);
            hourlyReportDataCompareMap = getIntegerHourlyReportDataMap(compareResponse, hourlyReportDataCompareMap);
        }

        //组装数据
        hourlyReportDataMap = getIntegerHourlyReportDataMap(amazonMarketingStreamDataList, hourlyReportDataMap);
        List<AdCampaignHourVo> voList = new ArrayList<>(24);
        //按24小时返回数据
        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdCampaignHourVo adCampaignHourVo = handleVo(hour, hourlyReportDataMap.get(hour),
                    hourlyReportDataCompareMap.get(hour));
            voList.add(adCampaignHourVo);

        }
        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(voList, adMetricDto);
            filterMetricData(voList, adMetricDto);
        }
        return voList;
    }

    @Deprecated
    @Override
    public List<AdCampaignHourVo> getListByAsin(int puid, CampaignHourParam param) {

        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        //获取小时级数据
        ProductPerspectiveCampaignHourlyRequestPb.ProductPerspectiveCampaignHourlyRequest.Builder builder =
                ProductPerspectiveCampaignHourlyRequestPb.ProductPerspectiveCampaignHourlyRequest.newBuilder();
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setMarketplaceId(shopAuth.getMarketplaceId());
        builder.setStartDate(param.getStartDate());
        builder.setEndDate(param.getEndDate());
        if (StringUtils.isNotBlank(param.getWeeks())) {
            builder.addAllWeekday(StringUtil.splitInt(param.getWeeks(), ","));
        }
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            builder.addCampaignId(param.getCampaignId());
        }
//        if (StringUtils.isNotBlank(param.getAsin())) {
//            List<String> adIdList = amazonAdProductDao.adIdListByAsin(puid, Collections.singletonList(shopAuth.getId()), shopAuth.getMarketplaceId(), param.getAsin());
//            if (CollectionUtils.isEmpty(adIdList)) {
//                return null;
//            }
//            builder.addAllAdId(adIdList);
//        }
        ProductPerspectiveCampaignHourlyResponsePb.ProductPerspectiveCampaignHourlyResponse statisticsByHourResponse = adFeedBlockingStub
                .statisticsProductPerspectiveCampaignHourlyReport(builder.build());

        //获取小时对比数据
        ProductPerspectiveCampaignHourlyResponsePb.ProductPerspectiveCampaignHourlyResponse compareResponse = null;
        Map<Integer, HourlyReportDataPb.HourlyReportData> hourlyReportDataMap = new HashMap<>();
        Map<Integer, HourlyReportDataPb.HourlyReportData> hourlyReportDataCompareMap = new HashMap<>();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            builder.setStartDate(param.getStartDateCompare());
            builder.setEndDate(param.getEndDateCompare());
            compareResponse = adFeedBlockingStub.statisticsProductPerspectiveCampaignHourlyReport(builder.build());
            hourlyReportDataCompareMap = getIntegerHourlyReportDataMap(compareResponse, hourlyReportDataCompareMap);
        }

        //组装数据
        hourlyReportDataMap = getIntegerHourlyReportDataMap(statisticsByHourResponse, hourlyReportDataMap);
        List<AdCampaignHourVo> voList = new ArrayList<>(24);
        //按24小时返回数据
        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdCampaignHourVo adCampaignHourVo = handleVo(hour, hourlyReportDataMap.get(hour),
                    hourlyReportDataCompareMap.get(hour));
            voList.add(adCampaignHourVo);

        }
        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(voList, adMetricDto);
            filterMetricData(voList, adMetricDto);
        }
        return voList;
    }

    @Override
    public List<AdCampaignHourVo> getAggregateHourList(List<ShopAuth> shopAuths, List<String> aggregateIds,
                                                       CampaignAggregateHourParamVO param) {
        if (CollectionUtils.isEmpty(shopAuths)) {
            return new ArrayList<>();
        }
        return amazonAdFeedReportService.listAggregateHourList(shopAuths, aggregateIds, param);
    }

    @Override
    public List<AdCampaignHourVo> getAggregateHourList(List<ShopAuth> shopAuths, CampaignAggregateHourParam param) {
        if (CollectionUtils.isEmpty(shopAuths)) {
            return new ArrayList<>();
        }
        return amazonAdFeedReportService.listAggregateHourList(shopAuths, param);
    }

    private CampaignHourlyReportResponsePb.CampaignHourlyReportResponse multiThreadQueryAms(List<String> ids,
                                                                                            CampaignHourlyRequestPb.CampaignHourlyRequest.Builder paramBuilder) {
        CampaignHourlyRequestPb.CampaignHourlyRequest.Builder queryBuilder = CampaignHourlyRequestPb.CampaignHourlyRequest.newBuilder();
        BeanUtils.copyProperties(paramBuilder, queryBuilder);
        Optional.ofNullable(paramBuilder.getWeekdayList()).map(queryBuilder::addAllWeekday);
        queryBuilder.addAllCampaignId(ids);
        return adFeedBlockingStub.statisticsCampaignHourlyReport(queryBuilder.build());
    }

    private AggregateCampaignHourlyReportResponsePb.AggregateCampaignHourlyReportResponse multiThreadQueryAmsBySellerIds(List<String> ids,
                                                                                                                         AggregateCampaignHourlyReportRequestPb.AggregateCampaignHourlyReportRequest.Builder paramBuilder) {
        if (CollectionUtils.isEmpty(ids) || CollectionUtils.isEmpty(paramBuilder.getAdIdList())) {
            return AggregateCampaignHourlyReportResponsePb.AggregateCampaignHourlyReportResponse.newBuilder().build();
        }
        AggregateCampaignHourlyReportRequestPb.AggregateCampaignHourlyReportRequest.Builder queryBuilder = AggregateCampaignHourlyReportRequestPb.AggregateCampaignHourlyReportRequest.newBuilder();
        BeanUtils.copyProperties(paramBuilder, queryBuilder);
        Optional.ofNullable(paramBuilder.getWeekdayList()).map(queryBuilder::addAllWeekday);
        queryBuilder.addAllCampaignId(ids);
        queryBuilder.addAllAdId(paramBuilder.getAdIdList());
        queryBuilder.addAllSellerId(paramBuilder.getSellerIdList());
        return adFeedBlockingStub.aggregateCampaignHourlyReport(queryBuilder.build());
    }

    private CampaignHourlyReportResponsePb.CampaignHourlyReportResponse mergeResult(List<CampaignHourlyReportResponsePb.
            CampaignHourlyReportResponse> resultList) {
        CampaignHourlyReportResponsePb.CampaignHourlyReportResponse.Builder builder = CampaignHourlyReportResponsePb.
                CampaignHourlyReportResponse.newBuilder();
        if (CollectionUtils.isEmpty(resultList)) return builder.build();
        List<HourlyReportDataPb.HourlyReportData> result = new ArrayList<>();
        //聚合数据
        Collection<HourlyReportDataPb.HourlyReportData> values = resultList.stream().filter(Objects::nonNull).filter(e -> e.getDataCount() > 0)
                .map(CampaignHourlyReportResponsePb.CampaignHourlyReportResponse::getDataList).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                    LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                    return localTime.getHour();
                }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport)).values();
        if (CollectionUtils.isNotEmpty(values)) {
            result = com.google.common.collect.Lists.newArrayList(values);
        }
        //取第一次请求中的参数即可
        Optional.ofNullable(resultList.get(0)).map(CampaignHourlyReportResponsePb.
                CampaignHourlyReportResponse::getSellerId).ifPresent(builder::setSellerId);
        Optional.ofNullable(resultList.get(0)).map(CampaignHourlyReportResponsePb.
                CampaignHourlyReportResponse::getMarketplaceId).ifPresent(builder::setMarketplaceId);
        Optional.ofNullable(resultList.get(0)).map(CampaignHourlyReportResponsePb.
                CampaignHourlyReportResponse::getStartDate).ifPresent(builder::setStartDate);
        Optional.ofNullable(resultList.get(0)).map(CampaignHourlyReportResponsePb.
                CampaignHourlyReportResponse::getEndDate).ifPresent(builder::setEndDate);
        builder.addAllData(result);
        return builder.build();
    }

    private AggregateCampaignHourlyReportResponsePb.AggregateCampaignHourlyReportResponse mergeAggregateCampaignHourlyReportResult(List<AggregateCampaignHourlyReportResponsePb.
            AggregateCampaignHourlyReportResponse> resultList) {
        AggregateCampaignHourlyReportResponsePb.AggregateCampaignHourlyReportResponse.Builder builder = AggregateCampaignHourlyReportResponsePb.AggregateCampaignHourlyReportResponse.newBuilder();
        if (CollectionUtils.isEmpty(resultList)) {
            return builder.build();
        }
        List<HourlyReportDataPb.HourlyReportData> result = new ArrayList<>();
        //聚合数据
        Collection<HourlyReportDataPb.HourlyReportData> values = resultList.stream().filter(Objects::nonNull).filter(e -> e.getDataCount() > 0)
                .map(AggregateCampaignHourlyReportResponsePb.AggregateCampaignHourlyReportResponse::getDataList).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                    LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                    return localTime.getHour();
                }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport)).values();
        if (CollectionUtils.isNotEmpty(values)) {
            result = com.google.common.collect.Lists.newArrayList(values);
        }
        //取第一次请求中的参数即可
        builder.addAllData(result);
        return builder.build();
    }

    @Override
    public List<AdCampaignHourVo> getBudgetList(int puid, CampaignHourParam param) {

        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        //获取小时级数据
        List<AmazonMarketingStreamData> statisticsByHourResponse;
        CampaignHourlyReportSelectDto queryDto = new CampaignHourlyReportSelectDto();
        if (StringUtils.isBlank(param.getFindType()) || StringUtils.isBlank(param.getFindValue())) {
            queryDto.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
            queryDto.setMarketplaceId(shopAuth.getMarketplaceId());
            queryDto.setStartDate(param.getStartDate());
            queryDto.setEndDate(param.getEndDate());
            if (StringUtils.isNotBlank(param.getCampaignId())) {
                queryDto.setCampaignIds(Collections.singletonList(param.getCampaignId()));
            }
            statisticsByHourResponse = amazonMarketingStreamDataDao.getHourReportByCampaignId(queryDto);
        } else {
            queryDto.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
            queryDto.setMarketplaceId(shopAuth.getMarketplaceId());
            queryDto.setStartDate(param.getStartDate());
            queryDto.setEndDate(param.getEndDate());
            List<String> adIdList = odsAmazonAdProductReportDao.adIdListByProduct(puid, Collections.singletonList(shopAuth.getId()), shopAuth.getMarketplaceId(), param.getFindType(), param.getFindValue(), param.getStartDate(), param.getEndDate());
            boolean returnEmptyList = false;
            if (CollectionUtils.isEmpty(adIdList)) {
                returnEmptyList = true;
            }
            queryDto.setAdIds(adIdList);
            if (StringUtils.isNotBlank(param.getCampaignId())) {
                queryDto.setCampaignIds(Collections.singletonList(param.getCampaignId()));
            }

            statisticsByHourResponse = (returnEmptyList ? new ArrayList<>() : amazonMarketingStreamDataDao.productPerspectiveAnalysisGetHourReportByCampaignId(queryDto));
        }


        //获取小时对比数据
//        GetHourReportByCampaignIdResponsePb.GetHourReportByCampaignIdResponse compareResponse = null;
        Map<String, AmazonMarketingStreamData> hourlyReportDataMap = new HashMap<>();
//        Map<String, GetHourReportByCampaignIdResponsePb.GetHourReportByCampaignIdResponse.Item> hourlyReportDataCompareMap = new HashMap<>();
//        if (Integer.valueOf(1).equals(param.getIsCompare())) {
//            builder.setStartDate(param.getStartDateCompare());
//            builder.setEndDate(param.getEndDateCompare());
//            compareResponse = adFeedBlockingStub.getHourReportByCampaignId(builder.build());
//            hourlyReportDataCompareMap = getBudgetHourlyReportDataMap(compareResponse, hourlyReportDataCompareMap);
//        }

        //组装数据
        hourlyReportDataMap = getBudgetHourlyReportDataMap(statisticsByHourResponse, hourlyReportDataMap);
        List<AdCampaignHourVo> voList = new ArrayList<>();
        String startDate = param.getStartDate();
        String endDate = param.getEndDate();
        // 日期格式化
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            // 起始日期
            Date d1 = sdf.parse(startDate);
            // 结束日期
            Date d2 = sdf.parse(endDate);
            Date tmp = d1;
            Calendar dd = Calendar.getInstance();
            dd.setTime(d1);
            while (tmp.getTime() <= d2.getTime()) {
                Date finalTmp = tmp;
                Map<String, AmazonMarketingStreamData> finalHourlyReportDataMap =
                        hourlyReportDataMap;
                HourConvert.twentyFourHoursList.stream().forEachOrdered(item -> {
                    String dateKey = sdf.format(finalTmp) + " " + item;
                    if (null != finalHourlyReportDataMap.get(dateKey)) {
                        AdCampaignHourVo adCampaignHourVo = handleBudgetVo(finalHourlyReportDataMap.get(dateKey));
                        voList.add(adCampaignHourVo);
                    }
                });
                // 天数加上1
                dd.add(Calendar.DAY_OF_MONTH, 1);
                tmp = dd.getTime();
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(voList, adMetricDto);
            filterMetricData(voList, adMetricDto);
        }
        return voList;
    }

    private AdCampaignHourVo handleBudgetVo(AmazonMarketingStreamData data) {
        AdCampaignHourVo vo = new AdCampaignHourVo();
        vo.setLabel(DateUtil.dateToStrWithTime(data.getTimeWindowStart(), "yyyy-MM-dd HH"));
//        AdCampaignHourVo adCampaignHourVoCompare = budgetConvertTo(dataCompare);
        AdCampaignHourVo adCampaignHourVo = budgetConvertTo(data);
        vo.setAdSale(adCampaignHourVo.getAdSale());
        vo.setAdSelfSale(adCampaignHourVo.getAdSelfSale());
        vo.setAdOtherSale(adCampaignHourVo.getAdOtherSale());
        vo.setAdOrderNum(adCampaignHourVo.getAdOrderNum());
        vo.setSelfAdOrderNum(adCampaignHourVo.getSelfAdOrderNum());
        vo.setOtherAdOrderNum(adCampaignHourVo.getOtherAdOrderNum());
        vo.setAdSaleNum(adCampaignHourVo.getAdSaleNum());
        vo.setAdSelfSaleNum(adCampaignHourVo.getAdSelfSaleNum());
        vo.setAdOtherSaleNum(adCampaignHourVo.getAdOtherSaleNum());
        vo.setAdCost(adCampaignHourVo.getAdCost());
        vo.setClicks(adCampaignHourVo.getClicks());
        vo.setImpressions(adCampaignHourVo.getImpressions());
        vo.setAdCostPerClick(adCampaignHourVo.getAdCostPerClick());
        vo.setCpa(adCampaignHourVo.getCpa());
        vo.setAcos(adCampaignHourVo.getAcos());
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        return vo;
    }

    /**
     * 组装返回值vo
     *
     * @param hour        小时区间
     * @param data        当前小时维度数据
     * @param dataCompare 对比区间小时维度数据
     * @return
     */
    private AdCampaignHourVo handleVo(Integer hour, HourlyReportDataPb.HourlyReportData data,
                                      HourlyReportDataPb.HourlyReportData dataCompare) {
        AdCampaignHourVo adCampaignHourVoCompare = convertTo(dataCompare);
        AdCampaignHourVo adCampaignHourVo = convertTo(data);
        AdCampaignHourVo vo = AdCampaignHourVo.builder()
                .label(HourConvert.hourMap.get(hour))
                .hour(hour)
                .adSale(adCampaignHourVo.getAdSale())
                .adSelfSale(adCampaignHourVo.getAdSelfSale())
                .adOtherSale(adCampaignHourVo.getAdOtherSale())
                .adOrderNum(adCampaignHourVo.getAdOrderNum())
                .selfAdOrderNum(adCampaignHourVo.getSelfAdOrderNum())
                .otherAdOrderNum(adCampaignHourVo.getOtherAdOrderNum())
                .adSaleNum(adCampaignHourVo.getAdSaleNum())
                .adSelfSaleNum(adCampaignHourVo.getAdSelfSaleNum())
                .adOtherSaleNum(adCampaignHourVo.getAdOtherSaleNum())
                .adCost(adCampaignHourVo.getAdCost())
                .clicks(adCampaignHourVo.getClicks())
                .impressions(adCampaignHourVo.getImpressions())
                .adCostPerClick(adCampaignHourVo.getAdCostPerClick())
                .cpa(adCampaignHourVo.getCpa())
                .acos(adCampaignHourVo.getAcos())
                .ctr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adCampaignHourVo.getClicks()), BigDecimal.valueOf(100)),
                        BigDecimal.valueOf(adCampaignHourVo.getImpressions())))
                .cvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adCampaignHourVo.getAdOrderNum()),
                        BigDecimal.valueOf(100)), BigDecimal.valueOf(adCampaignHourVo.getClicks())))
                .roas(Objects.isNull(adCampaignHourVo.getAdSale()) || BigDecimal.ZERO.compareTo(adCampaignHourVo.getAdSale()) == 0 || BigDecimal.ZERO.compareTo(adCampaignHourVo.getAdCost()) == 0 ?
                        BigDecimal.ZERO : adCampaignHourVo.getAdSale().divide(adCampaignHourVo.getAdCost(), 4, RoundingMode.HALF_UP))
                .clicksCompare(adCampaignHourVoCompare.getClicks())
                .impressionsCompare(adCampaignHourVoCompare.getImpressions())
                .adSaleNumCompare(adCampaignHourVoCompare.getAdOrderNum())
                .adSaleCompare(adCampaignHourVoCompare.getAdSale())
                .adCostCompare(adCampaignHourVoCompare.getAdCost())
                .adOrderNumCompare(adCampaignHourVoCompare.getAdOrderNum())
                .build();
        vo.afterPropertiesSet();//为各对比率属性设值
        return vo;
    }

    /**
     * 组装返回值vo
     *
     * @param hour        小时区间
     * @param data        当前小时维度数据
     * @param dataCompare 对比区间小时维度数据
     * @return
     */
    private AdCampaignHourVo handleVo(Integer hour, AmazonMarketingStreamData data, AmazonMarketingStreamData dataCompare) {
        AdCampaignHourVo adCampaignHourVoCompare = convertTo(dataCompare);
        AdCampaignHourVo adCampaignHourVo = convertTo(data);
        AdCampaignHourVo vo = AdCampaignHourVo.builder()
                .label(HourConvert.hourMap.get(hour))
                .hour(hour)
                .adSale(adCampaignHourVo.getAdSale())
                .adSelfSale(adCampaignHourVo.getAdSelfSale())
                .adOtherSale(adCampaignHourVo.getAdOtherSale())
                .adOrderNum(adCampaignHourVo.getAdOrderNum())
                .selfAdOrderNum(adCampaignHourVo.getSelfAdOrderNum())
                .otherAdOrderNum(adCampaignHourVo.getOtherAdOrderNum())
                .adSaleNum(adCampaignHourVo.getAdSaleNum())
                .adSelfSaleNum(adCampaignHourVo.getAdSelfSaleNum())
                .adOtherSaleNum(adCampaignHourVo.getAdOtherSaleNum())
                .adCost(adCampaignHourVo.getAdCost())
                .clicks(adCampaignHourVo.getClicks())
                .impressions(adCampaignHourVo.getImpressions())
                .adCostPerClick(adCampaignHourVo.getAdCostPerClick())
                .cpa(adCampaignHourVo.getCpa())
                .acos(adCampaignHourVo.getAcos())
                .ctr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adCampaignHourVo.getClicks()), BigDecimal.valueOf(100)),
                        BigDecimal.valueOf(adCampaignHourVo.getImpressions())))
                .cvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adCampaignHourVo.getAdOrderNum()),
                        BigDecimal.valueOf(100)), BigDecimal.valueOf(adCampaignHourVo.getClicks())))
                .roas(Objects.isNull(adCampaignHourVo.getAdSale()) || BigDecimal.ZERO.compareTo(adCampaignHourVo.getAdSale()) == 0 || BigDecimal.ZERO.compareTo(adCampaignHourVo.getAdCost()) == 0 ?
                        BigDecimal.ZERO : adCampaignHourVo.getAdSale().divide(adCampaignHourVo.getAdCost(), 4, RoundingMode.HALF_UP))
                .clicksCompare(adCampaignHourVoCompare.getClicks())
                .impressionsCompare(adCampaignHourVoCompare.getImpressions())
                .adSaleNumCompare(adCampaignHourVoCompare.getAdOrderNum())
                .adSaleCompare(adCampaignHourVoCompare.getAdSale())
                .adCostCompare(adCampaignHourVoCompare.getAdCost())
                .adOrderNumCompare(adCampaignHourVoCompare.getAdOrderNum())
                .adCostPerClickCompare(adCampaignHourVoCompare.getAdCostPerClick())
                .cpaCompare(adCampaignHourVoCompare.getCpa())
                .ctrCompare(adCampaignHourVoCompare.getCtr())
                .cvrCompare(adCampaignHourVoCompare.getCvr())
                .acosCompare(adCampaignHourVoCompare.getAcos())
                .roasCompare(adCampaignHourVoCompare.getRoas())
                .build();
        vo.afterPropertiesSet();//为各对比率属性设值
        return vo;
    }

    @Override
    public List<AdCampaignHourVo> getDayList(int puid, CampaignHourParam param) {
        return getDayWeekOrMonth(puid, param, x -> x);
    }


    @Override
    public List<AdCampaignHourVo> getDayCompareList(int puid, CampaignHourParam param) {
        List<AdCampaignHourVo> reports = getDayWeekOrMonthCompare(puid, param, x -> x, false);
        if (param.getIsCompare() != null && param.getIsCompare() == 1) {
            List<AdCampaignHourVo> compareReports = getDayWeekOrMonthCompare(puid, param, x -> x, true);
            return paddingDayAdCampaignHourVoCompare(param, reports, compareReports);
        } else {
            return reports;
        }
    }


    @Override
    public List<AdCampaignHourVo> getWeekCompareList(int puid, CampaignHourParam param) {
        List<AdCampaignHourVo> reports = getWeekList(puid, param, false);

        if (param.getIsCompare() != null && param.getIsCompare() == 1) {
            List<AdCampaignHourVo> compareReports = getWeekList(puid, param, true);
            return paddingWeekAdCampaignHourVoCompare(reports, compareReports);
        }else{
            return reports;
        }
    }


    @Override
    public List<AdCampaignHourVo> getWeekList(int puid, CampaignHourParam param) {
        return getWeekList(puid, param, false);
    }


    private List<AdCampaignHourVo> getWeekList(int puid, CampaignHourParam param, boolean isCompare) {
        List<AdCampaignHourVo> reports = getAdCampaignDailyReports(puid, param, isCompare);
        String start = param.getStartDate();
        String end = param.getEndDate();
        if (isCompare) {
            start = param.getStartDateCompare();
            end = param.getEndDateCompare();
        }
        reports = ReportChartUtil.getCampaignWeekReportVos(start, end, reports);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }
        return reports;
    }


    @Override
    public List<AdCampaignHourVo> getMonthList(int puid, CampaignHourParam param) {
        return getDayWeekOrMonth(puid, param, ReportChartUtil::getCampaignMonthReportVos);
    }


    @Override
    public List<AdCampaignHourVo> getMonthCompareList(int puid, CampaignHourParam param) {
        List<AdCampaignHourVo> reports = getDayWeekOrMonthCompare(puid, param, ReportChartUtil::getCampaignMonthReportVos, false);
        if (param.getIsCompare() != null && param.getIsCompare() == 1) {
            List<AdCampaignHourVo> compareReports = getDayWeekOrMonthCompare(puid, param, ReportChartUtil::getCampaignMonthReportVos, true);
            return paddingMothAdCampaignHourVoCompare(param, reports, compareReports);
        } else {
            return reports;
        }
    }

    public List<AdCampaignHourVo> getDayWeekOrMonth(int puid, CampaignHourParam param,
                                                    Function<List<AdCampaignHourVo>, List<AdCampaignHourVo>> function) {
        return getDayWeekOrMonthCompare(puid, param, function, false);
    }



    public List<AdCampaignHourVo> getDayWeekOrMonthCompare(int puid, CampaignHourParam param,
                                                    Function<List<AdCampaignHourVo>, List<AdCampaignHourVo>> function, boolean isCompare) {
        List<AdCampaignHourVo> reports = getAdCampaignDailyReports(puid, param, isCompare);
        reports = function.apply(reports);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }
        return reports;
    }




    private List<AdCampaignHourVo> paddingDayAdCampaignHourVoCompare(CampaignHourParam param, List<AdCampaignHourVo> day, List<AdCampaignHourVo> dayComparison) {
        //天的数据比较特殊，列表数据不用返回每一天数据，所以对比是要对应取值
        //从开始时间遍历到结束时间，对比值有值同样要返回该条数据
        LocalDate start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate startCompare = LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE);
        List<AdCampaignHourVo> reportHourlyVOS = new ArrayList<>();
        //将两个数组抓换成 Map 按日期
        Map<String, AdCampaignHourVo> dayMap = day.stream().collect(Collectors.toMap(AdCampaignHourVo::getLabel, Function.identity()));
        Map<String, AdCampaignHourVo> dayCompareMap = dayComparison.stream().collect(Collectors.toMap(AdCampaignHourVo::getLabel, Function.identity()));

        for (; !start.isAfter(end); start = start.plusDays(1),startCompare = startCompare.plusDays(1)) {
            AdCampaignHourVo adReportHourlyVO = dayMap.get(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
            AdCampaignHourVo adReportHourlyVOCompare = dayCompareMap.get(startCompare.format(DateTimeFormatter.ISO_LOCAL_DATE));
            if (adReportHourlyVO == null && adReportHourlyVOCompare == null) {
                continue;
            }
            AdCampaignHourVo vo;
            if (adReportHourlyVO == null) {
                vo = new AdCampaignHourVo();
                vo.setLabel(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
            } else {
                vo = adReportHourlyVO;
            }
            setCompare(vo, adReportHourlyVOCompare);
            reportHourlyVOS.add(vo);
        }
        return reportHourlyVOS;
    }


    private List<AdCampaignHourVo> paddingMothAdCampaignHourVoCompare(CampaignHourParam param, List<AdCampaignHourVo> moth, List<AdCampaignHourVo> dayComparison) {
        //天的数据比较特殊，列表数据不用返回每一天数据，所以对比是要对应取值
        //从开始时间遍历到结束时间，对比值有值同样要返回该条数据
        LocalDate start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate startCompare = LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE);
        List<AdCampaignHourVo> reportHourlyVOS = new ArrayList<>();
        //将两个数组抓换成 Map 按日期
        Map<String, AdCampaignHourVo> dayMap = moth.stream().collect(Collectors.toMap(AdCampaignHourVo::getLabel, Function.identity()));
        Map<String, AdCampaignHourVo> dayCompareMap = dayComparison.stream().collect(Collectors.toMap(AdCampaignHourVo::getLabel, Function.identity()));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        for (; !YearMonth.from(start).isAfter(YearMonth.from(end)); start = start.plusMonths(1), startCompare = startCompare.plusMonths(1)) {
            AdCampaignHourVo adReportHourlyVO = dayMap.get(start.format(formatter));
            AdCampaignHourVo adReportHourlyVOCompare = dayCompareMap.get(startCompare.format(formatter));
            if (adReportHourlyVO == null && adReportHourlyVOCompare == null) {
                continue;
            }
            AdCampaignHourVo vo;
            if (adReportHourlyVO == null) {
                vo = new AdCampaignHourVo();
                vo.setLabel(start.format(formatter));
                vo.setDate(vo.getLabel());
            } else {
                vo = adReportHourlyVO;
            }
            setCompare(vo, adReportHourlyVOCompare);
            reportHourlyVOS.add(vo);
        }
        return reportHourlyVOS;
    }

    private List<AdCampaignHourVo> paddingWeekAdCampaignHourVoCompare(List<AdCampaignHourVo> weekMoths, List<AdCampaignHourVo> weekMothComparisons) {
        //日周月已经按照时间全部填充，我们只需要一一对应对比就可以了
        if (CollectionUtils.isEmpty(weekMoths)) {
            return weekMoths;
        }
        for (int i = 0; i < weekMoths.size() && i < weekMothComparisons.size(); i++) {
            setCompare(weekMoths.get(i), weekMothComparisons.get(i));
        }
        return weekMoths;
    }


    private void setCompare(AdCampaignHourVo vo, AdCampaignHourVo voCompare){
        //对比值
        if (voCompare != null) {
            vo.setAdCostCompare(voCompare.getAdCost());
            vo.setImpressionsCompare(voCompare.getImpressions());
            vo.setClicksCompare(voCompare.getClicks());
            vo.setCpaCompare(voCompare.getCpa());
            vo.setAdCostPerClickCompare(voCompare.getAdCostPerClick());
            vo.setCtrCompare(voCompare.getCtr());
            vo.setCvrCompare(voCompare.getCvr());
            vo.setAcosCompare(voCompare.getAcos());
            vo.setRoasCompare(voCompare.getRoas());
            vo.setAdOrderNumCompare(voCompare.getAdOrderNum());
            vo.setSelfAdOrderNumCompare(voCompare.getSelfAdOrderNum());
            vo.setOtherAdOrderNumCompare(voCompare.getOtherAdOrderNum());
            vo.setAdSaleCompare(voCompare.getAdSale());
            vo.setAdSelfSaleCompare(voCompare.getAdSelfSale());
            vo.setAdOtherSaleCompare(voCompare.getAdOtherSale());
            vo.setAdSaleNumCompare(voCompare.getAdSaleNum());
            vo.setAdSelfSaleNumCompare(voCompare.getAdSelfSaleNum());
            vo.setAdOtherSaleNumCompare(voCompare.getAdOtherSaleNum());
            vo.setAcotsCompare(voCompare.getAcots());
            vo.setAsotsCompare(voCompare.getAsots());
            vo.afterPropertiesSet();
        }
    }


//    private List<AdReportHourlyVO> paddingDayCompare(AdHourReportRequest param, List<AdReportHourlyVO> day, List<AdReportHourlyVO> dayComparison) {
//        //天的数据比较特殊，列表数据不用返回每一天数据，所以对比是要对应取值
//        //从开始时间遍历到结束时间，对比值有值同样要返回该条数据
//        AdPageBasicData pageBasic = param.getPageBasic();
//        LocalDate start = LocalDate.parse(pageBasic.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
//        LocalDate end = LocalDate.parse(pageBasic.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
//        LocalDate startCompare = LocalDate.parse(pageBasic.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE);
//        LocalDate endCompare = LocalDate.parse(pageBasic.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
//        List<AdReportHourlyVO> reportHourlyVOS = new ArrayList<>();
//        //将两个数组抓换成 Map 按日期
//        Map<String, AdReportHourlyVO> dayMap = day.stream().collect(Collectors.toMap(AdReportHourlyVO::getLabel, Function.identity()));
//        Map<String, AdReportHourlyVO> dayCompareMap = dayComparison.stream().collect(Collectors.toMap(AdReportHourlyVO::getLabel, Function.identity()));
//
//        for (; start.compareTo(end) < 0; start = start.plusDays(1),startCompare = startCompare.plusDays(1)) {
//            AdReportHourlyVO adReportHourlyVO = dayMap.get(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
//            AdReportHourlyVO adReportHourlyVOCompare = dayCompareMap.get(startCompare.format(DateTimeFormatter.ISO_LOCAL_DATE));
//            if (adReportHourlyVO == null && adReportHourlyVOCompare == null) {
//                continue;
//            }
//            AdReportHourlyVO vo;
//            if (adReportHourlyVO == null) {
//                vo = new AdReportHourlyVO();
//                vo.setLabel(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
//                vo.setDate(vo.getLabel());
//            } else {
//                vo = adReportHourlyVO;
//            }
//            setCompare(vo, adReportHourlyVOCompare);
//            reportHourlyVOS.add(vo);
//        }
//        return reportHourlyVOS;
//    }


//    private List<AdReportHourlyVO> paddingMothCompare(AdHourReportRequest param, List<AdReportHourlyVO> moth, List<AdReportHourlyVO> dayComparison) {
//        //天的数据比较特殊，列表数据不用返回每一天数据，所以对比是要对应取值
//        //从开始时间遍历到结束时间，对比值有值同样要返回该条数据
//        AdPageBasicData pageBasic = param.getPageBasic();
//        LocalDate start = LocalDate.parse(pageBasic.getStartDate(), DateTimeFormatter.BASIC_ISO_DATE);
//        LocalDate end = LocalDate.parse(pageBasic.getEndDate(), DateTimeFormatter.BASIC_ISO_DATE);
//        LocalDate startCompare = LocalDate.parse(pageBasic.getStartDateCompare(), DateTimeFormatter.BASIC_ISO_DATE);
//        LocalDate endCompare = LocalDate.parse(pageBasic.getEndDate(), DateTimeFormatter.BASIC_ISO_DATE);
//        List<AdReportHourlyVO> reportHourlyVOS = new ArrayList<>();
//        //将两个数组抓换成 Map 按日期
//        Map<String, AdReportHourlyVO> dayMap = moth.stream().collect(Collectors.toMap(AdReportHourlyVO::getLabel, Function.identity()));
//        Map<String, AdReportHourlyVO> dayCompareMap = dayComparison.stream().collect(Collectors.toMap(AdReportHourlyVO::getLabel, Function.identity()));
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
//        for (; !YearMonth.from(start).isAfter(YearMonth.from(end)); ) {
//            AdReportHourlyVO adReportHourlyVO = dayMap.get(start.format(formatter));
//            AdReportHourlyVO adReportHourlyVOCompare = dayCompareMap.get(startCompare.format(formatter));
//            if (adReportHourlyVO == null && adReportHourlyVOCompare == null) {
//                continue;
//            }
//            AdReportHourlyVO vo;
//            if (adReportHourlyVO == null) {
//                vo = new AdReportHourlyVO();
//                vo.setLabel(start.format(formatter));
//                vo.setDate(vo.getLabel());
//            } else {
//                vo = adReportHourlyVO;
//            }
//            setCompare(vo, adReportHourlyVOCompare);
//            start = start.plusMonths(1);
//            startCompare = startCompare.plusMonths(1);
//            reportHourlyVOS.add(vo);
//        }
//        return reportHourlyVOS;
//    }


//    private void setCompare(AdReportHourlyVO vo, AdReportHourlyVO voCompare){
//        //对比值
//        if (voCompare != null) {
//            vo.setAdCostCompare(voCompare.getAdCost());
//            vo.setImpressionsCompare(voCompare.getImpressions());
//            vo.setClicksCompare(voCompare.getClicks());
//            vo.setCpaCompare(voCompare.getCpa());
//            vo.setAdCostPerClickCompare(voCompare.getAdCostPerClick());
//            vo.setCtrCompare(voCompare.getCtr());
//            vo.setCvrCompare(voCompare.getCvr());
//            vo.setAcosCompare(voCompare.getAcos());
//            vo.setRoasCompare(voCompare.getRoas());
//            vo.setAdOrderNumCompare(voCompare.getAdOrderNum());
//            vo.setSelfAdOrderNumCompare(voCompare.getSelfAdOrderNum());
//            vo.setOtherAdOrderNumCompare(voCompare.getOtherAdOrderNum());
//            vo.setAdSaleCompare(voCompare.getAdSale());
//            vo.setAdSelfSaleCompare(voCompare.getAdSelfSale());
//            vo.setAdOtherSaleCompare(voCompare.getAdOtherSale());
//            vo.setAdSaleNumCompare(voCompare.getAdSaleNum());
//            vo.setAdSelfSaleNumCompare(voCompare.getAdSelfSaleNum());
//            vo.setAdOtherSaleNumCompare(voCompare.getAdOtherSaleNum());
//            vo.setAcotsCompare(voCompare.getAcots());
//            vo.setAsotsCompare(voCompare.getAsots());
//            vo.afterPropertiesSet();
//        }
//    }



    @Override
    public List<AdCampaignWeekDayVo> getWeeklySuperpositionList(int puid, CampaignHourParam param) {
        return convertToHourOfWeekDayVos(getWeeklySuperpositionDailyList(puid, param));
    }

    @Override
    public List<AdCampaignWeekDayVo> getAggregateWeeklySuperpositionList(List<ShopAuth> shopAuths, int puid, CampaignHourParam param) {
        //需要获取暂存的所有活动campaignId列表，进行传递
        List<String> aggregateIds = cpcPageIdsHandler.getCampaignIdsTemporary(puid, param.getPageSign(),
                "", param.getShopId(), new CampaignHourParam[]{param});
        // 多店鋪,超过限制广告数量返回，防止doris cpu过高
        if((shopAuths.size() > 1 && aggregateIds.size() >= adManageLimitConfig.getHourLimit()) || shopAuths.size() > 40){
            if(shopAuths.size() > 40){
                throw new SponsoredBizException("当前店铺数量超过40个，请减少后重新查询!");
            }else{
                throw new SponsoredBizException("当前所选数据量过大，请减少店铺查询!");
            }
        }
        return convertToWeekDayVos(amazonAdFeedReportService.listAggregateWeekList(shopAuths, aggregateIds, param));
    }

    @Override
    public List<AdCampaignHourVo> getAggregateDayList(int puid, List<String> aggregateIds, CampaignHourParam param, List<AdCampaignHourVo> compares) {
        List<AdCampaignHourVo> dayList = getAggregateDayWeekOrMonth(puid, aggregateIds, param, x -> x);
        if (!Integer.valueOf(1).equals(param.getIsCompare())) {
            return dayList;
        } else {
            String startDate = param.getStartDate();
            String endDate = param.getEndDate();
            param.setStartDate(param.getStartDateCompare());
            param.setEndDate(param.getEndDateCompare());
            List<AdCampaignHourVo> compareList = getAggregateDayWeekOrMonth(puid, aggregateIds, param, x -> x);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            param.setStartDate(startDate);
            param.setEndDate(endDate);
            return paddingDayCompare(param, dayList, compareList);
        }
    }

    private List<AdCampaignHourVo> paddingDayCompare(CampaignHourParam param, List<AdCampaignHourVo> dayList, List<AdCampaignHourVo> compareList) {
        LocalDate start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate startCompare = LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE);
        List<AdCampaignHourVo> reportHourlyVOS = new ArrayList<>();
        Map<String, AdCampaignHourVo> dayMap = StreamUtil.toMap(dayList, AdCampaignHourVo::getLabel);
        Map<String, AdCampaignHourVo> dayCompareMap = StreamUtil.toMap(compareList, AdCampaignHourVo::getLabel);
        for (; !start.isAfter(end); start = start.plusDays(1), startCompare = startCompare.plusDays(1)) {
            AdCampaignHourVo adReportHourlyVO = dayMap.get(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
            AdCampaignHourVo adReportHourlyVOCompare = dayCompareMap.get(startCompare.format(DateTimeFormatter.ISO_LOCAL_DATE));
            if (adReportHourlyVO == null && adReportHourlyVOCompare == null) {
                continue;
            }
            AdCampaignHourVo vo;
            if (adReportHourlyVO == null) {
                vo = new AdCampaignHourVo();
                vo.setLabel(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
            } else {
                vo = adReportHourlyVO;
            }
            vo.compareDataSet(adReportHourlyVOCompare);
            reportHourlyVOS.add(vo);
        }
        return reportHourlyVOS;
    }

    @Override
    public List<AdCampaignHourVo> getAggregateWeekList(int puid, List<String> aggregateIds, CampaignHourParam param, List<AdCampaignHourVo> compares) {
        List<AdCampaignHourVo> reports = getAdCampaignListDailyReports(puid, aggregateIds, param);
        reports = ReportChartUtil.getCampaignWeekReportVos(param.getStartDate(), param.getEndDate(), reports);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }

        if (!Integer.valueOf(1).equals(param.getIsCompare())) {
            return reports;
        } else {
            String startDate = param.getStartDate();
            String endDate = param.getEndDate();
            param.setStartDate(param.getStartDateCompare());
            param.setEndDate(param.getEndDateCompare());
            List<AdCampaignHourVo> compareList = getAdCampaignListDailyReports(puid, aggregateIds, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            param.setStartDate(startDate);
            param.setEndDate(endDate);
            compareList = ReportChartUtil.getCampaignWeekReportVos(param.getStartDateCompare(), param.getEndDateCompare(), compareList);
            return paddingWeekCompare(reports, compareList);
        }
    }

    private List<AdCampaignHourVo> paddingWeekCompare(List<AdCampaignHourVo> weekList, List<AdCampaignHourVo> compareList) {
        if (CollectionUtils.isEmpty(weekList)) {
            return weekList;
        }
        for (int i = 0; i < weekList.size() && i < compareList.size(); i++) {
            weekList.get(i).compareDataSet(compareList.get(i));
        }
        return weekList;
    }

    @Override
    public List<AdCampaignHourVo> getAggregateMonthList(int puid, List<String> aggregateIds, CampaignHourParam param, List<AdCampaignHourVo> compares) {
        List<AdCampaignHourVo> reports = getAggregateDayWeekOrMonth(puid, aggregateIds, param, ReportChartUtil::getCampaignMonthReportVos);
        if (!Integer.valueOf(1).equals(param.getIsCompare())) {
            return reports;
        }else {
            String startDate = param.getStartDate();
            String endDate = param.getEndDate();
            param.setStartDate(param.getStartDateCompare());
            param.setEndDate(param.getEndDateCompare());
            List<AdCampaignHourVo> compareList = getAggregateDayWeekOrMonth(puid, aggregateIds, param, ReportChartUtil::getCampaignMonthReportVos);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            param.setStartDate(startDate);
            param.setEndDate(endDate);
            return paddingMonthCompare(param, reports, compareList);
        }
    }

    private List<AdCampaignHourVo> paddingMonthCompare(CampaignHourParam param, List<AdCampaignHourVo> dayList, List<AdCampaignHourVo> compareList) {
        LocalDate start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate startCompare = LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE);
        List<AdCampaignHourVo> reportHourlyVOS = new ArrayList<>();
        Map<String, AdCampaignHourVo> dayMap = StreamUtil.toMap(dayList, AdCampaignHourVo::getLabel);
        Map<String, AdCampaignHourVo> dayCompareMap = StreamUtil.toMap(compareList, AdCampaignHourVo::getLabel);
        DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
        for (; !YearMonth.from(start).isAfter(YearMonth.from(end));
             start = start.plusMonths(1), startCompare = startCompare.plusMonths(1)) {
            AdCampaignHourVo adReportHourlyVO = dayMap.get(start.format(monthFormatter));
            AdCampaignHourVo adReportHourlyVOCompare = dayCompareMap.get(startCompare.format(monthFormatter));
            if (adReportHourlyVO == null && adReportHourlyVOCompare == null) {
                continue;
            }
            AdCampaignHourVo vo;
            if (adReportHourlyVO == null) {
                vo = new AdCampaignHourVo();
                vo.setLabel(start.format(monthFormatter));
                vo.setDate(vo.getLabel());
            } else {
                vo = adReportHourlyVO;
            }
            vo.compareDataSet(adReportHourlyVOCompare);
            reportHourlyVOS.add(vo);
        }
        return reportHourlyVOS;
    }

    public List<AdCampaignHourVo> getAggregateDayWeekOrMonth(int puid, List<String> aggregateIds, CampaignHourParam param,
                                                             Function<List<AdCampaignHourVo>, List<AdCampaignHourVo>> function) {
        List<AdCampaignHourVo> reports = getAdCampaignListDailyReports(puid, aggregateIds, param);
        reports = reports.stream().filter(Objects::nonNull).sorted((r1, r2) ->
                        DateUtil.compareDate(DateUtil.strToDate(r1.getDate(), "yyyy-MM-dd"), DateUtil.strToDate(r2.getDate(), "yyyy-MM-dd")))
                .collect(Collectors.toList());
        reports = function.apply(reports);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }
        return reports;
    }

    @Override
    public List<AdCampaignHourVo> getWeeklySuperpositionDailyList(int puid, CampaignHourParam param) {
        //校验店铺信息
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        //获取小时级数据
        CampaignHourlyReportSelectDto builder = new CampaignHourlyReportSelectDto();
        builder.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
        builder.setMarketplaceId(shopAuth.getMarketplaceId());
        builder.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), param.getStartDate()));
        builder.setEndDate(param.getEndDate());
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            builder.setCampaignIds(Collections.singletonList(param.getCampaignId()));
        }

        List<AmazonMarketingStreamData> dataList = amazonMarketingStreamDataDao.statisticsByWeek(builder);
        List<AdCampaignHourVo> voList = dataList.stream().map(this::convertTo).collect(Collectors.toList());

        Map<Integer, List<AdCampaignHourVo>> compareMap = Maps.newHashMap();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            builder.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), param.getStartDateCompare()));
            builder.setEndDate(param.getEndDateCompare());
            List<AmazonMarketingStreamData> compareDataList = amazonMarketingStreamDataDao.statisticsByWeek(builder);
            List<AdCampaignHourVo> compareList = compareDataList.stream().map(this::convertTo).collect(Collectors.toList());
            compareMap.putAll(StreamUtil.groupingBy(compareList, AdCampaignHourVo::getWeekDay));
        }

        //填充无数据的时间段
        List<Integer> allWeeks = HourConvert.weeKs;
        List<Integer> weekList = voList.stream().map(AdCampaignHourVo::getWeekDay).collect(Collectors.toList());
        Map<Integer, List<AdCampaignHourVo>> voMap =
                voList.stream().collect(Collectors.groupingBy(AdCampaignHourVo::getWeekDay));
        List<Integer> needFilledWeek = allWeeks.stream().filter(item -> !weekList.contains(item)).collect(Collectors.toList());
        needFilledWeek.forEach(e -> {
            List<AdCampaignHourVo> adCampaignHourVos = new ArrayList<>();
            voMap.put(e, adCampaignHourVos);
        });
        //所有时间
        Collection<String> allHours = HourConvert.hourMap.values();

        voMap.forEach((k, v) -> {
            List<AdCampaignHourVo> adCampaignHourVos = compareMap.get(k);
            Map<String, AdCampaignHourVo> compareHourMap = StreamUtil.toMap(adCampaignHourVos, AdCampaignHourVo::getLabel);

            List<String> hourList = v.stream().map(AdCampaignHourVo::getLabel).collect(Collectors.toList());
            List<String> needFilledHour = allHours.stream()
                    .filter(item -> !hourList.contains(item)).collect(Collectors.toList());
            for (String hour : needFilledHour) {
                AdCampaignHourVo vo = new AdCampaignHourVo();
                vo.setLabel(hour);
                vo.setHour(Integer.valueOf(hour.split("-")[0]));
                vo.setWeekDay(k);
                voList.add(vo);
                if (compareHourMap.containsKey(hour)) {
                    vo.compareDataSet(compareHourMap.get(hour));
                }
            }
            v.forEach(e -> {
                if (compareHourMap.containsKey(e.getLabel())) {
                    e.compareDataSet(compareHourMap.get(e.getLabel()));
                }
            });
        });

        return voList.stream().sorted(Comparator.comparingInt(AdCampaignHourVo::getWeekDay)
                        .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
                .collect(Collectors.toList());
    }

    @Override
    public List<AdCampaignHourVo> getAggregateWeeklySuperpositionDailyList(int puid, List<String> aggregateIds,
                                                                           CampaignHourParam param) {
        //校验店铺信息
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        //获取小时级数据
        CampaignWeeklyRequestPb.CampaignWeeklyRequest.Builder builder =
                CampaignWeeklyRequestPb.CampaignWeeklyRequest.newBuilder();
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setMarketplaceId(shopAuth.getMarketplaceId());
        builder.setStartDate(param.getStartDate());
        builder.setEndDate(param.getEndDate());
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            builder.addCampaignId(param.getCampaignId());
        }

        List<CampaignWeeklyReportResponsePb.CampaignWeeklyReportResponse> resultList = multiThreadQueryAndMergeUtil.
                multiThreadQuery(ThreadPoolUtil.getCpcAggregateIdsSyncPool(),
                        () -> aggregateIds, builder, this::multiThreadQueryAmsWeeklyExport);
        CampaignWeeklyReportResponsePb.CampaignWeeklyReportResponse response = mergerAmsWeeklyExport(resultList);

        //pb对象转vo对象
        List<HourlyReportDataPb.HourlyReportData> dataList = response.getDataList();
        List<AdCampaignHourVo> voList = dataList.stream().map(this::convertTo).collect(Collectors.toList());
        //填充无数据的时间段
        List<Integer> allWeeks = HourConvert.weeKs;
        List<Integer> weekList = voList.stream().map(AdCampaignHourVo::getWeekDay).collect(Collectors.toList());
        Map<Integer, List<AdCampaignHourVo>> voMap =
                voList.stream().collect(Collectors.groupingBy(AdCampaignHourVo::getWeekDay));
        List<Integer> needFilledWeek = allWeeks.stream().filter(item -> !weekList.contains(item)).collect(Collectors.toList());
        needFilledWeek.forEach(e -> {
            List<AdCampaignHourVo> adCampaignHourVos = new ArrayList<>();
            voMap.put(e, adCampaignHourVos);
        });
        //所有时间
        Collection<String> allHours = HourConvert.hourMap.values();

        voMap.forEach((k, v) -> {
            List<String> hourList = v.stream().map(AdCampaignHourVo::getLabel).collect(Collectors.toList());
            List<String> needFilledHour = allHours.stream()
                    .filter(item -> !hourList.contains(item)).collect(Collectors.toList());
            for (String hour : needFilledHour) {
                AdCampaignHourVo vo = new AdCampaignHourVo();
                vo.setLabel(hour);
                vo.setHour(Integer.valueOf(hour.split("-")[0]));
                vo.setWeekDay(k);
                voList.add(vo);
            }
        });

        return voList.stream().sorted(Comparator.comparingInt(AdCampaignHourVo::getWeekDay)
                        .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
                .collect(Collectors.toList());
    }

    @Override
    public List<AdReportHourlyVO> getAdCampaignDailyReports(int puid, AdHourReportRequest param, String type, boolean isCompare) {
        AdPageBasicData pageBasicData = param.getPageBasic();
        LocalDate start = LocalDate.parse(pageBasicData.getStartDate());
        LocalDate end = LocalDate.parse(pageBasicData.getEndDate());
        Integer shopId = Optional.of(pageBasicData.getShopId()).map(Int32Value::getValue).orElse(null);
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (isCompare) {
            if (!pageBasicData.hasEndDateCompare() || !pageBasicData.hasStartDateCompare()) {
                //查询对比但是对比时间为空返回空list；
                return new ArrayList<>();
            }
            start = LocalDate.parse(pageBasicData.getStartDateCompare());
            end = LocalDate.parse(pageBasicData.getEndDateCompare());

        }
        //日,周,月报告数据
        List<AmazonAdCampaignAllReport> reports =
                amazonAdCampaignAllReportDao.getReportByCampaignId(puid, shopId,
                        start.format(DateTimeFormatter.ofPattern(LocalDateTimeUtil.YYYYMMDD_DATE_FORMATE)),
                        end.format(DateTimeFormatter.ofPattern(LocalDateTimeUtil.YYYYMMDD_DATE_FORMATE)), shopAuth.getMarketplaceId(), param.getCampaignId(), type);
        return reports.stream().map(item -> {
            AdReportHourlyVO vo = new AdReportHourlyVO();
            vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern(LocalDateTimeUtil.YYYYMMDD_DATE_FORMATE))
                    .format(DateTimeFormatter.ISO_LOCAL_DATE));
            vo.setDate(vo.getLabel());
            vo.setImpressions(Long.valueOf(item.getImpressions()));
            vo.setAdSale(Optional.ofNullable(item.getTotalSales()).orElse(BigDecimal.ZERO));
            vo.setAdSelfSale(Optional.ofNullable(item.getAdSales()).orElse(BigDecimal.ZERO));
            vo.setAdOtherSale(Optional.ofNullable(item.getAdOtherSales()).orElse(BigDecimal.ZERO));
            vo.setAdOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));

            if (Constants.SB.equalsIgnoreCase(item.getType())) {
                vo.setViewableImpressions(Optional.ofNullable(item.getViewableImpressions()).orElse(0));
            } else {
                vo.setViewableImpressions(Optional.ofNullable(item.getViewImpressions()).orElse(0));
            }

            if (Constants.SD_REPORT_VCPM.equalsIgnoreCase(item.getCostType())) {
                vo.setVcpm(MathUtil.divideByThousand(item.getCost(), Long.parseLong(vo.getViewableImpressions().toString())));
            } else {
                vo.setVcpm(BigDecimal.ZERO);
            }
            vo.setSelfAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
            vo.setOtherAdOrderNum(MathUtil.subtractInteger(item.getOrderNum(), item.getAdOrderNum()));


            vo.setAdSaleNum(Optional.ofNullable(item.getSaleNum()).orElse(0));

            if (Constants.SB.equalsIgnoreCase(item.getType()) || Constants.SD.equalsIgnoreCase(item.getType())) {
                vo.setAdSelfSaleNum(0);
                vo.setAdOtherSaleNum(0);
            } else {
                vo.setAdSelfSaleNum(Optional.ofNullable(item.getAdSaleNum()).orElse(0));
                vo.setAdOtherSaleNum(MathUtil.subtractInteger(item.getSaleNum(), item.getAdSaleNum()));
            }
            vo.setAdCost(Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO));
            vo.setClicks(Long.valueOf(item.getClicks()));
            vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
            vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
            vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
            vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
            vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
            vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
            // 新加字段
            vo.setOrdersNewToBrand(Optional.ofNullable(item.getOrdersNewToBrand14d()).orElse(0));
            vo.setUnitsOrderedNewToBrand(Optional.ofNullable(item.getUnitsOrderedNewToBrand14d()).orElse(0));
            vo.setSalesNewToBrand(Optional.ofNullable(item.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO));

            vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getImpressions()));
            vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getClicks(), vo.getViewableImpressions()));
            vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));

            if (Constants.SD_REPORT_VCPM.equalsIgnoreCase(item.getCostType())) {
                vo.setAdvertisingProductUnitPrice(BigDecimal.ZERO);
                vo.setAdvertisingOtherProductUnitPrice(BigDecimal.ZERO);
            } else {
                vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
                vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getOtherAdOrderNum())));
            }
            vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(Optional.ofNullable(item.getOrdersNewToBrand14d()).orElse(0)), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
            vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(Optional.ofNullable(item.getUnitsOrderedNewToBrand14d()).orElse(0)), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
            vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public GetCampaignHourReportResponse.CampaignHour getAggregateListMultiShop(int puid, List<String> relationIds, CampaignAggregateHourMultiShopParamVO param, ReportDateModelPb.ReportDateModel dateModel) {
        List<ShopAuth> shopAuth = shopAuthDao.listAllByIds(puid, param.getShopIds());
        if (CollectionUtils.isEmpty(shopAuth)) {
            return null;
        }
        boolean isVc = shopAuth.stream().anyMatch(e -> ShopTypeEnum.VC.getCode().equals(e.getType()));
        //取店铺销售额
        List<Integer> shopIds = shopAuth.stream().map(ShopAuth::getId).collect(Collectors.toList());
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(puid, shopIds, param.getStartDate().replace("-", ""), param.getEndDate().replace("-", ""));
        List<AdCampaignHourVo> list = Lists.newArrayList();
        List<AdCampaignHourVo> compares = Lists.newArrayList();
        CampaignHourParamMultiShop paramOld = new CampaignHourParamMultiShop();
        BeanUtils.copyProperties(param, paramOld);
        boolean bool = true;
        if (dateModel == ReportDateModelPb.ReportDateModel.HOURLY) {
            list = getAggregateHourListMultiShop(shopAuth, relationIds, param);
            bool = false;
        } else if (dateModel == ReportDateModelPb.ReportDateModel.DAILY) {
            list = getAggregateDayListMultiShop(param.getPuid(), relationIds, paramOld, shopAuth, compares);
        } else if (dateModel == ReportDateModelPb.ReportDateModel.WEEKLY) {
            list = getAggregateWeekListMultiShop(param.getPuid(), relationIds, paramOld, shopAuth, compares);
        } else if (dateModel == ReportDateModelPb.ReportDateModel.MONTHLY) {
            list = getAggregateMonthListMultiShop(param.getPuid(), relationIds, paramOld, shopAuth, compares);
        }
        GetCampaignHourReportResponse.CampaignHour.Builder builder1 = GetCampaignHourReportResponse.CampaignHour.newBuilder();
        if (CollectionUtils.isNotEmpty(list)) {
            for (AdCampaignHourVo vo : list) {
                vo.setAcots(shopSalesByDate.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(shopSalesByDate, 4, RoundingMode.HALF_UP));
                vo.setAsots(shopSalesByDate.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().multiply(new BigDecimal("100")).divide(shopSalesByDate, 4, RoundingMode.HALF_UP));
            }
            boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                    Constants.isADOrderField(param.getOrderField(), AdCampaignHourVo.class);
            if (isSorted) {
                PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
            }
        }
        builder1.addAllList(list.stream().filter(Objects::nonNull)
                .map(key -> PbUtil.toCampaignHourReportPb(key, shopSalesByDate, isVc)).collect(Collectors.toList()));
        AdCampaignHourVo summaryVO = summary(list, shopSalesByDate, bool);
        if (Integer.valueOf(1).equals(param.getIsCompare()) && dateModel != ReportDateModelPb.ReportDateModel.HOURLY) {
            AdCampaignHourVo compareVO = summary(compares);
            summaryVO.compareDataSet(compareVO);
        }
        builder1.setSummary(PbUtil.toCampaignHourReportPb(summaryVO, shopSalesByDate, isVc));
        builder1.addAllChart(ReportChartUtil.getCampaignHourChartData(list, false));
        //对比数据,chart图数据
        if (param.getIsCompare() != null && param.getIsCompare() == 1) {
            List<AdCampaignHourVo> compareHourVos = list.stream().map(item -> {
                AdCampaignHourVo vo = new AdCampaignHourVo();
                vo.setLabel(item.getLabel());
                vo.setClicks(item.getClicksCompare());
                vo.setImpressions(item.getImpressionsCompare());
                vo.setAdSale(item.getAdSaleCompare());
                vo.setAdCost(item.getAdCostCompare());
                vo.setAdOrderNum(item.getAdOrderNumCompare());
                vo.setCtr(item.getCtrCompare());
                vo.setCvr(item.getCvrCompare());
                vo.setAcos(item.getAcosCompare());
                vo.setRoas(item.getRoasCompare());
                vo.setAdSaleNum(item.getAdSaleNumCompare());
                vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                return vo;
            }).collect(Collectors.toList());
            builder1.addAllChart(ReportChartUtil.getCampaignHourChartData(compareHourVos, true));
        }
        return builder1.build();
    }

    @Override
    public List<AdCampaignWeekDayVo> getAggregateWeeklySuperpositionListMultiShop(Integer puid, List<String> relationIds, CampaignHourParamMultiShop param) {
        List<ShopAuth> shopAuth = shopAuthDao.listAllByIds(puid, param.getShopIds());
        return convertToWeekDayVos(amazonAdFeedReportService.listAggregateWeekListMultiShop(shopAuth , relationIds, param));
    }

    @Override
    public List<AdCampaignHourVo> getAggregateMonthListMultiShop(Integer puid, List<String> relationIds, CampaignHourParamMultiShop param, List<ShopAuth> shopAuth, List<AdCampaignHourVo> compares) {
        return getAggregateDayWeekOrMonthMultiShop(puid, relationIds, shopAuth, param, ReportChartUtil::getCampaignMonthReportVos, false, compares);
    }

    @Override
    public List<AdCampaignHourVo> getAggregateWeekListMultiShop(Integer puid, List<String> relationIds, CampaignHourParamMultiShop param, List<ShopAuth> shopAuth, List<AdCampaignHourVo> compares) {
        List<AdCampaignHourVo> reports = getAdCampaignListDailyMultiShopReports(puid, relationIds, shopAuth, param, false);
        reports = ReportChartUtil.getCampaignWeekReportVos(param.getStartDate(), param.getEndDate(), reports);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            List<AdCampaignHourVo> compareList = getAdCampaignListDailyMultiShopReports(puid, relationIds, shopAuth, param, true);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = ReportChartUtil.getCampaignWeekReportVos(param.getStartDateCompare(), param.getEndDateCompare(), compareList);
            return paddingWeekCompare(reports, compareList);
        } else {
            return reports;
        }
    }

    @Override
    public List<AdCampaignHourVo> getAggregateDayListMultiShop(Integer puid, List<String> relationIds, CampaignHourParamMultiShop param, List<ShopAuth> shopAuth, List<AdCampaignHourVo> compares) {
        return getAggregateDayWeekOrMonthMultiShop(puid, relationIds, shopAuth, param, x -> x, true, compares);
    }

    private List<AdCampaignHourVo> getAggregateDayWeekOrMonthMultiShop(Integer puid, List<String> relationIds, List<ShopAuth> shopAuth,
                                                                       CampaignHourParamMultiShop param,
                                                                       Function<List<AdCampaignHourVo>, List<AdCampaignHourVo>> function, boolean dayGroup, List<AdCampaignHourVo> compares) {
        List<AdCampaignHourVo> reports = getAdCampaignListDailyMultiShopReports(puid, relationIds, shopAuth, param, false);
        reports = reports.stream().filter(Objects::nonNull).sorted((r1, r2) ->
                        DateUtil.compareDate(DateUtil.strToDate(r1.getDate(), "yyyy-MM-dd"), DateUtil.strToDate(r2.getDate(), "yyyy-MM-dd")))
                .collect(Collectors.toList());
        reports = function.apply(reports);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            List<AdCampaignHourVo> compareList = getAdCampaignListDailyMultiShopReports(puid, relationIds, shopAuth, param, true);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = compareList.stream().filter(Objects::nonNull).sorted((r1, r2) ->
                            DateUtil.compareDate(DateUtil.strToDate(r1.getDate(), "yyyy-MM-dd"), DateUtil.strToDate(r2.getDate(), "yyyy-MM-dd")))
                    .collect(Collectors.toList());
            compareList = function.apply(compareList);
            return dayGroup ? paddingDayCompare(param, reports, compareList): paddingMonthCompare(param, reports, compareList);
        } else {
            return reports;
        }
    }

    private List<AdCampaignHourVo> paddingDayCompare(CampaignHourParamMultiShop param, List<AdCampaignHourVo> reports, List<AdCampaignHourVo> compareList) {
        LocalDate start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate startCompare = LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE);
        List<AdCampaignHourVo> resList = new ArrayList<>();
        Map<String, AdCampaignHourVo> map = StreamUtil.toMap(reports, AdCampaignHourVo::getLabel);
        Map<String, AdCampaignHourVo> compareMap = StreamUtil.toMap(compareList, AdCampaignHourVo::getLabel);
        for (; !start.isAfter(end); start = start.plusDays(1), startCompare = startCompare.plusDays(1)) {
            AdCampaignHourVo report = map.get(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
            AdCampaignHourVo compareReport = compareMap.get(startCompare.format(DateTimeFormatter.ISO_LOCAL_DATE));
            if (Objects.isNull(report) && Objects.isNull(compareReport)) {
                continue;
            }
            AdCampaignHourVo vo;
            if (Objects.isNull(report)) {
                vo = new AdCampaignHourVo();
                vo.setLabel(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
            }else {
                vo = report;
            }
            vo.compareDataSet(compareReport);
            resList.add(vo);
        }
        return resList;
    }

    private List<AdCampaignHourVo> paddingMonthCompare(CampaignHourParamMultiShop param, List<AdCampaignHourVo> reports, List<AdCampaignHourVo> compareList) {
        LocalDate start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate startCompare = LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE);
        List<AdCampaignHourVo> resList = new ArrayList<>();
        Map<String, AdCampaignHourVo> map = StreamUtil.toMap(reports, AdCampaignHourVo::getLabel);
        Map<String, AdCampaignHourVo> compareMap = StreamUtil.toMap(compareList, AdCampaignHourVo::getLabel);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        for (; !YearMonth.from(start).isAfter(YearMonth.from(end)); start = start.plusMonths(1), startCompare = startCompare.plusMonths(1)) {
            AdCampaignHourVo report = map.get(start.format(formatter));
            AdCampaignHourVo compareReport = compareMap.get(startCompare.format(formatter));
            if (Objects.isNull(report) && Objects.isNull(compareReport)) {
                continue;
            }
            AdCampaignHourVo vo;
            if (Objects.isNull(report)) {
                vo = new AdCampaignHourVo();
                vo.setLabel(start.format(formatter));
                vo.setDate(vo.getLabel());
            }else {
                vo = report;
            }
            vo.compareDataSet(compareReport);
            resList.add(vo);
        }
        return resList;
    }

    private List<AdCampaignHourVo> getAdCampaignListDailyMultiShopReports(Integer puid, List<String> relationIds, List<ShopAuth> shopAuth, CampaignHourParamMultiShop param, boolean compare) {
        LocalDate start = LocalDate.parse(param.getStartDate());
        LocalDate end = LocalDate.parse(param.getEndDate());
        if (compare) {
            start = LocalDate.parse(param.getStartDateCompare());
            end = LocalDate.parse(param.getEndDateCompare());
        }
        List<Integer> shopIds = shopAuth.stream().map(ShopAuth::getId).collect(Collectors.toList());
        List<String> marketplaceIds = param.getMarketplaceIds();
        MultiThreadQueryParamDtoMultiShop paramDtoMultiShop = MultiThreadQueryParamDtoMultiShop.builder()
                .puid(puid)
                .shopIds(shopIds)
                .marketplaceIds(marketplaceIds)
                .startDate(start)
                .endDate(end)
                .currency(param.getCurrency())
                .multipleIdComputation(param.getMultipleIdComputation())
                .build();
        //分片查询
        List<List<AmazonAdCampaignAllReport>> reportPartition = multiThreadQueryAndMergeUtil.
                multiThreadQuery(ThreadPoolUtil.getCpcAggregateIdsSyncPool(),
                        () -> relationIds, paramDtoMultiShop, this::multiQueryMultiShopReport);
        //合并请求
        List<AmazonAdCampaignAllReport> reports = mergerAggregateHourReport(reportPartition);
        return reports.stream().map(item -> {
            AdCampaignHourVo vo = new AdCampaignHourVo();
            vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                    .format(DateTimeFormatter.ISO_LOCAL_DATE));
            vo.setDate(vo.getLabel());
            vo.setAdSale(Optional.ofNullable(item.getTotalSales()).orElse(BigDecimal.ZERO));
            vo.setAdSelfSale(Optional.ofNullable(item.getAdSales()).orElse(BigDecimal.ZERO));
            vo.setAdOtherSale(Optional.ofNullable(item.getAdOtherSales()).orElse(BigDecimal.ZERO));
            vo.setAdOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
            vo.setSelfAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
            vo.setOtherAdOrderNum(MathUtil.subtractInteger(item.getOrderNum(), item.getAdOrderNum()));
            vo.setAdSaleNum(Optional.ofNullable(item.getSaleNum()).orElse(0));
            vo.setAdSelfSaleNum(Optional.ofNullable(item.getUnitsSold14d()).orElse(0));
            vo.setAdOtherSaleNum(Optional.ofNullable(item.getDetailPageViewsClicks14d()).orElse(0));
            vo.setAdCost(Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO));
            vo.setClicks(Long.valueOf(item.getClicks()));
            vo.setImpressions(Long.valueOf(item.getImpressions()));
            vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
            vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
            vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
            vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
            vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
            vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
            if (Constants.SB.equalsIgnoreCase(item.getType())) {
                vo.setViewableImpressions(Optional.ofNullable(item.getViewableImpressions()).orElse(0));
            } else {
                vo.setViewableImpressions(Optional.ofNullable(item.getViewImpressions()).orElse(0));
            }
            // 新加字段
            vo.setOrdersNewToBrand(Optional.ofNullable(item.getOrdersNewToBrand14d()).orElse(0));
            vo.setUnitsOrderedNewToBrand(Optional.ofNullable(item.getUnitsOrderedNewToBrand14d()).orElse(0));
            vo.setSalesNewToBrand(Optional.ofNullable(item.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO));
            vo.setVcpmCost(Optional.ofNullable(item.getTopOfSearchIs()).orElse(BigDecimal.ZERO));
            vo.setVcpmImpressions(Long.parseLong(String.valueOf(item.getDpv14d() != null ? item.getDpv14d() : 0)));
            vo.setVcpm(MathUtil.divideByThousand(item.getTopOfSearchIs(), vo.getVcpmImpressions()));
            vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getImpressions()));
            vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getClicks(), vo.getViewableImpressions()));
            vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
            vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
            vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getOtherAdOrderNum())));
            vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(item.getOrdersNewToBrand14d()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
            vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(item.getUnitsOrderedNewToBrand14d()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
            vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));
            return vo;
        }).collect(Collectors.toList());
    }

    private List<AmazonAdCampaignAllReport> multiQueryMultiShopReport(List<String> ids, MultiThreadQueryParamDtoMultiShop dto) {
        //日,周,月报告数据
        List<AmazonAdCampaignAllReport> campaignReportList = odsAmazonAdFlowConversionDao.getMultiShopReportByCampaignIdListAll(dto.getPuid(), dto.getShopIds(),
                dto.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                dto.getEndDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")), dto.getMarketplaceIds(), ids, dto.getCurrency());
        //重复的id是否需要多次计算报告数据
        if (dto.getMultipleIdComputation() != null && dto.getMultipleIdComputation()) {
            Map<String, Integer> idCountMap = new HashMap<>();
            ids.forEach(id -> {
                idCountMap.put(id, idCountMap.getOrDefault(id, 0) + 1);
            });
            Map<String, List<AmazonAdCampaignAllReport>> campaignIdReportMap = campaignReportList.stream().collect(Collectors.groupingBy(AmazonAdCampaignAllReport::getCampaignId, Collectors.toList()));
            idCountMap.forEach((k, v) -> {
                if (v > 1 && campaignIdReportMap.containsKey(k)) {
                    for (int i = 1; i < v; i++) {
                        campaignReportList.addAll(campaignIdReportMap.get(k));
                    }
                }
            });
        }
        return campaignReportList;
    }

    @Override
    public List<AdCampaignHourVo> getAggregateHourListMultiShop(List<ShopAuth> shopAuth, List<String> relationIds, CampaignAggregateHourMultiShopParamVO param) {
        if (Objects.isNull(shopAuth)) {
            return null;
        }
        return amazonAdFeedReportService.listAggregateHourListMultiShop(shopAuth, relationIds, param);
    }

    private CampaignWeeklyReportResponsePb.CampaignWeeklyReportResponse multiThreadQueryAmsWeeklyExport(List<String> ids, CampaignWeeklyRequestPb.CampaignWeeklyRequest.Builder paramBuilder) {
        CampaignWeeklyRequestPb.CampaignWeeklyRequest.Builder queryBuilder = CampaignWeeklyRequestPb.CampaignWeeklyRequest.newBuilder();
        BeanUtils.copyProperties(paramBuilder, queryBuilder);
        Optional.ofNullable(paramBuilder.getWeekdayList()).ifPresent(queryBuilder::addAllWeekday);
        queryBuilder.addAllCampaignId(ids);
        return adFeedBlockingStub.statisticsCampaignWeeklyReport(queryBuilder.build());
    }

    //需要merge所有report结果
    private CampaignWeeklyReportResponsePb.CampaignWeeklyReportResponse mergerAmsWeeklyExport(List<CampaignWeeklyReportResponsePb.CampaignWeeklyReportResponse> exports) {
        CampaignWeeklyReportResponsePb.CampaignWeeklyReportResponse.Builder responseBuilder = CampaignWeeklyReportResponsePb.CampaignWeeklyReportResponse.newBuilder();
        if (CollectionUtils.isEmpty(exports)) return responseBuilder.build();
        List<HourlyReportDataPb.HourlyReportData> result = new ArrayList<>();
        //聚合数据
        Collection<HourlyReportDataPb.HourlyReportData> values = exports.stream().filter(Objects::nonNull).filter(e -> e.getDataCount() > 0)
                .map(CampaignWeeklyReportResponsePb.CampaignWeeklyReportResponse::getDataList).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                    LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                    return localTime.getHour() + "&&" + e1.getWeekday();
                }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport)).values();
        if (CollectionUtils.isNotEmpty(values)) {
            result = com.google.common.collect.Lists.newArrayList(values);
        }
        //取第一次请求中的参数即可
        Optional.ofNullable(exports.get(0)).map(CampaignWeeklyReportResponsePb.
                CampaignWeeklyReportResponse::getSellerId).ifPresent(responseBuilder::setSellerId);
        Optional.ofNullable(exports.get(0)).map(CampaignWeeklyReportResponsePb.
                CampaignWeeklyReportResponse::getMarketplaceId).ifPresent(responseBuilder::setMarketplaceId);
        Optional.ofNullable(exports.get(0)).map(CampaignWeeklyReportResponsePb.
                CampaignWeeklyReportResponse::getStartDate).ifPresent(responseBuilder::setStartDate);
        Optional.ofNullable(exports.get(0)).map(CampaignWeeklyReportResponsePb.
                CampaignWeeklyReportResponse::getEndDate).ifPresent(responseBuilder::setEndDate);
        responseBuilder.addAllData(result);
        return responseBuilder.build();
    }

    private List<AdCampaignHourVo> getAdCampaignDailyReports(int puid, CampaignHourParam param, boolean isCompare) {
        LocalDate start = LocalDate.parse(param.getStartDate());
        LocalDate end = LocalDate.parse(param.getEndDate());
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), param.getPuid());
        //日,周,月报告数据
        String type = Constants.SP;
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllDao.getByCampaignId(puid, param.getShopId(), param.getCampaignId());
            if (amazonAdCampaignAll != null) {
                type = amazonAdCampaignAll.getType();
            }
        }

        if (isCompare) {
            if (param.getEndDateCompare() == null || param.getStartDateCompare() == null) {
                //查询对比但是对比时间为空返回空list；
                return new ArrayList<>();
            }
            start = LocalDate.parse(param.getStartDateCompare());
            end = LocalDate.parse(param.getEndDateCompare());
        }

        List<AmazonAdCampaignAllReport> reports =
                amazonAdCampaignAllReportDao.getReportByCampaignId(puid, param.getShopId(),
                        start.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                        end.format(DateTimeFormatter.ofPattern("yyyyMMdd")), shopAuth.getMarketplaceId(), param.getCampaignId(), type);
        return reports.stream().map(item -> {
            AdCampaignHourVo vo = new AdCampaignHourVo();

            vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                    .format(DateTimeFormatter.ISO_LOCAL_DATE));
            vo.setDate(vo.getLabel());
            vo.setAdSale(Optional.ofNullable(item.getTotalSales()).orElse(BigDecimal.ZERO));
            vo.setAdSelfSale(Optional.ofNullable(item.getAdSales()).orElse(BigDecimal.ZERO));
            vo.setAdOtherSale(Optional.ofNullable(item.getAdOtherSales()).orElse(BigDecimal.ZERO));
            vo.setAdOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
            vo.setSelfAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
            vo.setOtherAdOrderNum(MathUtil.subtractInteger(item.getOrderNum(), item.getAdOrderNum()));
            vo.setAdSaleNum(Optional.ofNullable(item.getSaleNum()).orElse(0));

            if (Constants.SB.equalsIgnoreCase(item.getType()) || Constants.SD.equalsIgnoreCase(item.getType())) {
                vo.setAdSelfSaleNum(0);
                vo.setAdOtherSaleNum(0);
            } else {
                vo.setAdSelfSaleNum(Optional.ofNullable(item.getAdSaleNum()).orElse(0));
                vo.setAdOtherSaleNum(MathUtil.subtractInteger(item.getSaleNum(), item.getAdSaleNum()));
            }

            vo.setAdCost(Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO));
            vo.setClicks(Long.valueOf(item.getClicks()));
            vo.setImpressions(Long.valueOf(item.getImpressions()));
            vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
            vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
            vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
            vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
            vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
            vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));

            if (Constants.SB.equalsIgnoreCase(item.getType())) {
                vo.setViewableImpressions(Optional.ofNullable(item.getViewableImpressions()).orElse(0));
            } else {
                vo.setViewableImpressions(Optional.ofNullable(item.getViewImpressions()).orElse(0));
            }

            if (Constants.SD_REPORT_VCPM.equalsIgnoreCase(item.getCostType())) {
                vo.setVcpm(MathUtil.divideByThousand(item.getCost(), vo.getViewableImpressions()));
                vo.setVcpmImpressions(vo.getViewableImpressions().longValue());
                vo.setVcpmCost(vo.getAdCost());
            } else {
                vo.setVcpm(BigDecimal.ZERO);
                vo.setVcpmImpressions(0L);
                vo.setVcpmCost(BigDecimal.ZERO);
            }

            // 新加字段
            vo.setOrdersNewToBrand(Optional.ofNullable(item.getOrdersNewToBrand14d()).orElse(0));
            vo.setUnitsOrderedNewToBrand(Optional.ofNullable(item.getUnitsOrderedNewToBrand14d()).orElse(0));
            vo.setSalesNewToBrand(Optional.ofNullable(item.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO));

            vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getImpressions()));
            vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getClicks(), vo.getViewableImpressions()));

            vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
            vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
            vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getAdOrderNum() - vo.getSelfAdOrderNum())));

            vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
            vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
            vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));

            return vo;
        }).collect(Collectors.toList());
    }

    private List<AdCampaignHourVo> getAdCampaignListDailyReports(int puid, List<String> campaignIdList, CampaignHourParam param) {
        LocalDate start = LocalDate.parse(param.getStartDate());
        LocalDate end = LocalDate.parse(param.getEndDate());
        MultiThreadQueryParamDto paramDto = MultiThreadQueryParamDto.builder()
                .puid(puid)
                .shopIds(param.getShopIdList())
                .startDate(start)
                .endDate(end)
                .build();
        //需要分片查询，数据量过大
        List<List<AmazonAdCampaignAllReport>> reportPartition = multiThreadQueryAndMergeUtil.
                multiThreadQuery(ThreadPoolUtil.getCpcAggregateIdsSyncPool(),
                        () -> campaignIdList, paramDto, this::multiQueryReport);
        //合并请求结果
        List<AmazonAdCampaignAllReport> reports = mergerAggregateHourReport(reportPartition);
        return reports.stream().map(item -> {
            AdCampaignHourVo vo = new AdCampaignHourVo();
            vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                    .format(DateTimeFormatter.ISO_LOCAL_DATE));
            vo.setDate(vo.getLabel());
            vo.setAdSale(Optional.ofNullable(item.getTotalSales()).orElse(BigDecimal.ZERO));
            vo.setAdSelfSale(Optional.ofNullable(item.getAdSales()).orElse(BigDecimal.ZERO));
            vo.setAdOtherSale(Optional.ofNullable(item.getAdOtherSales()).orElse(BigDecimal.ZERO));
            vo.setAdOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
            vo.setSelfAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
            vo.setOtherAdOrderNum(MathUtil.subtractInteger(item.getOrderNum(), item.getAdOrderNum()));
            vo.setAdSaleNum(Optional.ofNullable(item.getSaleNum()).orElse(0));
            // TODO 逻辑错误的
            vo.setAdSelfSaleNum(Optional.ofNullable(item.getUnitsSold14d()).orElse(0));
            vo.setAdOtherSaleNum(Optional.ofNullable(item.getDetailPageViewsClicks14d()).orElse(0));

            vo.setAdCost(Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO));
            vo.setClicks(Long.valueOf(item.getClicks()));
            vo.setImpressions(Long.valueOf(item.getImpressions()));
            vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
            vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
            vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
            vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
            vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
            vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
            // TODO 逻辑错误的
            if (Constants.SB.equalsIgnoreCase(item.getType())) {
                vo.setViewableImpressions(Optional.ofNullable(item.getViewableImpressions()).orElse(0));
            } else {
                vo.setViewableImpressions(Optional.ofNullable(item.getViewImpressions()).orElse(0));
            }
            // 新加字段
            vo.setOrdersNewToBrand(Optional.ofNullable(item.getOrdersNewToBrand14d()).orElse(0));
            vo.setUnitsOrderedNewToBrand(Optional.ofNullable(item.getUnitsOrderedNewToBrand14d()).orElse(0));
            vo.setSalesNewToBrand(Optional.ofNullable(item.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO));
            vo.setVcpmCost(Optional.ofNullable(item.getTopOfSearchIs()).orElse(BigDecimal.ZERO));
            vo.setVcpmImpressions(Long.parseLong(String.valueOf(item.getDpv14d() != null ? item.getDpv14d() : 0)));
            vo.setVcpm(MathUtil.divideByThousand(item.getTopOfSearchIs(), vo.getVcpmImpressions()));
            vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getImpressions()));
            vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getClicks(), vo.getViewableImpressions()));
            vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
            vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
            vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getOtherAdOrderNum())));
            vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(item.getOrdersNewToBrand14d()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
            vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(item.getUnitsOrderedNewToBrand14d()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
            vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));
            return vo;
        }).collect(Collectors.toList());
    }

    private List<AmazonAdCampaignAllReport> multiQueryReport(List<String> ids, MultiThreadQueryParamDto dto) {
        List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(dto.getPuid(), dto.getShopIds());
        //日,周,月报告数据
        return odsAmazonAdCampaignAllReportDao.getReportByCampaignIdListAll(dto.getPuid(), dto.getShopIds(),
                dto.getStartDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                dto.getEndDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), ids, MultipleUtils.changeRate(shopAuths));
    }

    //需要merge所有report结果
    private List<AmazonAdCampaignAllReport> mergerAggregateHourReport(List<List<AmazonAdCampaignAllReport>> reports) {
        List<AmazonAdCampaignAllReport> result = new ArrayList<>();
        //需要按日聚合
        Map<String, List<AmazonAdCampaignAllReport>> resultMap = reports.stream().flatMap(Collection::stream).
                collect(Collectors.groupingBy(AmazonAdCampaignAllReport::getCountDate));
        for (List<AmazonAdCampaignAllReport> dataList : resultMap.values()) {
            AmazonAdCampaignAllReport reducePO = new AmazonAdCampaignAllReport();
            if (CollectionUtils.isEmpty(dataList)) continue;
            reducePO.setCostType(dataList.get(0).getCostType());
            reducePO.setShopId(dataList.get(0).getShopId());
            reducePO.setCountDate(dataList.get(0).getCountDate());
            reducePO.setMarketplaceId(dataList.get(0).getMarketplaceId());
            reducePO.setType(dataList.get(0).getType());
            reducePO.setCampaignId(dataList.get(0).getCampaignId());
            reducePO.setCampaignName(dataList.get(0).getCampaignName());
            result.add(dataList.stream().reduce(reducePO, AggregationDataUtil::getReduce));
        }
        return result;
    }

    private void filterSumMetricData(List<AdCampaignHourVo> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        for (AdCampaignHourVo vo : voList) {
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdCost())).
                    map(AdCampaignHourVo::getAdCost).ifPresent(v -> adMetricDto.setSumCost(Optional.ofNullable(adMetricDto.getSumCost()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdSale())).
                    map(AdCampaignHourVo::getAdSale).ifPresent(v -> adMetricDto.setSumAdSale(Optional.ofNullable(adMetricDto.getSumAdSale()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdOrderNum())).
                    map(AdCampaignHourVo::getAdOrderNum).map(BigDecimal::valueOf).ifPresent(v -> adMetricDto.setSumAdOrderNum(Optional.ofNullable(adMetricDto.getSumAdOrderNum()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdSaleNum())).
                    map(AdCampaignHourVo::getAdSaleNum).map(BigDecimal::valueOf).ifPresent(v -> adMetricDto.setSumOrderNum(Optional.ofNullable(adMetricDto.getSumOrderNum()).orElse(BigDecimal.ZERO).add(v)));
        }
    }

    // 填充指标占比数据
    private void filterMetricData(List<AdCampaignHourVo> voList, AdMetricDto adMetricDto) {
        for (AdCampaignHourVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                continue;
            }
            computeMetricData(adMetricDto, vo);
        }
    }

    private void computeMetricData(AdMetricDto adMetricDto, AdCampaignHourVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdCost().toString(), adMetricDto.getSumCost().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSale().toString(), adMetricDto.getSumAdSale().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getAdSaleNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSaleNum().toString(), adMetricDto.getSumOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }
    }

    private Map<Integer, HourlyReportDataPb.HourlyReportData> getIntegerHourlyReportDataMap(CampaignHourlyReportResponsePb.CampaignHourlyReportResponse statisticsByHourResponse, Map<Integer, HourlyReportDataPb.HourlyReportData> hourlyReportDataMap) {
        if (statisticsByHourResponse != null && statisticsByHourResponse.getDataCount() > 0) {
            hourlyReportDataMap = statisticsByHourResponse.getDataList().stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, (e1, e2) -> e2));
        }
        return hourlyReportDataMap;
    }

    private Map<Integer, AmazonMarketingStreamData> getIntegerHourlyReportDataMap(List<AmazonMarketingStreamData> amazonMarketingStreamDataList, Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap) {
        if (CollectionUtils.isNotEmpty(amazonMarketingStreamDataList)) {
            hourlyReportDataMap = amazonMarketingStreamDataList.stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, (e1, e2) -> e2));
        }
        return hourlyReportDataMap;
    }

    private Map<Integer, HourlyReportDataPb.HourlyReportData> getIntegerHourlyReportDataMap(ProductPerspectiveCampaignHourlyResponsePb.ProductPerspectiveCampaignHourlyResponse statisticsByHourResponse, Map<Integer, HourlyReportDataPb.HourlyReportData> hourlyReportDataMap) {
        if (statisticsByHourResponse != null && statisticsByHourResponse.getDataCount() > 0) {
            hourlyReportDataMap = statisticsByHourResponse.getDataList().stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, (e1, e2) -> e2));
        }
        return hourlyReportDataMap;
    }

    private Map<Integer, HourlyReportDataPb.HourlyReportData> getIntegerHourlyReportDataMap(AggregateCampaignHourlyReportResponsePb.AggregateCampaignHourlyReportResponse statisticsByHourResponse, Map<Integer, HourlyReportDataPb.HourlyReportData> hourlyReportDataMap) {
        if (statisticsByHourResponse != null && statisticsByHourResponse.getDataCount() > 0) {
            hourlyReportDataMap = statisticsByHourResponse.getDataList().stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, (e1, e2) -> e2));
        }
        return hourlyReportDataMap;
    }

    private Map<String, AmazonMarketingStreamData> getBudgetHourlyReportDataMap(
            List<AmazonMarketingStreamData> statisticsByHourResponse,
            Map<String, AmazonMarketingStreamData> hourlyReportDataMap) {
        if (CollectionUtils.isNotEmpty(statisticsByHourResponse)) {
            hourlyReportDataMap = statisticsByHourResponse.stream().collect(Collectors.toMap(e1 -> {
                return DateUtil.dateToStrWithTime(e1.getTimeWindowStart(), "yyyy-MM-dd HH");
            }, e1 -> e1));
        }
        return hourlyReportDataMap;
    }

    private AdCampaignHourVo budgetConvertTo(AmazonMarketingStreamData data) {
        AdCampaignHourVo vo = new AdCampaignHourVo();
        if (data == null) {
            return vo;
        }
//        vo.setWeekDay(data.getWeekday());
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7d());
        vo.setSelfAdOrderNum(data.getAttributedConversions7dSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7d(), data.getAttributedConversions7dSameSku()));
        vo.setAdSale(data.getAttributedSales7d().setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(data.getAttributedSales7dSameSku().setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(data.getAttributedSales7d(), data.getAttributedSales7dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7d());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7dSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7d(), data.getAttributedUnitsOrdered7dSameSku()));
        vo.setImpressions(data.getImpressions());
//        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(DateUtil.dateToStrWithTime(data.getTimeWindowStart(), "yyyy-MM-dd HH"));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }

    private AdCampaignHourVo convertTo(HourlyReportDataPb.HourlyReportData data) {
        AdCampaignHourVo vo = new AdCampaignHourVo();
        if (data == null) {
            return vo;
        }
        vo.setWeekDay(data.getWeekday());
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7D());
        vo.setSelfAdOrderNum(data.getAttributedConversions7DSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7D(), data.getAttributedConversions7DSameSku()));
        vo.setAdSale(BigDecimal.valueOf(data.getAttributedSales7D()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(BigDecimal.valueOf(data.getAttributedSales7DSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(BigDecimal.valueOf(data.getAttributedSales7D()), BigDecimal.valueOf(data.getAttributedSales7DSameSku())).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7D());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7DSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7D(), data.getAttributedUnitsOrdered7DSameSku()));
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }

    private AdCampaignHourVo convertTo(AmazonMarketingStreamData data) {
        AdCampaignHourVo vo = new AdCampaignHourVo();
        if (data == null) {
            return vo;
        }
        vo.setWeekDay(data.getWeekday());
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7d());
        vo.setSelfAdOrderNum(data.getAttributedConversions7dSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7d(), data.getAttributedConversions7dSameSku()));
        vo.setAdSale(data.getAttributedSales7d().setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(data.getAttributedSales7dSameSku().setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(data.getAttributedSales7d(), data.getAttributedSales7dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7d());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7dSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7d(), data.getAttributedUnitsOrdered7dSameSku()));
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }

    private List<AdCampaignWeekDayVo> convertToHourOfWeekDayVos(List<AdCampaignHourVo> hourVos) {
        //按周汇总数据
        Map<Integer, List<AdCampaignHourVo>> hourVoMap =
                hourVos.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AdCampaignHourVo::getWeekDay));

        List<AdCampaignWeekDayVo> adCampaignWeekDayVos = new ArrayList<>(7);

        for (Map.Entry<Integer, List<AdCampaignHourVo>> entry : hourVoMap.entrySet()) {
            AdCampaignWeekDayVo adCampaignWeekDayVo = new AdCampaignWeekDayVo();
            List<AdCampaignHourVo> asinHourVos = entry.getValue();
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(asinHourVos, adMetricDto);
            filterMetricData(asinHourVos, adMetricDto);
            adCampaignWeekDayVo.setDetails(asinHourVos);
            adCampaignWeekDayVo.staticsFromHourVos(asinHourVos);
            adCampaignWeekDayVo.afterPropertiesSet();
            adCampaignWeekDayVo.setWeekDay(entry.getKey());
            adCampaignWeekDayVos.add(adCampaignWeekDayVo);
        }
        AdMetricDto adMetricDto1 = new AdMetricDto();
        sumMetricData(adCampaignWeekDayVos, adMetricDto1);
        metricData(adCampaignWeekDayVos, adMetricDto1);
        return adCampaignWeekDayVos.stream().collect(Collectors.toList());
    }

    private List<AdCampaignWeekDayVo> convertToWeekDayVos(List<AdCampaignHourVo> hourVos) {
        List<AdCampaignWeekDayVo> adCampaignWeekDayVos = new ArrayList<>(7);
        //按周汇总数据
        Map<Integer, List<AdCampaignHourVo>> map = hourVos.stream().collect(Collectors.groupingBy(AdCampaignHourVo::getWeekDay));
        map.forEach((key, value) -> {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(value, adMetricDto);
            filterMetricData(value, adMetricDto);
            value.sort(Comparator.comparing(AdCampaignHourVo::getHour));
            AdCampaignWeekDayVo adCampaignWeekDayVo = new AdCampaignWeekDayVo();
            adCampaignWeekDayVo.setDetails(value);
            adCampaignWeekDayVo.setWeekDay(key);
            //填充指标和对比指标
            adCampaignWeekDayVo.staticsFromHourVos(value);
            //填充对比率
            adCampaignWeekDayVo.afterPropertiesSet();
            BigDecimal vcpmCost = value.stream().map(AdCampaignHourVo::getVcpmCost).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            Long vcpmImpressions = value.stream().mapToLong(AdCampaignHourVo::getVcpmImpressions).sum();
            Long totalImpressions = value.stream().mapToLong(AdCampaignHourVo::getTotalImpressions).sum();
            Long totalClicks = value.stream().mapToLong(AdCampaignHourVo::getTotalClicks).sum();
            BigDecimal totalAdSale = value.stream().map(AdCampaignHourVo::getTotalAdSale).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal totalAdSelfSale = value.stream().map(AdCampaignHourVo::getTotalAdSelfSale).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

            adCampaignWeekDayVo.setTotalAdSale(totalAdSale);
            adCampaignWeekDayVo.setTotalAdSelfSale(totalAdSelfSale);
            adCampaignWeekDayVo.setTotalImpressions(totalImpressions);
            adCampaignWeekDayVo.setTotalClicks(totalClicks);
            adCampaignWeekDayVo.setVcpmCost(vcpmCost);
            adCampaignWeekDayVo.setVcpmImpressions(vcpmImpressions);
            adCampaignWeekDayVo.setVcpm(MathUtil.divideByThousand(vcpmCost, vcpmImpressions));
            adCampaignWeekDayVo.setVrt(MathUtil.divideIntegerByOneHundred(adCampaignWeekDayVo.getViewableImpressions(), totalImpressions));
            adCampaignWeekDayVo.setVCtr(MathUtil.divideIntegerByOneHundred(totalClicks, adCampaignWeekDayVo.getViewableImpressions()));
            adCampaignWeekDayVo.setAdvertisingUnitPrice(MathUtil.divideByZero(adCampaignWeekDayVo.getAdSale(), BigDecimal.valueOf(adCampaignWeekDayVo.getAdOrderNum())));
            adCampaignWeekDayVo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(adCampaignWeekDayVo.getTotalAdSelfSale(), BigDecimal.valueOf(adCampaignWeekDayVo.getSelfAdOrderNum())));
            adCampaignWeekDayVo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(adCampaignWeekDayVo.getTotalAdSale().subtract(adCampaignWeekDayVo.getTotalAdSelfSale()), BigDecimal.valueOf(adCampaignWeekDayVo.getOtherAdOrderNum())));
            adCampaignWeekDayVo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adCampaignWeekDayVo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adCampaignWeekDayVo.getAdOrderNum())));
            adCampaignWeekDayVo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adCampaignWeekDayVo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adCampaignWeekDayVo.getAdSaleNum())));
            adCampaignWeekDayVo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(adCampaignWeekDayVo.getSalesNewToBrand(), BigDecimal.valueOf(100)), adCampaignWeekDayVo.getAdSale()));
            adCampaignWeekDayVos.add(adCampaignWeekDayVo);
        });
        if (CollectionUtils.isNotEmpty(adCampaignWeekDayVos)) {
            AdMetricDto adMetricDto1 = new AdMetricDto();
            sumMetricData(adCampaignWeekDayVos, adMetricDto1);
            metricData(adCampaignWeekDayVos, adMetricDto1);
            adCampaignWeekDayVos.sort(Comparator.comparing(AdCampaignWeekDayVo::getWeekDay));
        }
        return adCampaignWeekDayVos;
    }


    private void sumMetricData(List<? extends AdCampaignHourBaseVo> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        adMetricDto.setSumCost(voList.stream().filter(item -> item != null && item.getAdCost() != null).map(item -> item.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdSale(voList.stream().filter(item -> item != null && item.getAdSale() != null).map(item -> item.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdCampaignHourBaseVo::getAdOrderNum).sum()));
        adMetricDto.setSumOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdCampaignHourBaseVo::getAdSaleNum).sum()));
    }

    // 填充指标占比数据
    private void metricData(List<? extends AdCampaignHourBaseVo> voList, AdMetricDto adMetricDto) {
        for (AdCampaignHourBaseVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                continue;
            }
            metricData(adMetricDto, vo);
        }
    }

    private void metricData(AdMetricDto adMetricDto, AdCampaignHourBaseVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdCost().toString(), adMetricDto.getSumCost().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSale().toString(), adMetricDto.getSumAdSale().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getAdSaleNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSaleNum().toString(), adMetricDto.getSumOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }
    }

}