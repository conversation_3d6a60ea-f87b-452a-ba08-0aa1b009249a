package com.meiyunji.sponsored.service.syncTask.amc;

import com.meiyunji.sellfox.aadas.types.message.notification.AmcReadyNotification;
import com.meiyunji.sponsored.service.syncTask.amc.enums.AmcReportTypeEnum;
import com.meiyunji.sponsored.service.syncTask.amc.reportStrategy.AmcReportProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;


@Slf4j
@Component
public class AmcReportReadyService {

    @Autowired
    private Map<String, AmcReportProcessor> processorMap;

    public void process(AmcReadyNotification message) throws Exception {
        AmcReportTypeEnum amcReportTypeEnum = AmcReportTypeEnum.typeMap.get(message.getType());

        if (Objects.isNull(amcReportTypeEnum)) {
            log.error("amc report, type is error");
            return;
        }

        AmcReportProcessor processor = processorMap.get(amcReportTypeEnum.getBeanName());
        if (Objects.isNull(processor)) {
            log.error("amc report, processor is null");
            return;
        }

        processor.parseAndSaveAmcReport(message);
    }

}
