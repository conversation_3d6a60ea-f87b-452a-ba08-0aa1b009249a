package com.meiyunji.sponsored.service.system.po;

import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;

import java.util.Date;

/**
 * ReSyncTime
 * <AUTHOR>
 */
@DbTable(value = "t_re_sync_time")
public class ReSyncTime extends BasePo {
	/**
	 * id
	 */
	@DbColumn(value = "id",key = true,autoIncrement = true)
	private Integer id;
	/**
	 * 商户Id
	 */
	@DbColumn(value = "puid")
	private Integer puid;
	/**
	 * 卖家id
	 */
	@DbColumn(value = "selling_partner_id")
	private String sellingPartnerId;
	/**
	 * 大区，na,eu,fe
	 */
	@DbColumn(value = "region")
	private String region;
	/**
	 * 同步任务类型
	 */
	@DbColumn(value = "sync_type")
	private Integer syncType;
	/**
	 * 上次同步时间
	 */
	@DbColumn(value = "sync_last_time")
	private Date syncLastTime;

	@DbColumn(value = "sync_start_time")
	private Date syncStartTime;
		
	public void setId(Integer value) {
		this.id = value;
	}
	public Integer getId() {
		return this.id;
	}
	public void setPuid(Integer value) {
		this.puid = value;
	}
	public Integer getPuid() {
		return this.puid;
	}
	public void setSellingPartnerId(String value) {
		this.sellingPartnerId = value;
	}
	public String getSellingPartnerId() {
		return this.sellingPartnerId;
	}
	public void setRegion(String value) {
		this.region = value;
	}
	public String getRegion() {
		return this.region;
	}
	public void setSyncType(Integer value) {
		this.syncType = value;
	}
	public Integer getSyncType() {
		return this.syncType;
	}
	public void setSyncLastTime(Date value) {
		this.syncLastTime = value;
	}
	public Date getSyncLastTime() {
		return this.syncLastTime;
	}

	public Date getSyncStartTime() {
		return syncStartTime;
	}

	public void setSyncStartTime(Date syncStartTime) {
		this.syncStartTime = syncStartTime;
	}
}