package com.meiyunji.sponsored.service.autoRule.vo.rule;

import com.meiyunji.sponsored.service.autoRule.vo.BidDataOperate;
import lombok.Data;

/**
 * 广告数据条件
 */
@Data
public class BiddingCallbackOperateVo {

    /**
     * 调整类型(百分比:percentage,国定值:fixed)
     */
    private String adjustType;

    /**
     * 调整的具体数值
     */
    private String adjustValue;

    /**
     * 调整的具体数值
     */
    private String limitValue;

    /**
     * 竞价
     */
    private BidDataOperate bid;

    /**
     * 顶部竞价比例
     */
    private BidDataOperate placementTopBidRatio;
}
