package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingSphereDao;
import com.meiyunji.sponsored.service.autoRule.vo.AutoRuleObjectParam;
import com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeyword;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryTargetingReport;
import com.meiyunji.sponsored.service.cpc.po.SearchQueryTagParam;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordBo;
import org.springframework.beans.PropertyValues;

import java.util.List;

/**
 * CpcQueryTargetingReport
 * <AUTHOR>
 */
public interface ICpcQueryTargetingReportDao extends IBaseShardingSphereDao<CpcQueryTargetingReport> {
    /**
     * 投放query报告
     * @param puid
     * @param list
     */
    void insertList(Integer puid, List<CpcQueryTargetingReport> list);

    /**
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    Page pageList(Integer puid, CpcQueryWordDto dto, Page page);

    Page pageManageList(Integer puid, CpcQueryWordDto dto, Page page);

    /**
     * 列表页汇总数据
     * @param puid
     * @param dto
     * @return
     */
    CpcQueryTargetingReport sumReport(Integer puid, CpcQueryWordDto dto, Boolean type);

    /**
     * 详情页列表
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    Page detailList(Integer puid, CpcQueryWordDetailDto dto, Page page);

    /**
     * 详情页汇总
     * @param puid
     * @param dto
     * @return
     */
    CpcQueryTargetingReport sumDetailReport(Integer puid, CpcQueryWordDetailDto dto);

    /**
     * 详情页图表
     * @param puid
     * @param dto
     * @return
     */
    List<CpcQueryTargetingReport> detailListChart(Integer puid, CpcQueryWordDetailDto dto);

    /**
     * 新版列表页
     * @param puid
     * @param param
     * @return
     */
    List<CpcQueryTargetingReport> listByPageParam(Integer puid, QueryWordPageParam param);

    /**
     * 获取指定投放、搜索词的报告数据
     * @param puid
     * @param shopId
     * @param startDate
     * @param endDate
     * @param targetId
     * @param query
     * @return
     */
    List<CpcQueryTargetingReport> listReports(Integer puid, Integer shopId, String startDate, String endDate, String targetId, String query);

    /**
     * 查出搜索词是asin的但没有图片的
     * @param puid：
     * @param shopId：
     * @param campaignId：也可以指定活动id查
     * @param offset：主键偏移量
     */
    List<CpcQueryTargetingReport> listNoAsinInfo(
            Integer puid, Integer shopId, String startDate, String endDate, String campaignId,
            long offset, int limit);

    /**
     * 批量回填Asin图片url、标题信息
     * @param needUpdateList：
     */
    void batchSetAsinInfo(Integer puid, List<CpcQueryTargetingReport> needUpdateList);

    /**
     * 搜索词(投放)首页数据
     * @param puid
     * @param dto
     * @return
     */
    List<AdHomePerformancedto> listAllTargetingReportByDate(Integer puid, CpcQueryWordDto dto);

    List<AdHomePerformancedto> getReportByTargetIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> targetIdList, CpcQueryWordDto dto);

    List<CpcQueryTargetingReport> getReportVoListByGroupIds(Integer puid, List<String> spGroupIds, QueryReportSearchVo searchVo);

    List<CpcQueryTargetingReport> getListByTargetId(Integer puid, String targetId, TargetQuerySearchVo searchVo);

    CpcQueryTargetingReport  getDetailsSumVo(Integer puid, QueryReportDetailsVo detailsVo);

    List<CpcQueryTargetingReport>  getListQueryDetailsDay(Integer puid, QueryReportDetailsVo detailsVo);

    List<CpcQueryTargetingReport> getReportTargetByDate(Integer puid, Integer shopId, String start, String end);

    List<CpcQueryTargetingReport> getNeedsLocalizationKeywords(Integer puid, Integer shopId, Integer limit);

    int updateQueryCnById(Integer puid, CpcQueryTargetingReport report);

    Page pageKeywordAndTargetManageList(Integer puid, CpcQueryWordDto dto, Page page);

    /**
     * 获取汇总指标数据
     * @param puid
     * @param dto
     * @return
     */
    AdMetricDto  getSumAdMetricDto(Integer puid, CpcQueryWordDto dto);

    List<AdHomePerformancedto> getKeywordAndTargetListAllTargetingReportByDate(Integer puid, CpcQueryWordDto dto);

    List<Long> getAllIdByPuidLimit(Integer puid, int start, int limit);

    void updateQueryId(Integer puid, Long startId, Long endId);

    List<CpcQueryTargetingReport> listSpByTargetRule(AutoRuleObjectParam param, List<String> itemIdList);

    List<CpcQueryTargetingReport> listSpTargetingReport(Integer puid, Integer shopId, Integer page);

    List<CpcQueryTargetingReport> listSpTargetingReportByTimeRange(Integer puid, Integer shopId, Integer timeRange);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

    List<String> listQueryId(ProcessTaskParam param);


    List<SearchQueryTagParam> listAdGroupIdByQueryWordDto(CpcQueryWordDto dto);

    List<CpcQueryTargetingReport> getDetailList(Integer puid, CpcQueryWordDetailDto dto);

    /**
     * 搜索词词根获取近两个月的词根列表
     * @param puid
     * @param shopId
     * @param page
     * @return
     */
    List<WordBo> listWordBo(Integer puid, Integer shopId, Integer page);

    /**
     * 搜索词词根获取近几天的词根列表
     * @param puid
     * @param shopId
     * @param timeRange
     * @return
     */
    List<WordBo> listWordBoTimeRange(Integer puid, Integer shopId, Integer timeRange);

    List<CpcQueryTargetingReport> wordListByIds(int puid, Integer shopId, List<Long> ids);
}