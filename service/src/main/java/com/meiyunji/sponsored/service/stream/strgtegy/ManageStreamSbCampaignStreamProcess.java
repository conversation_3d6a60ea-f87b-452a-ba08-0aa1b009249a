package com.meiyunji.sponsored.service.stream.strgtegy;

import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbCampaignApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCampaignApiService;
import com.meiyunji.sponsored.service.stream.enums.AmazonStreamTaskRetryStatusEnum;
import com.meiyunji.sponsored.service.stream.enums.StreamConstants;
import com.meiyunji.sponsored.service.stream.po.AmazonManagementStreamTaskRetry;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamTaskRetryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.stream.enums.StreamConstants.RETRY_COUNT;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2024/1/17 15:29
 */
@Service
public class ManageStreamSbCampaignStreamProcess extends AbstractManageStreamRetryProcessStrategy {


    @Resource
    private CpcSbCampaignApiService cpcSbCampaignApiService;

    @Resource
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    public ManageStreamSbCampaignStreamProcess(IAmazonManagementStreamTaskRetryService amazonManagementStreamTaskRetryService, DynamicRefreshConfiguration dynamicRefreshConfiguration) {
        super(amazonManagementStreamTaskRetryService, dynamicRefreshConfiguration);
    }



    @Override
    public int getMaxCount() {
        return StreamConstants.SP_MAX_CAMPAIGN_IDS_COUNT;
    }

    @Override
    public void doExecute(ShopAuth shopAuth, String ids) {
        cpcSbCampaignApiService.syncCampaignsV4(shopAuth, ids, false, true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
    }
}
