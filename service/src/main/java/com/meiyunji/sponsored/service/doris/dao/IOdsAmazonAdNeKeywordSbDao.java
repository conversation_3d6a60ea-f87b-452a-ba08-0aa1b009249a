package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.cpc.vo.KeywordLibsPageParam;
import com.meiyunji.sponsored.service.cpc.vo.KeywordLibsVo;
import com.meiyunji.sponsored.service.cpc.vo.RepeatTargetingDetailPageVo;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdNeKeywordSb;

import java.util.List;

/**
 * amazon广告关键词表(OdsAmazonAdKeyword)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:18
 */
public interface IOdsAmazonAdNeKeywordSbDao extends IDorisBaseDao<OdsAmazonAdNeKeywordSb> {

    /**
     * 根据广告组id获取关键词
     */
    List<OdsAmazonAdNeKeywordSb> listByGroupIdList(Integer puid, Integer shopId, List<String> groupIdList);

    /**
     * 根据筛选条件查出所有的否投关键词Id
     * @param puid
     * @param detailPageVo
     * @return
     */
    List<String> filterKeywordId(Integer puid, RepeatTargetingDetailPageVo detailPageVo);
}

