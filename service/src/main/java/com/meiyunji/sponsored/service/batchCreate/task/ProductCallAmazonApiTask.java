package com.meiyunji.sponsored.service.batchCreate.task;

import com.amazon.advertising.spV3.enumeration.SpV3StateEnum;
import com.amazon.advertising.spV3.product.CreateSpProductV3Response;
import com.amazon.advertising.spV3.product.ListSpProductV3Response;
import com.amazon.advertising.spV3.product.ProductSpV3Client;
import com.amazon.advertising.spV3.product.entity.ProductEntityV3;
import com.amazon.advertising.spV3.product.entity.ProductSuccessResultV3;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.batchCreate.dao.IAmazonAdBatchGroupDao;
import com.meiyunji.sponsored.service.batchCreate.dao.IAmazonAdBatchProductDao;
import com.meiyunji.sponsored.service.batchCreate.dto.task.BatchCreateReturnDto;
import com.meiyunji.sponsored.service.batchCreate.dto.task.CallAmazonApiTaskResultDto;
import com.meiyunji.sponsored.service.batchCreate.enums.AdAmazonErrorTypeEnum;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchConstants;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdLevelStatusEnum;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdTaskLevelEnum;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchProduct;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProduct;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdProduct;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-11-17  17:07
 */

/**
 * 广告产品批量处理任务
 */
@Component
@Slf4j
public class ProductCallAmazonApiTask extends AbstractCallAmazonApiTask {
    @Autowired
    private IAmazonAdBatchProductDao amazonAdBatchProductDao;
    @Autowired
    private IAmazonAdBatchGroupDao amazonAdBatchGroupDao;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;
    @Autowired
    private IAdManageOperationLogService manageOperationLogService;
    @Autowired
    private TaskStatusHelper taskStatusHelper;
    @Autowired
    private IDorisService dorisService;
    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    public CallAmazonApiTaskResultDto call(CallAmazonApiTaskResultDto resultDto) {
        log.info("sp batch create, create product, batch traceId: {}, taskId: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId());
        List<Long> ids = resultDto.getSuccessIdList();
        resultDto.setSuccessIdList(Collections.emptyList());
        if (CollectionUtils.isEmpty(ids)) {
            return resultDto;
        }
        Integer puid = resultDto.getPuid();
        Integer shopId = resultDto.getShopId();
        Long taskId = resultDto.getTaskId();
        String traceId = resultDto.getTraceDto().getTraceId();
        //1、获取redisson分布式锁，锁标识为："sp_batch:product:"+ task_id;
        RLock lock = redissonClient.getLock(SpBatchCreateAdTaskLevelEnum.LEVEL_PRODUCT.getLockKey() + taskId);
        boolean b = false;
        try {
            b = lock.tryLock(SpBatchConstants.TRY_LOCK_SECONDS, SpBatchConstants.LOCK_MINUTES, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.info("product task tryLock err,puid：{},shopId:{},taskId:{},traceId:{}", puid, shopId, taskId, traceId);
            return resultDto;
        }
        if (!b) {
            log.info("product task in progress,puid：{},shopId:{},taskId:{},traceId:{}", puid, shopId, taskId, traceId);
            return resultDto;
        }

        List<AmazonAdBatchProduct> products = new ArrayList<>();
        try {
            ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(resultDto.getShopId(), resultDto.getPuid());
            //2、根据传入的id列表获取不为成功或者终止状态的广告产品集合
            if (resultDto.isCurrentLevel()) {
                products = amazonAdBatchProductDao.listByIdList(puid, shopId, ids,
                        Lists.newArrayList(SpBatchCreateAdLevelStatusEnum.DOING.getCode(), SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode()));
            } else {
                products = amazonAdBatchProductDao.listByGroupIdList(puid, shopId, taskId, ids,
                        Lists.newArrayList(SpBatchCreateAdLevelStatusEnum.DOING.getCode(), SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode()));
            }
            if (CollectionUtils.isEmpty(products)) {
                return resultDto;
            }
            //3、分片请求亚马逊返回列表分成成功失败可重试三种类型
            Map<Integer, String> succMap = new HashMap<>();
            Map<Integer, String> errMap = new HashMap<>();
            List<Integer> retryList = new ArrayList<>();
            List<List<AmazonAdBatchProduct>> productsPartition = Lists.partition(products, SpBatchConstants.REQUEST_PARTITION_SIZE);
            for (int i = 0; i < productsPartition.size(); i++) {
                BatchCreateReturnDto batchCreateReturnDto = this.amazonApiCreateByPartition(traceId, taskId, shop, productsPartition.get(i), i * SpBatchConstants.REQUEST_PARTITION_SIZE);
                succMap.putAll(batchCreateReturnDto.getSuccMap());
                errMap.putAll(batchCreateReturnDto.getErrMap());
                retryList.addAll(batchCreateReturnDto.getRetryList());
            }

            //4、成功处理
            Set<Long> succGroupIdSet = new HashSet<>();
            if (succMap.size() > 0) {
                List<AmazonAdProduct> succProducts = new ArrayList<>();
                Map<Long, String> succBatchProductIdMap = new HashMap<>();
                Date date = new Date();
                for (Map.Entry<Integer, String> succMapEntry : succMap.entrySet()) {
                    AmazonAdBatchProduct amazonAdBatchProduct = products.get(succMapEntry.getKey());
                    succBatchProductIdMap.put(amazonAdBatchProduct.getId(), succMapEntry.getValue());
                    //构建日志的po
                    AmazonAdProduct amazonAdProduct = this.batchProduct2Product(amazonAdBatchProduct);
                    amazonAdProduct.setAdId(succMapEntry.getValue());
                    amazonAdProduct.setCreateTime(date);
                    amazonAdProduct.setUpdateTime(date);
                    succProducts.add(amazonAdProduct);
                    //创建成功的广告组id
                    succGroupIdSet.add(amazonAdBatchProduct.getGroupId());
                }
                //插入成功的广告产品
                amazonAdProductDao.insertOnDuplicateKeyUpdate(puid, succProducts);
                //写入doris
                saveDoris(succProducts);
                //修改当前层级任务的状态为成功
                amazonAdBatchProductDao.updateSuccTaskStatusByIdList(puid, succBatchProductIdMap);
                //记录成功日志
                this.collectSuccessLog(succProducts, resultDto.getLoginIp());
                log.info("sp batch create, create product success, batch traceId: {}, taskId: {}, succBatchProductIdMap: {}", traceId, resultDto.getTaskId(), succBatchProductIdMap);
            }

            //5、失败处理
            Set<Long> errGroupIdSet = new HashSet<>();
            List<AmazonAdProduct> errProducts = new ArrayList<>();
            if (errMap.size() > 0) {
                Map<Long, String> idMsgMap = new HashMap<>();
                for (Map.Entry<Integer, String> errMapEntry : errMap.entrySet()) {
                    AmazonAdBatchProduct amazonAdBatchProduct = products.get(errMapEntry.getKey());
                    idMsgMap.put(amazonAdBatchProduct.getId(), errMapEntry.getValue());
                    errGroupIdSet.add(amazonAdBatchProduct.getGroupId());
                    //构建日志po
                    AmazonAdProduct amazonAdProduct = this.batchProduct2Product(amazonAdBatchProduct);
                    amazonAdProduct.setFailReason(errMapEntry.getValue());
                    errProducts.add(amazonAdProduct);
                }
                //修改当前层级任务的状态为失败
                taskStatusHelper.batchUpdateErrStatus(puid, taskId, idMsgMap, SpBatchCreateAdTaskLevelEnum.LEVEL_PRODUCT,  null, true);
                log.info("sp batch create, create product error, batch traceId: {}, taskId: {}, idMsgMap: {}", traceId, resultDto.getTaskId(), idMsgMap);
            }

            //6、重试处理
            if (retryList.size() > 0) {
                List<Long> retryIdList = new ArrayList<>();
                Map<Long, String> errIdMap = new HashMap<>();
                for (Integer index : retryList) {
                    AmazonAdBatchProduct amazonAdBatchProduct = products.get(index);
                    AmazonAdProduct amazonAdProduct = this.batchProduct2Product(amazonAdBatchProduct);
                    //若重试超过三次则直接置为失败
                    if (amazonAdBatchProduct.getExecuteCount() + 1 >= SpBatchConstants.EXECUTE_COUNT_LIMIT) {
                        errIdMap.put(amazonAdBatchProduct.getId(), SpBatchConstants.RETRY_ERROR_MSG);
                        amazonAdProduct.setFailReason(SpBatchConstants.RETRY_ERROR_MSG);
                        errGroupIdSet.add(amazonAdBatchProduct.getGroupId());
                    } else {
                        retryIdList.add(amazonAdBatchProduct.getId());
                        amazonAdProduct.setFailReason(SpBatchConstants.RETRY_MSG);
                    }
                    errProducts.add(amazonAdProduct);
                }
                if (CollectionUtils.isNotEmpty(retryIdList)) {
                    amazonAdBatchProductDao.updateRetryTaskStatusByIdList(puid, retryIdList, DateUtil.addSecond(new Date(), SpBatchConstants.RETRY_AFTER_SECONDS));
                    log.info("sp batch create, create product retry, batch traceId: {}, taskId: {}, retryIdList: {}", traceId, resultDto.getTaskId(), StringUtils.join(retryIdList, ","));
                }
                if (!errIdMap.isEmpty()) {
                    amazonAdBatchProductDao.updateErrTaskStatusByIdList(puid, errIdMap, true);
                    log.info("sp batch create, create product error, batch traceId: {}, taskId: {}, errIdMap: {}", traceId, resultDto.getTaskId(), errIdMap);
                }
            }

            //7、记录失败日志
            this.collectFailedLog(errProducts, resultDto.getLoginIp());

            //8、广告组下所有广告产品都失败的时，将投放都改成失败状态
            //获取产品全部失败的广告组id（所有广告组id - 成功的广告组id）
            if (CollectionUtils.isNotEmpty(errGroupIdSet)) {
                List<Long> errGroupIdList = new ArrayList<>(errGroupIdSet);
                //过滤掉有提交中、成功或者失败可重试或者终止的非失败不可重试广告组id，即为提交产品全部失败的广告组id
                List<Long> nonErrGroupIds = amazonAdBatchProductDao.listGroupIdByGroupIdListAndTaskStatus(puid, shopId, taskId, errGroupIdList,
                        Lists.newArrayList(SpBatchCreateAdLevelStatusEnum.DOING.getCode(), SpBatchCreateAdLevelStatusEnum.SUCCESS.getCode(), SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode(), SpBatchCreateAdLevelStatusEnum.STOP.getCode()));
                errGroupIdList.removeAll(nonErrGroupIds);
                if (CollectionUtils.isNotEmpty(errGroupIdList)) {
                    //将除自动投放外的投放层全部改为失败
                    taskStatusHelper.batchUpdateUnderProductErrStatus(puid, taskId, errGroupIdList);
                    //自动投放的广告组id加入成功列表，后续继续执行自动投放任务
                    succGroupIdSet.addAll(amazonAdBatchGroupDao.idListByGroupIdListAndType(puid, shopId, taskId, errGroupIdList, Constants.AUTO));
                }
            }
            //返回成功的广告组id
            resultDto.setSuccessIdList(new ArrayList<>(succGroupIdSet));
            log.info("sp batch create, create product end, batch traceId: {}, taskId: {}, successIds: {}", traceId, resultDto.getTaskId(), StringUtils.join(resultDto.getSuccessIdList(), ","));
        } catch (Exception e) {
            //有异常将本层级全部改为重试状态
            log.error(String.format("sp batch create product, exception, batch traceId: %s, taskId: %s", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId()), e);
            if (CollectionUtils.isEmpty(products)) {
                List<Long> idList = products.stream().map(AmazonAdBatchProduct::getId).collect(Collectors.toList());
                amazonAdBatchProductDao.updateRetryTaskStatusByIdList(puid, idList, DateUtil.addSecond(new Date(), SpBatchConstants.RETRY_AFTER_SECONDS));
            }
        } finally {
            //8、释放redission锁
            lock.unlock();
        }
        return resultDto;
    }

    /**
     * 批量调亚马逊创建接口
     * @param shop
     * @param products
     * @param offset 偏移量，返回index加上这个值
     * @return
     */
    private BatchCreateReturnDto amazonApiCreateByPartition(String traceId, Long taskId, ShopAuth shop, List<AmazonAdBatchProduct> products, int offset) {
        BatchCreateReturnDto dto = new BatchCreateReturnDto();
        Map<Integer, String> succMap = new HashMap<>();
        Map<Integer, String> errMap = new HashMap<>();
        List<Integer> retryList = new ArrayList<>();
        dto.setSuccMap(succMap);
        dto.setErrMap(errMap);
        dto.setRetryList(retryList);
        List<Integer> duplicateValueErrorList = new ArrayList<>();
        //组装亚马逊请求
        List<ProductEntityV3> productEntityV3List = Lists.newArrayList();
        for (AmazonAdBatchProduct amazonAdBatchProduct : products) {
            productEntityV3List.add(this.batchProduct2ProductEntityV3(amazonAdBatchProduct));
        }
        //批量将这些数据提交给亚马逊。
        String profileId = products.get(0).getProfileId();
        CreateSpProductV3Response response = ProductSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createProductAd(shop.getAdAccessToken(), profileId,
                shop.getMarketplaceId(), productEntityV3List, true);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            log.info("sp batch create product, response is null or http code not 200, batch traceId: {}, taskId: {}", traceId, taskId);
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = ProductSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createProductAd(shop.getAdAccessToken(), profileId,
                    shop.getMarketplaceId(), productEntityV3List, true);
        }

        //异常返回，全部加入到重试列表
        if (response == null || response.getData() == null || response.getStatusCode() == -1 || response.getStatusCode() == 429 || response.getStatusCode() == 500) {
            log.info("sp batch create, create product response none data, fail all, batch traceId: {}, taskId: {}", traceId, taskId);
            for (int i = 0; i < products.size(); i++) {
                retryList.add(i + offset);
            }
            return dto;
        }

        //4、提交给亚马逊之后，需要处理其响应，将成功的和失败的分成2组。
        String errMsg = "创建广告产品失败";
        if (response.getData() != null && response.getData().getProductAds() != null) {
            List<ProductSuccessResultV3> success = response.getData().getProductAds().getSuccess();
            List<ErrorItemResultV3> errorItemResultV3s = response.getData().getProductAds().getError();
            //成功的广告产品，批量和单个广告产品都设置广告产品id
            for (ProductSuccessResultV3 productSuccessResultV3 : success) {
                succMap.put(offset + productSuccessResultV3.getIndex(), productSuccessResultV3.getAdId());
            }
            //失败的广告产品，批量和单个广告产品都设置错误信息
            for (ErrorItemResultV3 productResult : errorItemResultV3s) {
                //如果错误信息为创建重复，直接设置为创建成功，后续查询广告产品id
                if (AdAmazonErrorTypeEnum.DUPLICATE_VALUE_ERROR.getType().equals(productResult.getErrors().get(0).getErrorType())) {
                    duplicateValueErrorList.add(productResult.getIndex());
                    continue;
                }
                errMap.put(offset + productResult.getIndex(), productResult.getErrors().get(0).getErrorMessage());
            }
        } else if (response.getError() != null) {
            if (StringUtils.isNotBlank(response.getError().getMessage())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if (CollectionUtils.isNotEmpty(response.getError().getErrors())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }
            for (int i = 0; i < products.size(); i++) {
                errMap.put(offset + i, errMsg);
            }
        }

        //对创建重复的调用列表接口获取广告产品id，若有则加入到成功列表中
        if (CollectionUtils.isNotEmpty(duplicateValueErrorList)) {
            Map<Integer, ProductEntityV3> duplicateValueErrorProductMap = duplicateValueErrorList.stream().collect(Collectors.toMap(e -> e, productEntityV3List::get));
            Map<Integer, String> retrySuccMap = this.duplicateProductGetId(shop, profileId, duplicateValueErrorProductMap, offset);
            succMap.putAll(retrySuccMap);
            retryList.addAll(duplicateValueErrorList.stream().filter(e -> !succMap.containsKey(e + offset)).collect(Collectors.toList()));
        }
        return dto;
    }

    /**
     * 创建重复的广告产品获取广告产品adId，并且全部加入到创建成功列表
     */
    private Map<Integer, String> duplicateProductGetId(ShopAuth shop, String profileId, Map<Integer, ProductEntityV3> duplicateValueErrorProductMap, int offset) {
        //获取重复成功的index和adid
        Map<Integer, String> succMap = new HashMap<>();
        //广告产品标识(广告组+sku)与index的map
        Map<String, Integer> skuIndexMap = new HashMap<>();
        //获取创建重复广告产品的活动和广告组
        List<String> campaignIds = new ArrayList<>();
        List<String> adGroupIds = new ArrayList<>();
        duplicateValueErrorProductMap.forEach((k, v) -> {
            campaignIds.add(v.getCampaignId());
            adGroupIds.add(v.getAdGroupId());
            skuIndexMap.put(v.getAdGroupId() + "#" + v.getSku(), k);
        });
        ProductSpV3Client client = ProductSpV3Client.getInstance();
        // 刷新token次数，保证不会无限刷新
        int refreshedToken = 0;
        String nextToken = null;
        ListSpProductV3Response response;
        Map<String, String> skuAdIdMap;
        while (true) {
            response = client.listProduct(shop.getAdAccessToken(), profileId, shop.getMarketplaceId(), campaignIds, adGroupIds, null, nextToken);
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode() == AmazonAdUtils.rateLimitingCode) {
                log.info("batch create product rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = client.listProduct(shop.getAdAccessToken(), profileId, shop.getMarketplaceId(), campaignIds, adGroupIds, null, nextToken);
                retry++;
            }
            //刷新token
            if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401 && refreshedToken < 2) {
                shopAuthService.refreshCpcAuth(shop);
                refreshedToken++;
                continue;
            }
            if (response == null || response.getData() == null || CollectionUtils.isEmpty(response.getData().getProductAds())) {
                break;
            }
            //将成功返回的adId存入成功列表中
            skuAdIdMap = response.getData().getProductAds().stream().collect(Collectors.toMap(e -> e.getAdGroupId() + "#" + e.getSku(), ProductEntityV3::getAdId));
            Iterator<Map.Entry<String, Integer>> iterator = skuIndexMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, Integer> entry = iterator.next();
                if (skuAdIdMap.containsKey(entry.getKey())) {
                    succMap.put(offset + entry.getValue(), skuAdIdMap.get(entry.getKey()));
                    iterator.remove();
                }
            }
            //nextToken刷新
            if (StringUtils.isNotBlank(response.getData().getNextToken())){
                nextToken = response.getData().getNextToken();
            } else {
                break;
            }
        }
        return succMap;
    }

    /**
     * 批量创建广告产品实体类转广告产品实体类
     * @param amazonAdBatchProduct
     * @return
     */
    private AmazonAdProduct batchProduct2Product(AmazonAdBatchProduct amazonAdBatchProduct) {
        AmazonAdProduct amazonAdProduct;
        amazonAdProduct = new AmazonAdProduct();
        amazonAdProduct.setPuid(amazonAdBatchProduct.getPuid());
        amazonAdProduct.setShopId(amazonAdBatchProduct.getShopId());
        amazonAdProduct.setMarketplaceId(amazonAdBatchProduct.getMarketplaceId());
        amazonAdProduct.setAdGroupId(amazonAdBatchProduct.getAmazonAdGroupId());
        amazonAdProduct.setCampaignId(amazonAdBatchProduct.getAmazonCampaignId());
        amazonAdProduct.setProfileId(amazonAdBatchProduct.getProfileId());
        amazonAdProduct.setSku(amazonAdBatchProduct.getSku());
        amazonAdProduct.setAsin(amazonAdBatchProduct.getAsin());
        amazonAdProduct.setState(Constants.ENABLED);
        amazonAdProduct.setCreateId(amazonAdBatchProduct.getCreateId());
        Date date = new Date();
        amazonAdProduct.setCreateTime(date);
        amazonAdProduct.setUpdateTime(date);
        return amazonAdProduct;
    }

    /**
     * 批量创建广告产品类转亚马逊创建产品类
     * @param amazonAdBatchProduct
     * @return
     */
    private ProductEntityV3 batchProduct2ProductEntityV3(AmazonAdBatchProduct amazonAdBatchProduct) {
        ProductEntityV3 productAd = new ProductEntityV3();
        productAd.setCampaignId(amazonAdBatchProduct.getAmazonCampaignId());
        productAd.setAdGroupId(amazonAdBatchProduct.getAmazonAdGroupId());
        productAd.setSku(amazonAdBatchProduct.getSku());
        productAd.setState(SpV3StateEnum.ENABLED.valueV3());
        return productAd;
    }

    /**
     * 记录报错日志
     * @param amazonAdProducts
     * @param loginIp
     */
    private void collectFailedLog(List<AmazonAdProduct> amazonAdProducts, String loginIp) {
        if (CollectionUtils.isEmpty(amazonAdProducts)) {
            return;
        }
        Map<String, List<AmazonAdProduct>> productGroupMap = amazonAdProducts.stream().collect(Collectors.groupingBy(AmazonAdProduct::getAdGroupId));
        Map<String, String> groupMsgMap = new HashMap<>();
        StringBuilder msgSb;
        for (Map.Entry<String, List<AmazonAdProduct>> entry : productGroupMap.entrySet()) {
            msgSb = new StringBuilder();
            List<AmazonAdProduct> productList = entry.getValue();
            for (AmazonAdProduct product : productList) {
                msgSb.append("sku:").append(product.getSku()).append(",desc:").append(product.getFailReason()).append(";");
            }
            groupMsgMap.put(entry.getKey(), msgSb.toString());
        }

        String err = "创建广告产品失败";
        List<AdManageOperationLog> failCollect = amazonAdProducts.stream().map(e -> {
            AdManageOperationLog productLog = manageOperationLogService.getProductLog(null, e);
            productLog.setIp(loginIp);
            productLog.setResult(1);
            productLog.setResultInfo(groupMsgMap.getOrDefault(e.getAdGroupId(), err));
            return productLog;
        }).collect(Collectors.toList());
        manageOperationLogService.batchLogsMergeByAdGroup(failCollect);
    }

    /**
     * 记录成功日志
     * @param amazonAdProducts
     * @param loginIp
     */
    private void collectSuccessLog(List<AmazonAdProduct> amazonAdProducts, String loginIp) {
        if (CollectionUtils.isEmpty(amazonAdProducts)) {
            return;
        }
        List<AdManageOperationLog> succLog = amazonAdProducts.stream().map(e -> {
            AdManageOperationLog productLog = manageOperationLogService.getProductLog(null, e);
            productLog.setIp(loginIp);
            productLog.setResult(0);
            return productLog;
        }).collect(Collectors.toList());
        manageOperationLogService.batchLogsMergeByAdGroup(succLog);
    }


    /**
     * routine load写入doris
     * @param productList
     */
    private void saveDoris(List<AmazonAdProduct> productList) {
        try {
            Date date = new Date();
            List<OdsAmazonAdProduct> collect = productList.stream().map(x -> {
                OdsAmazonAdProduct odsAmazonAdProduct = new OdsAmazonAdProduct();
                BeanUtils.copyProperties(x, odsAmazonAdProduct);
                odsAmazonAdProduct.setCreateTime(date);
                odsAmazonAdProduct.setUpdateTime(date);
                if (StringUtils.isNotBlank(odsAmazonAdProduct.getState())) {
                    odsAmazonAdProduct.setState(odsAmazonAdProduct.getState().toLowerCase());
                }
                return odsAmazonAdProduct;
            }).collect(Collectors.toList());
            dorisService.saveDorisByRoutineLoad(null, collect);
        } catch (Exception e) {
            log.error("sp product save doris error", e);
        }
    }
}
