package com.meiyunji.sponsored.service.multiPlatform.walmart.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingItemReport;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingItemTrends;


import java.util.Map;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description:
 */
public interface IWalmartAdvertisingItemTrendsDao extends IBaseShardingDao<WalmartAdvertisingItemTrends> {

    int add(WalmartAdvertisingItemTrends itemTrends);

    int update(WalmartAdvertisingItemTrends itemTrends);

    int delete(Long id);

    WalmartAdvertisingItemTrends getById(Long id);

    Page getPageList(int puid, int pageNo, int pageSize, Map<String, Object> queryParams);

    int getByReportDate(String reportDate);

    int getByReportDateAll(String reportDate);

    int deleteByReportDate(String reportDate);

    int deleteByReportDateAll(String reportDate);

    WalmartAdvertisingItemTrends getLastItemTrends();
}
