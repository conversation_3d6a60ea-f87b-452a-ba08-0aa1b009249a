package com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-09-18  14:23
 */
@Data
public class CategoryTargetViewAggregateVo extends StreamDataViewVo {
    @ApiModelProperty("匹配方式")
    private String targetType;
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("asin")
    private String asin;
    @ApiModelProperty("图片url")
    private String imgUrl;
    @ApiModelProperty("目录")
    private String category;
    @ApiModelProperty("domain")
    private String domain;
    @ApiModelProperty("商品asin、类目")
    private String targetText;
}
