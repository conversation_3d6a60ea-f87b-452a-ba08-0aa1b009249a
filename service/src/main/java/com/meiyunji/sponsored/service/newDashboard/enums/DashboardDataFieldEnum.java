package com.meiyunji.sponsored.service.newDashboard.enums;

import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 广告看板数据筛选指标条件枚举
 */

public enum DashboardDataFieldEnum {

    COST("cost", "广告花费"),

    TOTAL_SALES("totalSales", "广告销售额"),

    ACOS("acos", "ACoS"),

    ROAS("roas", "ROAS"),
    IMPRESSIONS("impressions", "广告曝光量"),
    CLICKS("clicks", "广告点击量"),
    ORDER_NUM("orderNum", "广告订单量"),
    CLICK_RATE("clickRate", "广告点击率"),
    CONVERSION_RATE("conversionRate", "广告转化率"),
    SALE_NUM("saleNum", "广告销量"),
    CPC("cpc", "CPC"),
    CPA("cpa", "CPA");

    private String code;

    private String desc;


    /**
     * 可以不用聚合直接比较排除为0 的字段为了提高性能；
     */
    public static Set<String> noZeroFieldSet = Lists.newArrayList(COST, TOTAL_SALES, IMPRESSIONS, CLICKS, SALE_NUM, ORDER_NUM).stream()
            .map(DashboardDataFieldEnum::getCode).collect(Collectors.toSet());
    public static Set<String> fieldSet = Arrays.stream(DashboardDataFieldEnum.values()).map(DashboardDataFieldEnum::getCode).collect(Collectors.toSet());

    public static Map<String, DashboardDataFieldEnum> fieldMap = Arrays.stream(DashboardDataFieldEnum.values())
            .collect((Collectors.groupingBy(DashboardDataFieldEnum::getCode,
                    Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0)))));

    DashboardDataFieldEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
