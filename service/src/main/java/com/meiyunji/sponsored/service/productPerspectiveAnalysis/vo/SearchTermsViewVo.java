package com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo;

import com.meiyunji.sponsored.service.cpc.po.AdTag;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-09-12  16:10
 */
@Data
public class SearchTermsViewVo extends StreamDataViewVo {
    @ApiModelProperty("广告活动类型")
    private String type;

    @ApiModelProperty("搜索词")
    private String query;

    @ApiModelProperty("搜索词翻译")
    private String queryCn;

    @ApiModelProperty("搜索词为asin时的图片")
    private String mainImage;

    @ApiModelProperty("搜索词是否为asin，用于前端判断添加到关键词投放还是商品投放")
    private Boolean isAsin;



}
