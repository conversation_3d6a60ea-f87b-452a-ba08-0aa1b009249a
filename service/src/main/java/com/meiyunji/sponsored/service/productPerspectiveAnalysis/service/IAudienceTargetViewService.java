package com.meiyunji.sponsored.service.productPerspectiveAnalysis.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.AudienceTargetViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AudienceTargetViewAggregatePageVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AudienceTargetViewVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.CategoryTargetViewAggregatePageVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.CategoryTargetViewVo;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-09-14  15:34
 */
public interface IAudienceTargetViewService {

    /**
     * 获取商品投放视图列表页（通过feed分页），支持广告类型多选
     * @param puid
     * @param param
     * @return
     */
    Page<AudienceTargetViewVo> getAllAudienceTargetViewPageVoList(Integer puid, AudienceTargetViewParam param);

    /**
     * 获取商品投放视图汇总，支持广告类型多选
     */
    AudienceTargetViewAggregatePageVo getAllAudienceTargetViewAggregatePageVo(Integer puid, AudienceTargetViewParam param);
}
