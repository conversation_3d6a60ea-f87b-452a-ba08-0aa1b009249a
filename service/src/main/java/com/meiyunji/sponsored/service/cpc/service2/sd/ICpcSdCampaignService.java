package com.meiyunji.sponsored.service.cpc.service2.sd;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.rpc.sb.campaign.NewCreateCampaignRequest;
import com.meiyunji.sponsored.rpc.sb.campaign.NewCreateInfoResponse;
import com.meiyunji.sponsored.rpc.sd.campaign.CheckAdsResponseInfo;
import com.meiyunji.sponsored.rpc.sd.campaign.SDCreateCampaignNewRequest;
import com.meiyunji.sponsored.rpc.sd.campaign.SDCreateInfoNewResponse;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdCampaign;
import com.meiyunji.sponsored.service.cpc.vo.*;


import java.util.List;
import java.util.Map;

/**
 * Created by xp on 2021/3/29.
 * 广告活动业务
 */
public interface ICpcSdCampaignService {

    Result showCampaignPerformance(int puid, AdPerformanceParam param);

    List<CampaignPageVo> getList(Integer puid, CampaignPageParam param);

    Result<String> createCampaign(SdCampaignVo vo);

    NewCreateResultResultVo createCampaign(SdCampaignVo vo, ShopAuth shop, AmazonAdProfile profile);

    SDCreateInfoNewResponse submitTogetherCreateCampaign(SDCreateCampaignNewRequest request) throws InterruptedException;

    Result updateCampaign(SdCampaignVo vo);

    Result archive(Integer puid,Integer shopId,Integer uid, String campaignId, String ip);


    Page getPageList(Integer puid, CampaignPageParam param, Page page);

    Result updateBatchCampaign(List<BatchCampaignVo> vos, String type);

    Map<String, String> getSdCampaignCostTypeByCampaignIds(Integer puid, Integer shopId, List<String> campaignIds);

    AmazonAdCampaignAll convertVoToCreatePo(SdCampaignVo vo, AmazonAdProfile amazonAdProfile);
    List<CheckAdsResponseInfo> checkAdsInfoByCampaignId(Integer puid, Integer shopId, String campaignId,
                                                        String filterType);

    }
