package com.meiyunji.sponsored.service.searchTermsAnalysis;

import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.doris.dao.IOdsQuarterSearchTermsAnalysisDao;
import com.meiyunji.sponsored.service.doris.po.OdsQuarterSearchTermsAnalysis;
import com.meiyunji.sponsored.service.searchTermsAnalysis.qo.SearchTermsAnalysisTrendQo;
import com.meiyunji.sponsored.service.searchTermsAnalysis.vo.SearchTermsListTrendsVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-08-06 10:55
 */
@Service
public class QuarterSearchTermsAnalysisService implements SearchTermsAnalysisInterface {
    @Resource
    private IOdsQuarterSearchTermsAnalysisDao quarterSearchTermsAnalysisDao;

    public List<SearchTermsListTrendsVo> getTrend(SearchTermsAnalysisTrendQo param) {
        //获取7个季度
        Set<String> set = this.getLastQuarters(param.getEndDate(), 6);
        String minDay = set.stream().min(Comparator.naturalOrder()).get().split("#")[0];
        List<OdsQuarterSearchTermsAnalysis> list = quarterSearchTermsAnalysisDao.getTrend(param, minDay);
        Map<String, List<OdsQuarterSearchTermsAnalysis>> map = list.stream().collect(Collectors.groupingBy(report ->
                report.getMarketplaceId() + "###" + report.getSearchTerm()));
        List<SearchTermsListTrendsVo> resultList = new ArrayList<>();
        map.forEach((key, value) -> {
            String[] arr = key.split("###", -1);
            String marketplaceId = arr[0];
            String searchTerm = arr[1];
            SearchTermsListTrendsVo vo = new SearchTermsListTrendsVo();
            vo.setSearchTerm(searchTerm);
            vo.setMarketplaceId(marketplaceId);
            List<SearchTermsListTrendsVo.SearchTermsTrendsVo> trendList = new ArrayList<>();
            Map<String, Integer> trendMap = value.stream().collect(Collectors.toMap(report ->
                            DateUtil.dateToStrWithTime(report.getStartDate(), DateUtil.PATTERN) + "#" + DateUtil.dateToStrWithTime(report.getEndDate(), DateUtil.PATTERN),
                    OdsQuarterSearchTermsAnalysis::getSearchFrequencyRank, (report1, report2) -> report1));
            set.forEach(item -> {
                //否则50万名
                // Integer rank = trendMap.get(item) != null ? trendMap.get(item) : 500000;
                Integer rank = trendMap.get(item);
                SearchTermsListTrendsVo.SearchTermsTrendsVo trendVo = new SearchTermsListTrendsVo.SearchTermsTrendsVo();
                trendVo.setStartDate(item.split("#")[0]);
                trendVo.setEndDate(item.split("#")[1]);
                trendVo.setSearchFrequencyRank(rank);
                trendVo.setWeekStr(dealQuarter(item.split("#")[1]));
                trendList.add(trendVo);
            });
            trendList.sort(Comparator.comparing(SearchTermsListTrendsVo.SearchTermsTrendsVo::getStartDate));
            vo.setList(trendList);
            resultList.add(vo);
        });
        return resultList;
    }

    private Set<String> getLastQuarters(String endStr, int num) {
        Set<String> set = new TreeSet<>();
        LocalDate endDay = LocalDate.parse(endStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        for (int i = 0; i < num; i++) {
            Month monthOfQuarterStart = Month.of(endDay.getMonth().firstMonthOfQuarter().getValue());
            //季度第一天
            String startDate = LocalDate.of(endDay.getYear(), monthOfQuarterStart, 1)
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            //季度第最后一天
            Month monthOfQuarterEnd = monthOfQuarterStart.plus(2);
            String endDate = LocalDate.of(endDay.getYear(), monthOfQuarterEnd, monthOfQuarterEnd.maxLength())
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            set.add(startDate + "#" + endDate);
            endDay = endDay.minusMonths(3);
        }
        return set;
    }

    private String dealQuarter(String day) {
        LocalDate startDay = LocalDate.parse(day, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        int num = (startDay.getMonth().getValue() - 1) / 3 + 1;
        return day.substring(2, 4) + "年第" + num + "季度";
    }
}
