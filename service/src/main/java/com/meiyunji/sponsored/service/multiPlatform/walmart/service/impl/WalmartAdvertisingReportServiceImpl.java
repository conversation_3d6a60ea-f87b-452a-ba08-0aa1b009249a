package com.meiyunji.sponsored.service.multiPlatform.walmart.service.impl;


import com.google.api.client.util.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.multiPlatform.shop.dao.IMultiPlatformShopAuthDao;
import com.meiyunji.sponsored.service.multiPlatform.shop.po.MultiPlatformShopAuth;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dao.*;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.*;
import com.meiyunji.sponsored.service.multiPlatform.walmart.service.IWalmartAdvertiserAttributesService;
import com.meiyunji.sponsored.service.multiPlatform.walmart.service.IWalmartAdvertisingReportService;
import com.meiyunji.sponsored.service.multiPlatform.walmart.service.IWalmartAdvertisingSnapshotService;
import com.meiyunji.sponsored.service.multiPlatform.walmart.util.Constants;
import com.meiyunji.sponsored.service.multiPlatform.walmart.util.ImportCsv;
import com.walmart.oms.advertiser.WalmartAdvertiserClient;
import com.walmart.oms.advertiser.base.enums.JobStatusEnum;
import com.walmart.oms.advertiser.model.CreateReportSnapshotResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description walmart广告活动报告ervice
 */
@Service
@Slf4j
public class WalmartAdvertisingReportServiceImpl implements IWalmartAdvertisingReportService {


    @Autowired
    private IMultiPlatformShopAuthDao multiPlatformShopAuthDao;

    @Override
    public List<WalmartAdvertisingGroupReportPage> getPageListShopReportTop(int puid, Long shopId, Map<String, Object> queryParams) {
        return null;
    }

    @Override
    public Page getPageListCampaignReportTop(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
        return null;
    }

    @Override
    public Page getPageListGroupReportTop(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
        return null;
    }

    @Override
    public Page getPageListItemReportTop(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
        return null;
    }

    @Override
    public Page getPageListKeywordReportTop(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
        return null;
    }

    @Override
    public List<Map<String, Object>> getIndicatorList(int puid, Map<String, Object> queryParams) {
        return null;
    }

    @Override
    public List<Map<String, Object>> getIndicatorDateList(int puid, Map<String, Object> queryParams) {
        return null;
    }

    @Override
    public Page getIndicatorDateDetailsPage(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
        return null;
    }

    @Override
    public WalmartAdvertisingGroupReportPage getSumReportDate(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
        return null;
    }

    @Override
    public Map<String, Object> getAdvertisingTransition(int puid, Map<String, Object> queryParams) {
        return null;
    }

    @Override
    public Map<String, Object> getTargetingRatio(int puid, Map<String, Object> queryParams) {
        return null;
    }

    @Autowired
    private IWalmartAdvertiserAttributesService walmartAdvertiserAttributesService;
    @Autowired
    private IWalmartAdvertisingSnapshotService advertisingSnapshotService;
    @Autowired
    private IWalmartAdvertisingGroupReportDao walmartAdvertisingGroupReportDao;
    @Autowired
    private IWalmartAdvertisingItemReportDao walmartAdvertisingItemReportDao;
    @Autowired
    private IWalmartAdvertisingKeywordReportDao walmartAdvertisingKeywordReportDao;
    @Autowired
    private IWalmartAdvertisingPlacementReportDao walmartAdvertisingPlacementReportDao;
    @Autowired
    private IWalmartAdvertisingOutOfBudgetReportDao walmartAdvertisingOutOfBudgetReportDao;
    @Autowired
    private IWalmartAdvertisingSearchImpressionDao walmartAdvertisingSearchImpressionDao;

    @Override
    public void syncReport(Integer puid, Integer shopId, String reportDateManual, String startDateManual, String endDateManual) {
        List<Long> allId = new ArrayList<>();
        if (puid != null && shopId != null) {
            WalmartAdvertiserAttributes attributes = walmartAdvertiserAttributesService.getByShopId(puid, shopId);
            if (attributes != null) {
                allId.add(attributes.getId());
            }
        } else if (puid != null) {
            List<WalmartAdvertiserAttributes> attributesList = walmartAdvertiserAttributesService.getByPuid(puid);
            if (CollectionUtils.isNotEmpty(attributesList)) {
                allId.addAll(attributesList.stream().map(WalmartAdvertiserAttributes::getId).collect(Collectors.toList()));
            }
        } else {
            allId = walmartAdvertiserAttributesService.getAllId();
        }
        executeReport(allId, reportDateManual, startDateManual, endDateManual);
    }

    private void executeReport(List<Long> ids, String reportDateManual, String startDateManual, String endDateManual) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        WalmartAdvertiserClient advertiserClient = new WalmartAdvertiserClient();
        String reportDate = "", startDate = "", endDate = "";
        for (Long id : ids) {
            WalmartAdvertiserAttributes attributes = walmartAdvertiserAttributesService.getById(id);
            if (attributes == null) {
                continue;
            }
            //广告组维度报告
            WalmartAdvertisingGroupReport lastGroupReport = walmartAdvertisingGroupReportDao.getLastGroupReport(attributes.getPuid(), attributes.getShopId());
            if (lastGroupReport == null) {
                startDate = DateUtil.dateToStrNoTime(DateUtil.addDay(new Date(), -90));
                endDate = DateUtil.dateToStrNoTime(DateUtil.addDay(new Date(), -2));
            } else {
                Date startDate1 = getStartDate(lastGroupReport.getReportDate());
                startDate = DateUtil.dateToStrNoTime(DateUtil.addDay(startDate1, 1));
                endDate = DateUtil.dateToStrNoTime(DateUtil.addDay(new Date(), -2));
            }
            reportDate = StringUtils.isNotBlank(reportDateManual) ? reportDateManual : reportDate;
            startDate = StringUtils.isNotBlank(startDateManual) ? startDateManual : startDate;
            endDate = StringUtils.isNotBlank(endDateManual) ? endDateManual : endDate;
            createReport(attributes, advertiserClient, Constants.SNAPSHORT_TYPE_REPORT_GROUP, Constants.REPORT_TYPE_GROUP, Constants.REPORT_METRICS_GROUP, reportDate, startDate, endDate);
            //取数据只能取前两天的数据，美国时间生成前一天数据
            //广告产品维度报告
            WalmartAdvertisingItemReport lastItemReport = walmartAdvertisingItemReportDao.getLastItemReport(attributes.getPuid(), attributes.getShopId());
            if (lastItemReport == null) {
                startDate = DateUtil.dateToStrNoTime(DateUtil.addDay(new Date(), -90));
                endDate = DateUtil.dateToStrNoTime(DateUtil.addDay(new Date(), -2));
            } else {
                Date startDate1 = getStartDate(lastItemReport.getReportDate());
                startDate = DateUtil.dateToStrNoTime(DateUtil.addDay(startDate1, 1));
                endDate = DateUtil.dateToStrNoTime(DateUtil.addDay(new Date(), -2));
            }
            reportDate = StringUtils.isNotBlank(reportDateManual) ? reportDateManual : reportDate;
            startDate = StringUtils.isNotBlank(startDateManual) ? startDateManual : startDate;
            endDate = StringUtils.isNotBlank(endDateManual) ? endDateManual : endDate;
            createReport(attributes, advertiserClient, Constants.SNAPSHORT_TYPE_REPORT_ITEM, Constants.REPORT_TYPE_ITEM, Constants.REPORT_METRICS_ITEM, reportDate, startDate, endDate);

            //关键词维度报告
            WalmartAdvertisingKeywordReport lastKeywordReport = walmartAdvertisingKeywordReportDao.getLastKeywordReport(attributes.getPuid(), attributes.getShopId());
            if (lastKeywordReport == null) {
                startDate = DateUtil.dateToStrNoTime(DateUtil.addDay(new Date(), -90));
                endDate = DateUtil.dateToStrNoTime(DateUtil.addDay(new Date(), -2));
            } else {
                Date startDate1 = getStartDate(lastKeywordReport.getReportDate());
                startDate = DateUtil.dateToStrNoTime(DateUtil.addDay(startDate1, 1));
                endDate = DateUtil.dateToStrNoTime(DateUtil.addDay(new Date(), -2));
            }
            reportDate = StringUtils.isNotBlank(reportDateManual) ? reportDateManual : reportDate;
            startDate = StringUtils.isNotBlank(startDateManual) ? startDateManual : startDate;
            endDate = StringUtils.isNotBlank(endDateManual) ? endDateManual : endDate;
            createReport(attributes, advertiserClient, Constants.SNAPSHORT_TYPE_REPORT_KEYWORD, Constants.REPORT_TYPE_KEYWORD, Constants.REPORT_METRICS_KEYWORD, reportDate, startDate, endDate);

            //广告位维度
            WalmartAdvertisingPlacementReport lastPlacementReport = walmartAdvertisingPlacementReportDao.getLastPlacementReport(attributes.getPuid(), attributes.getShopId());
            if (lastPlacementReport == null) {
                startDate = DateUtil.dateToStrNoTime(DateUtil.addDay(new Date(), -90));
                endDate = DateUtil.dateToStrNoTime(DateUtil.addDay(new Date(), -2));
            } else {
                Date startDate1 = getStartDate(lastPlacementReport.getReportDate());
                startDate = DateUtil.dateToStrNoTime(DateUtil.addDay(startDate1, 1));
                endDate = DateUtil.dateToStrNoTime(DateUtil.addDay(new Date(), -2));
            }
            reportDate = StringUtils.isNotBlank(reportDateManual) ? reportDateManual : reportDate;
            startDate = StringUtils.isNotBlank(startDateManual) ? startDateManual : startDate;
            endDate = StringUtils.isNotBlank(endDateManual) ? endDateManual : endDate;
            createReport(attributes, advertiserClient, Constants.SNAPSHORT_TYPE_REPORT_PLACEMENT, Constants.REPORT_TYPE_PLACEMENT, Constants.REPORT_METRICS_PLACEMENT, reportDate, startDate, endDate);

            //超预算报告
            //createReport(attributes, advertiserClient, Constants.SNAPSHORT_TYPE_REPORT_OUT, Constants.REPORT_TYPE_OUT, Constants.REPORT_METRICS_OUT, "", "", "");

            //搜索词报告
            createReport(attributes, advertiserClient, Constants.SNAPSHORT_TYPE_SEARCH_IMPRESSION, Constants.REPORT_SEARCH_IMPRESSION, Constants.REPORT_METRICS_SEARCH, "", "", "");
        }
    }

    private void createReport(WalmartAdvertiserAttributes attributes, WalmartAdvertiserClient advertiserClient, Integer snapshotType, String reportType, List<String> reportMetrics, String reportDate, String startDate, String endDate) {
        int count = advertisingSnapshotService.getUndoneCountByType(attributes.getPuid(), attributes.getShopId(), snapshotType);
        if (count > 0) {
            log.error("createReportSnapshot:::UndoneCount:::" + count);
            return;
        }
        CreateReportSnapshotResponse response = advertiserClient.createReportSnapshot(attributes.getAdvertiserId(), reportType, reportMetrics, reportDate, startDate, endDate);
        if (response == null) {
            log.info("createReportSnapshot:::response is null");
            return;
        }
        if (StringUtils.equals("failure", response.getCode()) && StringUtils.isNotBlank(response.getDetailsStr())) {
            log.info("createReportSnapshot:::response:::" + JSONUtil.objectToJson(response));
            return;
        }
        if (response.getDetailsObj() != null && StringUtils.isNotBlank(response.getDetailsObj().getDescription())) {
            log.info("createReportSnapshot:::Description:::" + response.getDetailsObj().getDescription());
            return;
        }
        if (StringUtils.isBlank(response.getSnapshotId())) {
            log.info("createReportSnapshot:::SnapshotId is null");
            return;
        }
        WalmartAdvertisingSnapshot snapshot = new WalmartAdvertisingSnapshot();
        snapshot.setPuid(attributes.getPuid());
        snapshot.setShopId(attributes.getShopId());
        snapshot.setSnapshotId(response.getSnapshotId());
        snapshot.setJobStatus(JobStatusEnum.PENDING.getValue());
        snapshot.setType(snapshotType);
        snapshot.setState(Constants.STATE_PENDING);
        advertisingSnapshotService.add(snapshot);
    }

    @Override
    public void snapshotExecute(WalmartAdvertisingSnapshot snapshot, String detailsStr, String jobStatus) {
        List<String[]> csvList;
        try {
            csvList = ImportCsv.readCsvFile(detailsStr);
        } catch (Exception e) {
            advertisingSnapshotService.updateState(snapshot.getId(), Constants.STATE_FINISH, jobStatus, "Walmart:::snapshot:::数据解析异常！");
            return;
        }
        if (CollectionUtils.isEmpty(csvList)) {
            advertisingSnapshotService.updateState(snapshot.getId(), Constants.STATE_FINISH, jobStatus, "Walmart:::snapshot:::csv data is null");
            return;
        }
        if (Constants.SNAPSHORT_TYPE_REPORT_GROUP.equals(snapshot.getType())) {
            saveGroupReport(snapshot.getPuid(), snapshot.getShopId(), csvList);
        }
        if (Constants.SNAPSHORT_TYPE_REPORT_ITEM.equals(snapshot.getType())) {
            saveItemReport(snapshot.getPuid(), snapshot.getShopId(), csvList);
        }
        if (Constants.SNAPSHORT_TYPE_REPORT_KEYWORD.equals(snapshot.getType())) {
            saveKeywordReport(snapshot.getPuid(), snapshot.getShopId(), csvList);
        }
        if (Constants.SNAPSHORT_TYPE_REPORT_PLACEMENT.equals(snapshot.getType())) {
            savePlacementReport(snapshot.getPuid(), snapshot.getShopId(), csvList);
        }
        if (Constants.SNAPSHORT_TYPE_REPORT_OUT.equals(snapshot.getType())) {
            saveOutOfBudgetReport(snapshot.getPuid(), snapshot.getShopId(), csvList);
        }
        if (Constants.SNAPSHORT_TYPE_SEARCH_IMPRESSION.equals(snapshot.getType())) {
            saveSearchImpressionReport(snapshot.getPuid(), snapshot.getShopId(), csvList);
        }
        advertisingSnapshotService.updateState(snapshot.getId(), Constants.STATE_FINISH, jobStatus, "");
    }

    private void saveGroupReport(int puid, Integer shopId, List<String[]> csvList) {
        String[] checkData = csvList.get(0);
        if (checkData.length < Constants.REPORT_METRICS_GROUP.size()) {
            log.info("saveGroupReport:::csv data rowLength error:::" + (StringUtils.isNotBlank(checkData[0]) ? checkData[0] : ""));
            return;
        }


        MultiPlatformShopAuth multiPlatformShopAuth = multiPlatformShopAuthDao.getByIdAndPuid(shopId, puid);
        if (multiPlatformShopAuth == null) {
            log.error("店铺不存在 puid:{}, shopId:{}", puid, shopId);
            return;
        }

        List<WalmartAdvertisingGroupReport> reports = Lists.newArrayListWithCapacity(csvList.size());

        for (String[] data : csvList) {
            if (checkData.length != data.length) {
                continue;
            }
            String reportDate = data[0];
            if (StringUtils.isBlank(reportDate)) {
                continue;
            }
            String campaignId = data[1];
            String adGroupId = data[2];
            if (StringUtils.isBlank(campaignId) || StringUtils.isBlank(adGroupId)) {
                continue;
            }

            String adSpend = data[3];
            String numAdsClicks = data[4];
            String numAdsShown = data[5];
            String advertisedSkuSales3days = data[6];
            String advertisedSkuSales14days = data[7];
            String advertisedSkuSales30days = data[8];
            String advertisedSkuUnits3days = data[9];
            String advertisedSkuUnits14days = data[10];
            String advertisedSkuUnits30days = data[11];
            String attributedOrders3days = data[12];
            String attributedOrders14days = data[13];
            String attributedOrders30days = data[14];
            String attributedUnits3days = data[15];
            String attributedUnits14days = data[16];
            String attributedUnits30days = data[17];
            String attributedSales3days = data[18];
            String attributedSales14days = data[19];
            String attributedSales30days = data[20];
            String otherSkuSales3days = data[21];
            String otherSkuSales14days = data[22];
            String otherSkuSales30days = data[23];
            String otherSkuUnits3days = data[24];
            String otherSkuUnits14days = data[25];
            String otherSkuUnits30days = data[26];

            WalmartAdvertisingGroupReport groupReport = new WalmartAdvertisingGroupReport();
            groupReport.setReportDate(DateUtil.strToDate4(reportDate));
            groupReport.setCampaignId(campaignId);
            groupReport.setAdGroupId(adGroupId);
            if (StringUtils.isNotBlank(adSpend)) {
                groupReport.setAdSpend(Double.valueOf(adSpend));
            }
            if (StringUtils.isNotBlank(numAdsClicks)) {
                groupReport.setNumAdsClicks(Integer.valueOf(numAdsClicks));
            }
            if (StringUtils.isNotBlank(numAdsShown)) {
                groupReport.setNumAdsShown(Integer.valueOf(numAdsShown));
            }
            if (StringUtils.isNotBlank(advertisedSkuSales3days)) {
                groupReport.setAdvertisedSkuSales3days(Double.valueOf(advertisedSkuSales3days));
            }
            if (StringUtils.isNotBlank(advertisedSkuSales14days)) {
                groupReport.setAdvertisedSkuSales14days(Double.valueOf(advertisedSkuSales14days));
            }
            if (StringUtils.isNotBlank(advertisedSkuSales30days)) {
                groupReport.setAdvertisedSkuSales30days(Double.valueOf(advertisedSkuSales30days));
            }
            if (StringUtils.isNotBlank(advertisedSkuUnits3days)) {
                groupReport.setAdvertisedSkuUnits3days(Integer.valueOf(advertisedSkuUnits3days));
            }
            if (StringUtils.isNotBlank(advertisedSkuUnits14days)) {
                groupReport.setAdvertisedSkuUnits14days(Integer.valueOf(advertisedSkuUnits14days));
            }
            if (StringUtils.isNotBlank(advertisedSkuUnits30days)) {
                groupReport.setAdvertisedSkuUnits30days(Integer.valueOf(advertisedSkuUnits30days));
            }
            if (StringUtils.isNotBlank(attributedOrders3days)) {
                groupReport.setAttributedOrders3days(Integer.valueOf(attributedOrders3days));
            }
            if (StringUtils.isNotBlank(attributedOrders14days)) {
                groupReport.setAttributedOrders14days(Integer.valueOf(attributedOrders14days));
            }
            if (StringUtils.isNotBlank(attributedOrders30days)) {
                groupReport.setAttributedOrders30days(Integer.valueOf(attributedOrders30days));
            }

            if (StringUtils.isNotBlank(attributedUnits3days)) {
                groupReport.setAttributedUnits3days(Integer.valueOf(attributedUnits3days));
            }
            if (StringUtils.isNotBlank(attributedUnits14days)) {
                groupReport.setAttributedUnits14days(Integer.valueOf(attributedUnits14days));
            }
            if (StringUtils.isNotBlank(attributedUnits30days)) {
                groupReport.setAttributedUnits30days(Integer.valueOf(attributedUnits30days));
            }

            if (StringUtils.isNotBlank(attributedSales3days)) {
                groupReport.setAttributedSales3days(Double.valueOf(attributedSales3days));
            }
            if (StringUtils.isNotBlank(attributedSales14days)) {
                groupReport.setAttributedSales14days(Double.valueOf(attributedSales14days));
            }
            if (StringUtils.isNotBlank(attributedSales30days)) {
                groupReport.setAttributedSales30days(Double.valueOf(attributedSales30days));
            }

            if (StringUtils.isNotBlank(otherSkuSales3days)) {
                groupReport.setOtherSkuSales3days(Double.valueOf(otherSkuSales3days));
            }
            if (StringUtils.isNotBlank(otherSkuSales14days)) {
                groupReport.setOtherSkuSales14days(Double.valueOf(otherSkuSales14days));
            }
            if (StringUtils.isNotBlank(otherSkuSales30days)) {
                groupReport.setOtherSkuSales30days(Double.valueOf(otherSkuSales30days));
            }

            if (StringUtils.isNotBlank(otherSkuUnits3days)) {
                groupReport.setOtherSkuUnits3days(Integer.valueOf(otherSkuUnits3days));
            }
            if (StringUtils.isNotBlank(otherSkuUnits14days)) {
                groupReport.setOtherSkuUnits14days(Integer.valueOf(otherSkuUnits14days));
            }
            if (StringUtils.isNotBlank(otherSkuUnits30days)) {
                groupReport.setOtherSkuUnits30days(Integer.valueOf(otherSkuUnits30days));
            }

            groupReport.setPuid(puid);
            groupReport.setShopId(shopId);
            groupReport.setMarketplaceCode(multiPlatformShopAuth.getMarketplaceCode());
            reports.add(groupReport);
        }
        if (CollectionUtils.isNotEmpty(reports)) {
            walmartAdvertisingGroupReportDao.insertOrUpdate(puid, reports);
        }
    }

    private void saveItemReport(int puid, Integer shopId, List<String[]> csvList) {
        String[] checkData = csvList.get(0);
        if (checkData.length < Constants.REPORT_METRICS_ITEM.size()) {
            log.info("saveItemReport:::csv data rowLength error:::" + (StringUtils.isNotBlank(checkData[0]) ? checkData[0] : ""));
            return;
        }


        MultiPlatformShopAuth multiPlatformShopAuth = multiPlatformShopAuthDao.getByIdAndPuid(shopId, puid);
        if (multiPlatformShopAuth == null) {
            log.error("店铺不存在 puid:{}, shopId:{}", puid, shopId);
            return;
        }

        List<WalmartAdvertisingItemReport> reports = Lists.newArrayListWithCapacity(csvList.size());

        for (String[] data : csvList) {
            if (checkData.length != data.length) {
                continue;
            }
            String reportDate = data[0];
            if (StringUtils.isBlank(reportDate)) {
                continue;
            }
            String campaignId = data[1];
            String adGroupId = data[2];
            String itemId = data[3];
            if (StringUtils.isBlank(campaignId) || StringUtils.isBlank(adGroupId) || StringUtils.isBlank(itemId)) {
                continue;
            }

            String adSpend = data[4];
            String numAdsClicks = data[5];
            String numAdsShown = data[6];
            String advertisedSkuSales3days = data[7];
            String advertisedSkuSales14days = data[8];
            String advertisedSkuSales30days = data[9];
            String advertisedSkuUnits3days = data[10];
            String advertisedSkuUnits14days = data[11];
            String advertisedSkuUnits30days = data[12];
            String attributedOrders3days = data[13];
            String attributedOrders14days = data[14];
            String attributedOrders30days = data[15];

            String attributedUnits3days = data[16];
            String attributedUnits14days = data[17];
            String attributedUnits30days = data[18];
            String attributedSales3days = data[19];
            String attributedSales14days = data[20];
            String attributedSales30days = data[21];
            String otherSkuSales3days = data[22];
            String otherSkuSales14days = data[23];
            String otherSkuSales30days = data[24];
            String otherSkuUnits3days = data[25];
            String otherSkuUnits14days = data[26];
            String otherSkuUnits30days = data[27];

            String inStoreAdvertisedSales3days = data[28];
            String inStoreAdvertisedSales14days = data[29];
            String inStoreAdvertisedSales30days = data[30];
            String inStoreAttributedSales3days = data[31];
            String inStoreAttributedSales14days = data[32];
            String inStoreAttributedSales30days = data[33];
            String inStoreOrders3days = data[34];
            String inStoreOrders14days = data[35];
            String inStoreOrders30days = data[36];
            String inStoreOtherSales3days = data[37];
            String inStoreOtherSales14days = data[38];
            String inStoreOtherSales30days = data[39];
            String inStoreUnitsSold3days = data[40];
            String inStoreUnitsSold14days = data[41];
            String inStoreUnitsSold30days = data[42];


            WalmartAdvertisingItemReport itemReport = new WalmartAdvertisingItemReport();
            itemReport.setReportDate(DateUtil.strToDate4(reportDate));
            itemReport.setCampaignId(campaignId);
            itemReport.setAdGroupId(adGroupId);
            itemReport.setItemId(itemId);

            if (StringUtils.isNotBlank(adSpend)) {
                itemReport.setAdSpend(Double.valueOf(adSpend));
            }
            if (StringUtils.isNotBlank(numAdsClicks)) {
                itemReport.setNumAdsClicks(Integer.valueOf(numAdsClicks));
            }
            if (StringUtils.isNotBlank(numAdsShown)) {
                itemReport.setNumAdsShown(Integer.valueOf(numAdsShown));
            }
            if (StringUtils.isNotBlank(advertisedSkuSales3days)) {
                itemReport.setAdvertisedSkuSales3days(Double.valueOf(advertisedSkuSales3days));
            }
            if (StringUtils.isNotBlank(advertisedSkuSales14days)) {
                itemReport.setAdvertisedSkuSales14days(Double.valueOf(advertisedSkuSales14days));
            }
            if (StringUtils.isNotBlank(advertisedSkuSales30days)) {
                itemReport.setAdvertisedSkuSales30days(Double.valueOf(advertisedSkuSales30days));
            }
            if (StringUtils.isNotBlank(advertisedSkuUnits3days)) {
                itemReport.setAdvertisedSkuUnits3days(Integer.valueOf(advertisedSkuUnits3days));
            }
            if (StringUtils.isNotBlank(advertisedSkuUnits14days)) {
                itemReport.setAdvertisedSkuUnits14days(Integer.valueOf(advertisedSkuUnits14days));
            }
            if (StringUtils.isNotBlank(advertisedSkuUnits30days)) {
                itemReport.setAdvertisedSkuUnits30days(Integer.valueOf(advertisedSkuUnits30days));
            }
            if (StringUtils.isNotBlank(attributedOrders3days)) {
                itemReport.setAttributedOrders3days(Integer.valueOf(attributedOrders3days));
            }
            if (StringUtils.isNotBlank(attributedOrders14days)) {
                itemReport.setAttributedOrders14days(Integer.valueOf(attributedOrders14days));
            }
            if (StringUtils.isNotBlank(attributedOrders30days)) {
                itemReport.setAttributedOrders30days(Integer.valueOf(attributedOrders30days));
            }

            if (StringUtils.isNotBlank(attributedUnits3days)) {
                itemReport.setAttributedUnits3days(Integer.valueOf(attributedUnits3days));
            }
            if (StringUtils.isNotBlank(attributedUnits14days)) {
                itemReport.setAttributedUnits14days(Integer.valueOf(attributedUnits14days));
            }
            if (StringUtils.isNotBlank(attributedUnits30days)) {
                itemReport.setAttributedUnits30days(Integer.valueOf(attributedUnits30days));
            }

            if (StringUtils.isNotBlank(attributedSales3days)) {
                itemReport.setAttributedSales3days(Double.valueOf(attributedSales3days));
            }
            if (StringUtils.isNotBlank(attributedSales14days)) {
                itemReport.setAttributedSales14days(Double.valueOf(attributedSales14days));
            }
            if (StringUtils.isNotBlank(attributedSales30days)) {
                itemReport.setAttributedSales30days(Double.valueOf(attributedSales30days));
            }

            if (StringUtils.isNotBlank(otherSkuSales3days)) {
                itemReport.setOtherSkuSales3days(Double.valueOf(otherSkuSales3days));
            }
            if (StringUtils.isNotBlank(otherSkuSales14days)) {
                itemReport.setOtherSkuSales14days(Double.valueOf(otherSkuSales14days));
            }
            if (StringUtils.isNotBlank(otherSkuSales30days)) {
                itemReport.setOtherSkuSales30days(Double.valueOf(otherSkuSales30days));
            }

            if (StringUtils.isNotBlank(otherSkuUnits3days)) {
                itemReport.setOtherSkuUnits3days(Integer.valueOf(otherSkuUnits3days));
            }
            if (StringUtils.isNotBlank(otherSkuUnits14days)) {
                itemReport.setOtherSkuUnits14days(Integer.valueOf(otherSkuUnits14days));
            }
            if (StringUtils.isNotBlank(otherSkuUnits30days)) {
                itemReport.setOtherSkuUnits30days(Integer.valueOf(otherSkuUnits30days));
            }

            if (StringUtils.isNotBlank(inStoreAdvertisedSales3days)) {
                itemReport.setInStoreAdvertisedSales3days(Double.valueOf(inStoreAdvertisedSales3days));
            }
            if (StringUtils.isNotBlank(inStoreAdvertisedSales14days)) {
                itemReport.setInStoreAdvertisedSales14days(Double.valueOf(inStoreAdvertisedSales14days));
            }
            if (StringUtils.isNotBlank(inStoreAdvertisedSales30days)) {
                itemReport.setInStoreAdvertisedSales30days(Double.valueOf(inStoreAdvertisedSales30days));
            }

            if (StringUtils.isNotBlank(inStoreAttributedSales3days)) {
                itemReport.setInStoreAttributedSales3days(Double.valueOf(inStoreAttributedSales3days));
            }
            if (StringUtils.isNotBlank(inStoreAttributedSales14days)) {
                itemReport.setInStoreAttributedSales14days(Double.valueOf(inStoreAttributedSales14days));
            }
            if (StringUtils.isNotBlank(inStoreAttributedSales30days)) {
                itemReport.setInStoreAttributedSales30days(Double.valueOf(inStoreAttributedSales30days));
            }

            if (StringUtils.isNotBlank(inStoreOrders3days)) {
                itemReport.setInStoreOrders3days(Integer.valueOf(inStoreOrders3days));
            }
            if (StringUtils.isNotBlank(inStoreOrders14days)) {
                itemReport.setInStoreOrders14days(Integer.valueOf(inStoreOrders14days));
            }
            if (StringUtils.isNotBlank(inStoreOrders30days)) {
                itemReport.setInStoreOrders30days(Integer.valueOf(inStoreOrders30days));
            }

            if (StringUtils.isNotBlank(inStoreOtherSales3days)) {
                itemReport.setInStoreOtherSales3days(Double.valueOf(inStoreOtherSales3days));
            }
            if (StringUtils.isNotBlank(inStoreOtherSales14days)) {
                itemReport.setInStoreOtherSales14days(Double.valueOf(inStoreOtherSales14days));
            }
            if (StringUtils.isNotBlank(inStoreOtherSales30days)) {
                itemReport.setInStoreOtherSales30days(Double.valueOf(inStoreOtherSales30days));
            }

            if (StringUtils.isNotBlank(inStoreUnitsSold3days)) {
                itemReport.setInStoreUnitsSold3days(Integer.valueOf(inStoreUnitsSold3days));
            }
            if (StringUtils.isNotBlank(inStoreUnitsSold14days)) {
                itemReport.setInStoreUnitsSold14days(Integer.valueOf(inStoreUnitsSold14days));
            }
            if (StringUtils.isNotBlank(inStoreUnitsSold30days)) {
                itemReport.setInStoreUnitsSold30days(Integer.valueOf(inStoreUnitsSold30days));
            }

            itemReport.setPuid(puid);
            itemReport.setShopId(shopId);
            itemReport.setMarketplaceCode(multiPlatformShopAuth.getMarketplaceCode());
            reports.add(itemReport);
        }
        if (CollectionUtils.isNotEmpty(reports)) {
            walmartAdvertisingItemReportDao.insertOrUpdate(puid, reports);
        }
    }

    private void saveKeywordReport(int puid, Integer shopId, List<String[]> csvList) {
        String[] checkData = csvList.get(0);
        if (checkData.length < Constants.REPORT_METRICS_KEYWORD.size()) {
            log.info("saveKeywordReport:::csv data rowLength error:::" + (StringUtils.isNotBlank(checkData[0]) ? checkData[0] : ""));
            return;
        }

        MultiPlatformShopAuth multiPlatformShopAuth = multiPlatformShopAuthDao.getByIdAndPuid(shopId, puid);
        if (multiPlatformShopAuth == null) {
            log.error("店铺不存在 puid:{}, shopId:{}", puid, shopId);
            return;
        }

        List<WalmartAdvertisingKeywordReport> reports = Lists.newArrayListWithCapacity(csvList.size());

        
        for (String[] data : csvList) {
            if (checkData.length != data.length) {
                continue;
            }
            String reportDate = data[0];
            if (StringUtils.isBlank(reportDate)) {
                continue;
            }
            String campaignId = data[1];
            String adGroupId = data[2];
            String keywordId = data[3];
            if (StringUtils.isBlank(campaignId) || StringUtils.isBlank(adGroupId) || StringUtils.isBlank(keywordId)) {
                continue;
            }
            String adSpend = data[4];
            String numAdsClicks = data[5];
            String numAdsShown = data[6];
            String advertisedSkuSales3days = data[7];
            String advertisedSkuSales14days = data[8];
            String advertisedSkuSales30days = data[9];
            String advertisedSkuUnits3days = data[10];
            String advertisedSkuUnits14days = data[11];
            String advertisedSkuUnits30days = data[12];
            String attributedOrders3days = data[13];
            String attributedOrders14days = data[14];
            String attributedOrders30days = data[15];

            String attributedUnits3days = data[16];
            String attributedUnits14days = data[17];
            String attributedUnits30days = data[18];
            String attributedSales3days = data[19];
            String attributedSales14days = data[20];
            String attributedSales30days = data[21];
            String otherSkuSales3days = data[22];
            String otherSkuSales14days = data[23];
            String otherSkuSales30days = data[24];
            String otherSkuUnits3days = data[25];
            String otherSkuUnits14days = data[26];
            String otherSkuUnits30days = data[27];

            String inStoreAdvertisedSales3days = data[28];
            String inStoreAdvertisedSales14days = data[29];
            String inStoreAdvertisedSales30days = data[30];
            String inStoreAttributedSales3days = data[31];
            String inStoreAttributedSales14days = data[32];
            String inStoreAttributedSales30days = data[33];
            String inStoreOrders3days = data[34];
            String inStoreOrders14days = data[35];
            String inStoreOrders30days = data[36];
            String inStoreOtherSales3days = data[37];
            String inStoreOtherSales14days = data[38];
            String inStoreOtherSales30days = data[39];
            String inStoreUnitsSold3days = data[40];
            String inStoreUnitsSold14days = data[41];
            String inStoreUnitsSold30days = data[42];

            WalmartAdvertisingKeywordReport keywordReport = new WalmartAdvertisingKeywordReport();
            keywordReport.setReportDate(DateUtil.strToDate4(reportDate));
            keywordReport.setCampaignId(campaignId);
            keywordReport.setAdGroupId(adGroupId);
            keywordReport.setKeywordId(keywordId);
            if (StringUtils.isNotBlank(adSpend)) {
                keywordReport.setAdSpend(Double.valueOf(adSpend));
            }
            if (StringUtils.isNotBlank(numAdsClicks)) {
                keywordReport.setNumAdsClicks(Integer.valueOf(numAdsClicks));
            }
            if (StringUtils.isNotBlank(numAdsShown)) {
                keywordReport.setNumAdsShown(Integer.valueOf(numAdsShown));
            }
            if (StringUtils.isNotBlank(advertisedSkuSales3days)) {
                keywordReport.setAdvertisedSkuSales3days(Double.valueOf(advertisedSkuSales3days));
            }
            if (StringUtils.isNotBlank(advertisedSkuSales14days)) {
                keywordReport.setAdvertisedSkuSales14days(Double.valueOf(advertisedSkuSales14days));
            }
            if (StringUtils.isNotBlank(advertisedSkuSales30days)) {
                keywordReport.setAdvertisedSkuSales30days(Double.valueOf(advertisedSkuSales30days));
            }
            if (StringUtils.isNotBlank(advertisedSkuUnits3days)) {
                keywordReport.setAdvertisedSkuUnits3days(Integer.valueOf(advertisedSkuUnits3days));
            }
            if (StringUtils.isNotBlank(advertisedSkuUnits14days)) {
                keywordReport.setAdvertisedSkuUnits14days(Integer.valueOf(advertisedSkuUnits14days));
            }
            if (StringUtils.isNotBlank(advertisedSkuUnits30days)) {
                keywordReport.setAdvertisedSkuUnits30days(Integer.valueOf(advertisedSkuUnits30days));
            }
            if (StringUtils.isNotBlank(attributedOrders3days)) {
                keywordReport.setAttributedOrders3days(Integer.valueOf(attributedOrders3days));
            }
            if (StringUtils.isNotBlank(attributedOrders14days)) {
                keywordReport.setAttributedOrders14days(Integer.valueOf(attributedOrders14days));
            }
            if (StringUtils.isNotBlank(attributedOrders30days)) {
                keywordReport.setAttributedOrders30days(Integer.valueOf(attributedOrders30days));
            }

            if (StringUtils.isNotBlank(attributedUnits3days)) {
                keywordReport.setAttributedUnits3days(Integer.valueOf(attributedUnits3days));
            }
            if (StringUtils.isNotBlank(attributedUnits14days)) {
                keywordReport.setAttributedUnits14days(Integer.valueOf(attributedUnits14days));
            }
            if (StringUtils.isNotBlank(attributedUnits30days)) {
                keywordReport.setAttributedUnits30days(Integer.valueOf(attributedUnits30days));
            }

            if (StringUtils.isNotBlank(attributedSales3days)) {
                keywordReport.setAttributedSales3days(Double.valueOf(attributedSales3days));
            }
            if (StringUtils.isNotBlank(attributedSales14days)) {
                keywordReport.setAttributedSales14days(Double.valueOf(attributedSales14days));
            }
            if (StringUtils.isNotBlank(attributedSales30days)) {
                keywordReport.setAttributedSales30days(Double.valueOf(attributedSales30days));
            }

            if (StringUtils.isNotBlank(otherSkuSales3days)) {
                keywordReport.setOtherSkuSales3days(Double.valueOf(otherSkuSales3days));
            }
            if (StringUtils.isNotBlank(otherSkuSales14days)) {
                keywordReport.setOtherSkuSales14days(Double.valueOf(otherSkuSales14days));
            }
            if (StringUtils.isNotBlank(otherSkuSales30days)) {
                keywordReport.setOtherSkuSales30days(Double.valueOf(otherSkuSales30days));
            }

            if (StringUtils.isNotBlank(otherSkuUnits3days)) {
                keywordReport.setOtherSkuUnits3days(Integer.valueOf(otherSkuUnits3days));
            }
            if (StringUtils.isNotBlank(otherSkuUnits14days)) {
                keywordReport.setOtherSkuUnits14days(Integer.valueOf(otherSkuUnits14days));
            }
            if (StringUtils.isNotBlank(otherSkuUnits30days)) {
                keywordReport.setOtherSkuUnits30days(Integer.valueOf(otherSkuUnits30days));
            }

            if (StringUtils.isNotBlank(inStoreAdvertisedSales3days)) {
                keywordReport.setInStoreAdvertisedSales3days(Double.valueOf(inStoreAdvertisedSales3days));
            }
            if (StringUtils.isNotBlank(inStoreAdvertisedSales14days)) {
                keywordReport.setInStoreAdvertisedSales14days(Double.valueOf(inStoreAdvertisedSales14days));
            }
            if (StringUtils.isNotBlank(inStoreAdvertisedSales30days)) {
                keywordReport.setInStoreAdvertisedSales30days(Double.valueOf(inStoreAdvertisedSales30days));
            }

            if (StringUtils.isNotBlank(inStoreAttributedSales3days)) {
                keywordReport.setInStoreAttributedSales3days(Double.valueOf(inStoreAttributedSales3days));
            }
            if (StringUtils.isNotBlank(inStoreAttributedSales14days)) {
                keywordReport.setInStoreAttributedSales14days(Double.valueOf(inStoreAttributedSales14days));
            }
            if (StringUtils.isNotBlank(inStoreAttributedSales30days)) {
                keywordReport.setInStoreAttributedSales30days(Double.valueOf(inStoreAttributedSales30days));
            }

            if (StringUtils.isNotBlank(inStoreOrders3days)) {
                keywordReport.setInStoreOrders3days(Integer.valueOf(inStoreOrders3days));
            }
            if (StringUtils.isNotBlank(inStoreOrders14days)) {
                keywordReport.setInStoreOrders14days(Integer.valueOf(inStoreOrders14days));
            }
            if (StringUtils.isNotBlank(inStoreOrders30days)) {
                keywordReport.setInStoreOrders30days(Integer.valueOf(inStoreOrders30days));
            }

            if (StringUtils.isNotBlank(inStoreOtherSales3days)) {
                keywordReport.setInStoreOtherSales3days(Double.valueOf(inStoreOtherSales3days));
            }
            if (StringUtils.isNotBlank(inStoreOtherSales14days)) {
                keywordReport.setInStoreOtherSales14days(Double.valueOf(inStoreOtherSales14days));
            }
            if (StringUtils.isNotBlank(inStoreOtherSales30days)) {
                keywordReport.setInStoreOtherSales30days(Double.valueOf(inStoreOtherSales30days));
            }

            if (StringUtils.isNotBlank(inStoreUnitsSold3days)) {
                keywordReport.setInStoreUnitsSold3days(Integer.valueOf(inStoreUnitsSold3days));
            }
            if (StringUtils.isNotBlank(inStoreUnitsSold14days)) {
                keywordReport.setInStoreUnitsSold14days(Integer.valueOf(inStoreUnitsSold14days));
            }
            if (StringUtils.isNotBlank(inStoreUnitsSold30days)) {
                keywordReport.setInStoreUnitsSold30days(Integer.valueOf(inStoreUnitsSold30days));
            }

            keywordReport.setPuid(puid);
            keywordReport.setShopId(shopId);
            keywordReport.setMarketplaceCode(multiPlatformShopAuth.getMarketplaceCode());
            reports.add(keywordReport);
        }
        if (CollectionUtils.isNotEmpty(reports)) {
            walmartAdvertisingKeywordReportDao.insertOrUpdate(puid, reports);
        }
    }

    private void savePlacementReport(int puid, Integer shopId, List<String[]> csvList) {
        String[] checkData = csvList.get(0);
        if (checkData.length < Constants.REPORT_METRICS_PLACEMENT.size()) {
            log.info("savePlacementReport:::csv data rowLength error:::" + (StringUtils.isNotBlank(checkData[0]) ? checkData[0] : ""));
            return;
        }

        MultiPlatformShopAuth multiPlatformShopAuth = multiPlatformShopAuthDao.getByIdAndPuid(shopId, puid);
        if (multiPlatformShopAuth == null) {
            log.error("店铺不存在 puid:{}, shopId:{}", puid, shopId);
            return;
        }

        List<WalmartAdvertisingPlacementReport> reports = Lists.newArrayListWithCapacity(csvList.size());


        for (String[] data : csvList) {
            if (checkData.length != data.length) {
                continue;
            }
            String reportDate = data[0];
            if (StringUtils.isBlank(reportDate)) {
                continue;
            }
            String campaignId = data[1];
            String placement = data[2];
            if (StringUtils.isBlank(campaignId) || StringUtils.isBlank(placement)) {
                continue;
            }
            String adSpend = data[3];
            String numAdsClicks = data[4];
            String numAdsShown = data[5];
            String advertisedSkuSales3days = data[6];
            String advertisedSkuSales14days = data[7];
            String advertisedSkuSales30days = data[8];
            String advertisedSkuUnits3days = data[9];
            String advertisedSkuUnits14days = data[10];
            String advertisedSkuUnits30days = data[11];
            String attributedOrders3days = data[12];
            String attributedOrders14days = data[13];
            String attributedOrders30days = data[14];

            String attributedUnits3days = data[15];
            String attributedUnits14days = data[16];
            String attributedUnits30days = data[17];
            String attributedSales3days = data[18];
            String attributedSales14days = data[19];
            String attributedSales30days = data[20];
            String otherSkuSales3days = data[21];
            String otherSkuSales14days = data[22];
            String otherSkuSales30days = data[23];
            String otherSkuUnits3days = data[24];
            String otherSkuUnits14days = data[25];
            String otherSkuUnits30days = data[26];

            String inStoreAdvertisedSales3days = data[27];
            String inStoreAdvertisedSales14days = data[28];
            String inStoreAdvertisedSales30days = data[29];
            String inStoreAttributedSales3days = data[30];
            String inStoreAttributedSales14days = data[31];
            String inStoreAttributedSales30days = data[32];
            String inStoreOrders3days = data[33];
            String inStoreOrders14days = data[34];
            String inStoreOrders30days = data[35];
            String inStoreOtherSales3days = data[36];
            String inStoreOtherSales14days = data[37];
            String inStoreOtherSales30days = data[38];
            String inStoreUnitsSold3days = data[39];
            String inStoreUnitsSold14days = data[40];
            String inStoreUnitsSold30days = data[41];

            WalmartAdvertisingPlacementReport placementReport = new WalmartAdvertisingPlacementReport();
            placementReport.setReportDate(DateUtil.strToDate4(reportDate));
            placementReport.setCampaignId(campaignId);
            placementReport.setPlacement(placement);
            if (StringUtils.isNotBlank(adSpend)) {
                placementReport.setAdSpend(Double.valueOf(adSpend));
            }
            if (StringUtils.isNotBlank(numAdsClicks)) {
                placementReport.setNumAdsClicks(Integer.valueOf(numAdsClicks));
            }
            if (StringUtils.isNotBlank(numAdsShown)) {
                placementReport.setNumAdsShown(Integer.valueOf(numAdsShown));
            }
            if (StringUtils.isNotBlank(advertisedSkuSales3days)) {
                placementReport.setAdvertisedSkuSales3days(Double.valueOf(advertisedSkuSales3days));
            }
            if (StringUtils.isNotBlank(advertisedSkuSales14days)) {
                placementReport.setAdvertisedSkuSales14days(Double.valueOf(advertisedSkuSales14days));
            }
            if (StringUtils.isNotBlank(advertisedSkuSales30days)) {
                placementReport.setAdvertisedSkuSales30days(Double.valueOf(advertisedSkuSales30days));
            }
            if (StringUtils.isNotBlank(advertisedSkuUnits3days)) {
                placementReport.setAdvertisedSkuUnits3days(Integer.valueOf(advertisedSkuUnits3days));
            }
            if (StringUtils.isNotBlank(advertisedSkuUnits14days)) {
                placementReport.setAdvertisedSkuUnits14days(Integer.valueOf(advertisedSkuUnits14days));
            }
            if (StringUtils.isNotBlank(advertisedSkuUnits30days)) {
                placementReport.setAdvertisedSkuUnits30days(Integer.valueOf(advertisedSkuUnits30days));
            }
            if (StringUtils.isNotBlank(attributedOrders3days)) {
                placementReport.setAttributedOrders3days(Integer.valueOf(attributedOrders3days));
            }
            if (StringUtils.isNotBlank(attributedOrders14days)) {
                placementReport.setAttributedOrders14days(Integer.valueOf(attributedOrders14days));
            }
            if (StringUtils.isNotBlank(attributedOrders30days)) {
                placementReport.setAttributedOrders30days(Integer.valueOf(attributedOrders30days));
            }

            if (StringUtils.isNotBlank(attributedUnits3days)) {
                placementReport.setAttributedUnits3days(Integer.valueOf(attributedUnits3days));
            }
            if (StringUtils.isNotBlank(attributedUnits14days)) {
                placementReport.setAttributedUnits14days(Integer.valueOf(attributedUnits14days));
            }
            if (StringUtils.isNotBlank(attributedUnits30days)) {
                placementReport.setAttributedUnits30days(Integer.valueOf(attributedUnits30days));
            }

            if (StringUtils.isNotBlank(attributedSales3days)) {
                placementReport.setAttributedSales3days(Double.valueOf(attributedSales3days));
            }
            if (StringUtils.isNotBlank(attributedSales14days)) {
                placementReport.setAttributedSales14days(Double.valueOf(attributedSales14days));
            }
            if (StringUtils.isNotBlank(attributedSales30days)) {
                placementReport.setAttributedSales30days(Double.valueOf(attributedSales30days));
            }

            if (StringUtils.isNotBlank(otherSkuSales3days)) {
                placementReport.setOtherSkuSales3days(Double.valueOf(otherSkuSales3days));
            }
            if (StringUtils.isNotBlank(otherSkuSales14days)) {
                placementReport.setOtherSkuSales14days(Double.valueOf(otherSkuSales14days));
            }
            if (StringUtils.isNotBlank(otherSkuSales30days)) {
                placementReport.setOtherSkuSales30days(Double.valueOf(otherSkuSales30days));
            }

            if (StringUtils.isNotBlank(otherSkuUnits3days)) {
                placementReport.setOtherSkuUnits3days(Integer.valueOf(otherSkuUnits3days));
            }
            if (StringUtils.isNotBlank(otherSkuUnits14days)) {
                placementReport.setOtherSkuUnits14days(Integer.valueOf(otherSkuUnits14days));
            }
            if (StringUtils.isNotBlank(otherSkuUnits30days)) {
                placementReport.setOtherSkuUnits30days(Integer.valueOf(otherSkuUnits30days));
            }

            if (StringUtils.isNotBlank(inStoreAdvertisedSales3days)) {
                placementReport.setInStoreAdvertisedSales3days(Double.valueOf(inStoreAdvertisedSales3days));
            }
            if (StringUtils.isNotBlank(inStoreAdvertisedSales14days)) {
                placementReport.setInStoreAdvertisedSales14days(Double.valueOf(inStoreAdvertisedSales14days));
            }
            if (StringUtils.isNotBlank(inStoreAdvertisedSales30days)) {
                placementReport.setInStoreAdvertisedSales30days(Double.valueOf(inStoreAdvertisedSales30days));
            }

            if (StringUtils.isNotBlank(inStoreAttributedSales3days)) {
                placementReport.setInStoreAttributedSales3days(Double.valueOf(inStoreAttributedSales3days));
            }
            if (StringUtils.isNotBlank(inStoreAttributedSales14days)) {
                placementReport.setInStoreAttributedSales14days(Double.valueOf(inStoreAttributedSales14days));
            }
            if (StringUtils.isNotBlank(inStoreAttributedSales30days)) {
                placementReport.setInStoreAttributedSales30days(Double.valueOf(inStoreAttributedSales30days));
            }

            if (StringUtils.isNotBlank(inStoreOrders3days)) {
                placementReport.setInStoreOrders3days(Integer.valueOf(inStoreOrders3days));
            }
            if (StringUtils.isNotBlank(inStoreOrders14days)) {
                placementReport.setInStoreOrders14days(Integer.valueOf(inStoreOrders14days));
            }
            if (StringUtils.isNotBlank(inStoreOrders30days)) {
                placementReport.setInStoreOrders30days(Integer.valueOf(inStoreOrders30days));
            }

            if (StringUtils.isNotBlank(inStoreOtherSales3days)) {
                placementReport.setInStoreOtherSales3days(Double.valueOf(inStoreOtherSales3days));
            }
            if (StringUtils.isNotBlank(inStoreOtherSales14days)) {
                placementReport.setInStoreOtherSales14days(Double.valueOf(inStoreOtherSales14days));
            }
            if (StringUtils.isNotBlank(inStoreOtherSales30days)) {
                placementReport.setInStoreOtherSales30days(Double.valueOf(inStoreOtherSales30days));
            }

            if (StringUtils.isNotBlank(inStoreUnitsSold3days)) {
                placementReport.setInStoreUnitsSold3days(Integer.valueOf(inStoreUnitsSold3days));
            }
            if (StringUtils.isNotBlank(inStoreUnitsSold14days)) {
                placementReport.setInStoreUnitsSold14days(Integer.valueOf(inStoreUnitsSold14days));
            }
            if (StringUtils.isNotBlank(inStoreUnitsSold30days)) {
                placementReport.setInStoreUnitsSold30days(Integer.valueOf(inStoreUnitsSold30days));
            }
            placementReport.setPuid(puid);
            placementReport.setShopId(shopId);
            placementReport.setMarketplaceCode(multiPlatformShopAuth.getMarketplaceCode());
            reports.add(placementReport);
        }
        if (CollectionUtils.isNotEmpty(reports)) {
            walmartAdvertisingPlacementReportDao.insertOrUpdate(puid, reports);
        }
    }

    private void saveOutOfBudgetReport(int puid, Integer shopId, List<String[]> csvList) {
        String[] checkData = csvList.get(0);
        if (checkData.length < Constants.REPORT_METRICS_OUT.size()) {
            log.info("saveOutOfBudgetReport:::csv data rowLength error:::" + (StringUtils.isNotBlank(checkData[0]) ? checkData[0] : ""));
            return;
        }
        MultiPlatformShopAuth multiPlatformShopAuth = multiPlatformShopAuthDao.getByIdAndPuid(shopId, puid);
        if (multiPlatformShopAuth == null) {
            log.error("店铺不存在 puid:{}, shopId:{}", puid, shopId);
            return;
        }
        List<WalmartAdvertisingOutOfBudgetReport> reports = Lists.newArrayListWithCapacity(csvList.size());
        for (String[] data : csvList) {
            if (checkData.length != data.length) {
                continue;
            }
            String startDate = data[0];
            String endDate = data[1];
            String campaignId = data[2];
            if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate) || StringUtils.isBlank(campaignId)) {
                continue;
            }
            String suggestedLatestDailyBudget = data[3];
            String suggestedLatestTotalBudget = data[4];

            WalmartAdvertisingOutOfBudgetReport outOfBudgetReport = new WalmartAdvertisingOutOfBudgetReport();
            outOfBudgetReport.setStartDate(DateUtil.strToDate4(startDate));
            outOfBudgetReport.setEndDate(DateUtil.strToDate4(endDate));
            outOfBudgetReport.setCampaignId(campaignId);
            if (StringUtils.isNotBlank(suggestedLatestDailyBudget)) {
                outOfBudgetReport.setSuggestedLatestDailyBudget(Double.valueOf(suggestedLatestDailyBudget));
            }
            if (StringUtils.isNotBlank(suggestedLatestTotalBudget)) {
                outOfBudgetReport.setSuggestedLatestTotalBudget(Double.valueOf(suggestedLatestTotalBudget));
            }

            outOfBudgetReport.setPuid(puid);
            outOfBudgetReport.setShopId(shopId);
            outOfBudgetReport.setMarketplaceCode(multiPlatformShopAuth.getMarketplaceCode());
            outOfBudgetReport.setCountDate(DateUtil.strToDate4(DateUtil.dateToStrNoTime(new Date())));
            reports.add(outOfBudgetReport);
        }

        if (CollectionUtils.isNotEmpty(reports)) {
            walmartAdvertisingOutOfBudgetReportDao.insertOrUpdate(puid, reports);
        }

    }


    private void saveSearchImpressionReport(int puid, Integer shopId, List<String[]> csvList) {
        String[] checkData = csvList.get(0);
        if (checkData.length < Constants.REPORT_METRICS_SEARCH.size()) {
            log.info("saveKeywordReport:::csv data rowLength error:::" + (StringUtils.isNotBlank(checkData[0]) ? checkData[0] : ""));
            return;
        }


        MultiPlatformShopAuth multiPlatformShopAuth = multiPlatformShopAuthDao.getByIdAndPuid(shopId, puid);
        if (multiPlatformShopAuth == null) {
            log.error("店铺不存在 puid:{}, shopId:{}", puid, shopId);
            return;
        }
        List<WalmartAdvertisingSearchImpressionReport> reports = new ArrayList<>();
        for (String[] data : csvList) {
            if (checkData.length != data.length) {
                continue;
            }
            String reportDate = data[0];
            if (StringUtils.isBlank(reportDate)) {
                continue;
            }
            String searchedKeyword = data[1];
            if (StringUtils.isBlank(searchedKeyword)) {
                continue;
            }

            String campaignId = data[2];
            if (StringUtils.isBlank(campaignId)) {
                continue;
            }

            String numAdsShown = data[3];
            String searchedKeywordImpressionShare = data[4];
            String searchedKeywordImpressionRank = data[5];
            String tosSearchedKeywordImpressionShare = data[6];
            String tosSearchedKeywordImpressionRank = data[7];
            String numAdsClicks = data[8];
            String adSpend = data[9];
            String attributedOrders3days = data[10];
            String attributedOrders14days = data[11];
            String attributedOrders30days = data[12];
            String advertisedSkuSales3days = data[13];
            String advertisedSkuSales14days = data[14];
            String advertisedSkuSales30days = data[15];
            String otherSkuSales3days = data[16];
            String otherSkuSales14days = data[17];
            String otherSkuSales30days = data[18];
            String advertisedSkuUnits3days = data[19];
            String advertisedSkuUnits14days = data[20];
            String advertisedSkuUnits30days = data[21];
            String otherSkuUnits3days = data[22];
            String otherSkuUnits14days = data[23];
            String otherSkuUnits30days = data[24];

            WalmartAdvertisingSearchImpressionReport searchImpressionReport = new WalmartAdvertisingSearchImpressionReport();
            searchImpressionReport.setReportDate(DateUtil.strToDate4(reportDate));
            searchImpressionReport.setCampaignId(campaignId);
            searchImpressionReport.setSearchedKeyword(searchedKeyword);


            if (StringUtils.isNotBlank(searchedKeywordImpressionShare)) {
                searchImpressionReport.setSearchedKeywordImpressionShare(searchedKeywordImpressionShare);
            }
            if (StringUtils.isNotBlank(searchedKeywordImpressionRank)) {
                searchImpressionReport.setSearchedKeywordImpressionRank(searchedKeywordImpressionRank);
            }

            if (StringUtils.isNotBlank(tosSearchedKeywordImpressionShare)) {
                searchImpressionReport.setTosSearchedKeywordImpressionShare(tosSearchedKeywordImpressionShare);
            }
            if (StringUtils.isNotBlank(tosSearchedKeywordImpressionRank)) {
                searchImpressionReport.setTosSearchedKeywordImpressionRank(tosSearchedKeywordImpressionRank);
            }

            if (StringUtils.isNotBlank(adSpend)) {
                searchImpressionReport.setAdSpend(Double.valueOf(adSpend));
            }
            if (StringUtils.isNotBlank(numAdsClicks)) {
                searchImpressionReport.setNumAdsClicks(Integer.valueOf(numAdsClicks));
            }
            if (StringUtils.isNotBlank(numAdsShown)) {
                searchImpressionReport.setNumAdsShown(Integer.valueOf(numAdsShown));
            }
            if (StringUtils.isNotBlank(advertisedSkuSales3days)) {
                searchImpressionReport.setAdvertisedSkuSales3days(Double.valueOf(advertisedSkuSales3days));
            }
            if (StringUtils.isNotBlank(advertisedSkuSales14days)) {
                searchImpressionReport.setAdvertisedSkuSales14days(Double.valueOf(advertisedSkuSales14days));
            }
            if (StringUtils.isNotBlank(advertisedSkuSales30days)) {
                searchImpressionReport.setAdvertisedSkuSales30days(Double.valueOf(advertisedSkuSales30days));
            }
            if (StringUtils.isNotBlank(advertisedSkuUnits3days)) {
                searchImpressionReport.setAdvertisedSkuUnits3days(Integer.valueOf(advertisedSkuUnits3days));
            }
            if (StringUtils.isNotBlank(advertisedSkuUnits14days)) {
                searchImpressionReport.setAdvertisedSkuUnits14days(Integer.valueOf(advertisedSkuUnits14days));
            }
            if (StringUtils.isNotBlank(advertisedSkuUnits30days)) {
                searchImpressionReport.setAdvertisedSkuUnits30days(Integer.valueOf(advertisedSkuUnits30days));
            }
            if (StringUtils.isNotBlank(attributedOrders3days)) {
                searchImpressionReport.setAttributedOrders3days(Integer.valueOf(attributedOrders3days));
            }
            if (StringUtils.isNotBlank(attributedOrders14days)) {
                searchImpressionReport.setAttributedOrders14days(Integer.valueOf(attributedOrders14days));
            }
            if (StringUtils.isNotBlank(attributedOrders30days)) {
                searchImpressionReport.setAttributedOrders30days(Integer.valueOf(attributedOrders30days));
            }


            if (StringUtils.isNotBlank(otherSkuSales3days)) {
                searchImpressionReport.setOtherSkuSales3days(Double.valueOf(otherSkuSales3days));
            }
            if (StringUtils.isNotBlank(otherSkuSales14days)) {
                searchImpressionReport.setOtherSkuSales14days(Double.valueOf(otherSkuSales14days));
            }
            if (StringUtils.isNotBlank(otherSkuSales30days)) {
                searchImpressionReport.setOtherSkuSales30days(Double.valueOf(otherSkuSales30days));
            }

            if (StringUtils.isNotBlank(otherSkuUnits3days)) {
                searchImpressionReport.setOtherSkuUnits3days(Integer.valueOf(otherSkuUnits3days));
            }
            if (StringUtils.isNotBlank(otherSkuUnits14days)) {
                searchImpressionReport.setOtherSkuUnits14days(Integer.valueOf(otherSkuUnits14days));
            }
            if (StringUtils.isNotBlank(otherSkuUnits30days)) {
                searchImpressionReport.setOtherSkuUnits30days(Integer.valueOf(otherSkuUnits30days));
            }
            searchImpressionReport.setAttributedSales3days(MathUtil.add(searchImpressionReport.getAdvertisedSkuSales3days(),searchImpressionReport.getOtherSkuSales3days()));
            searchImpressionReport.setAttributedSales14days(MathUtil.add(searchImpressionReport.getAdvertisedSkuSales14days(),searchImpressionReport.getOtherSkuSales14days()));
            searchImpressionReport.setAttributedSales30days(MathUtil.add(searchImpressionReport.getAdvertisedSkuSales30days(),searchImpressionReport.getOtherSkuSales30days()));

            searchImpressionReport.setAttributedUnits3days(MathUtil.add(searchImpressionReport.getAdvertisedSkuUnits3days(),searchImpressionReport.getOtherSkuUnits3days()));
            searchImpressionReport.setAttributedUnits14days(MathUtil.add(searchImpressionReport.getAdvertisedSkuUnits14days(),searchImpressionReport.getOtherSkuUnits14days()));
            searchImpressionReport.setAttributedUnits30days(MathUtil.add(searchImpressionReport.getAdvertisedSkuUnits30days(),searchImpressionReport.getOtherSkuUnits30days()));

            searchImpressionReport.setPuid(puid);
            searchImpressionReport.setShopId(shopId);
            searchImpressionReport.setMarketplaceCode(multiPlatformShopAuth.getMarketplaceCode());
            reports.add(searchImpressionReport);
        }
        if (CollectionUtils.isNotEmpty(reports)) {
            List<List<WalmartAdvertisingSearchImpressionReport>> partition = ListUtils.partition(reports, 2000);
            partition.forEach(e->{
                walmartAdvertisingSearchImpressionDao.insertOrUpdateList(puid, e);
            });
        }
    }


    /**
     *  开始时间如果大于14天直接返回，如果开始小于14天，返回14天时间
     * @param date
     * @return
     */
    private Date getStartDate (Date date) {
        // 获取当前时间
        Calendar calendar = Calendar.getInstance();
        // 将日历时间设置为当前时间
        calendar.setTime(new Date());
        // 从当前时间减去 14 天
        calendar.add(Calendar.DAY_OF_YEAR, -14);
        // 获取 14 天前的时间
        Date twoWeeksAgo = calendar.getTime();
        // 比较目标日期是否在 14 天前之前
        if (date.before(twoWeeksAgo)) {
            return date;
        } else {
            return twoWeeksAgo;
        }
    }

}
