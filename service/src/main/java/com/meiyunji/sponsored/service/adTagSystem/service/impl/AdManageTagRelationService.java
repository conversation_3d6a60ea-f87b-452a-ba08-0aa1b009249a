package com.meiyunji.sponsored.service.adTagSystem.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.impl.ShopAuthDaoImpl;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.adTagSystem.dao.IAdManageTagDao;
import com.meiyunji.sponsored.service.adTagSystem.dao.IAdManageTagRelationDao;
import com.meiyunji.sponsored.service.adTagSystem.dao.impl.AdManageTagDaoGroupUserImpl;
import com.meiyunji.sponsored.service.adTagSystem.enums.AdManageTagTypeEnum;
import com.meiyunji.sponsored.service.adTagSystem.po.AdManageTag;
import com.meiyunji.sponsored.service.adTagSystem.po.AdManageTagRelation;
import com.meiyunji.sponsored.service.adTagSystem.service.IAdManageTagRelationService;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdMarkupTagVo;
import com.meiyunji.sponsored.service.cpc.vo.MakeAdTagVo;
import com.meiyunji.sponsored.service.doris.dao.IOdsAdManageTagRelationDao;
import com.meiyunji.sponsored.service.doris.po.OdsAdManageTagRelation;
import com.meiyunji.sponsored.service.doris.service.impl.DorisServiceImpl;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyStatusSequenceDao;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-10-23  16:14
 */
@Service
@Slf4j
public class AdManageTagRelationService implements IAdManageTagRelationService {

    @Autowired
    private IOdsAdManageTagRelationDao odsAdManageTagRelationDao;
    @Autowired
    private IAdManageTagRelationDao adManageTagRelationDao;
    @Autowired
    private DorisServiceImpl dorisService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    public AdvertiseStrategyStatusSequenceDao advertiseStrategyStatusSequenceDao;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;
    @Resource
    private AdManageTagDaoGroupUserImpl adManageTagDaoGroupUser;
    @Resource
    private IAdManageTagDao adManageTagDao;

    @Override
    public List<String> getRelationIdByTagId(int puid, String tagId, int type, List<Integer> shopIds) {
        List<String> relationIds = new ArrayList<>();
        //通过标签Id获取关联Id
        if (StringUtils.isNotBlank(tagId)) {
            relationIds = odsAdManageTagRelationDao.getRelationIdByTagId(puid, tagId, type, shopIds);
        }
        return relationIds;
    }

    @Override
    public List<String> getRelationIdByTagIds(Integer puid, List<String> temporaryIds, int type, List<Integer> shopIds) {
        List<String> relationIds = new ArrayList<>();
        //通过标签Ids获取关联Id
        if (CollectionUtils.isNotEmpty(temporaryIds)) {
            relationIds = odsAdManageTagRelationDao.getRelationIdByTagIds(puid, temporaryIds, type, shopIds);
        }
        return relationIds;
    }

    @Override
    public List<AdMarkupTagVo> getTagIdByRelationId(Integer puid, int type, List<String> campaignIds, List<Long> groupId) {
        List<AdMarkupTagVo> relationVos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            relationVos = odsAdManageTagRelationDao.getTagIdByRelationId(puid, type, campaignIds, groupId);
            //设置标签Ids
            if (CollectionUtils.isEmpty(relationVos)) {
                return relationVos;
            }
            relationVos.forEach(i -> {
                String tagIdsStr = i.getTagIdsStr();
                if (StringUtils.isNotBlank(tagIdsStr)) {
                    i.setTagIdsList(tagIdsStr);
                }
            });
        }
        return relationVos;
    }

    @Override
    public Result makeAdTag(Integer puid, int type, List<AdManageTagRelation> adTagRelations, List<Long> addTagIdList, List<String> addCampaignIdList) {
        //查询广告活动被标记的标签数量 单个广告活动只能添加5个标签
        List<String> relations = adTagRelations.stream().map(AdManageTagRelation::getRelationId).collect(Collectors.toList());
        List<AdManageTagRelation> tags = new ArrayList<>();
        List<AdMarkupTagVo> vos = odsAdManageTagRelationDao.getTagIdByRelationId(puid, type, relations);
        Map<String, List<Long>> map = new HashMap<>();
        for (AdMarkupTagVo vo : vos) {
            String str = vo.getTagIdsStr();
            if (StringUtils.isBlank(str)) {
                continue;
            }
            vo.setTagIdsList(str);
            Set<Long> tagIds = new HashSet<>(vo.getTagIds());
            tagIds.addAll(addTagIdList);
            if (tagIds.size() > 5) {
                if (addCampaignIdList.size() == 1) {
                    return ResultUtil.error(-1, "无法添加；原因：单个广告活动的手动标签数不能超过5个（包括其他人添加的、没给你看的标签）");
                } else {
                    return ResultUtil.error(-1, "添加失败；原因：部分广告活动已有5个标签，无法再添加标签了");
                }
            }
        }
        //单个标签限制3000个广告活动
        if (CollectionUtils.isNotEmpty(addCampaignIdList) && addCampaignIdList.size() > dynamicRefreshConfiguration.getAdTagSystemCampaignPageTagCampaignLimit()) {
            return ResultUtil.error(-1, "添加失败。1个标签最多添加" + dynamicRefreshConfiguration.getAdTagSystemCampaignPageTagCampaignLimit() + "个广告活动（包括其他人添加的、其他店铺的广告活动）");
        }
        Map<Long, List<String>> tagIdRelationIdsMap = odsAdManageTagRelationDao.getByTagIds(puid, type, adTagRelations.stream().map(AdManageTagRelation::getTagId).distinct().collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(OdsAdManageTagRelation::getTagId, Collectors.mapping(OdsAdManageTagRelation::getRelationId, Collectors.toList())));
        for (List<String> campaignList : tagIdRelationIdsMap.values()) {
            Set<String> campaignIds = new HashSet<>(campaignList);
            campaignIds.addAll(addCampaignIdList);
            if (campaignIds.size() > dynamicRefreshConfiguration.getAdTagSystemCampaignPageTagCampaignLimit()) {
                return ResultUtil.error(-1, "添加失败。1个标签最多添加" + dynamicRefreshConfiguration.getAdTagSystemCampaignPageTagCampaignLimit() + "个广告活动（包括其他人添加的、其他店铺的广告活动）");
            }
        }
        //根据活动id查询出该广告活动下所有的标签id作为map
        //剔除掉该广告活动下已存在的标签，在对其进行插入
        if (CollectionUtils.isNotEmpty(vos)) {
            map = vos.stream().collect(Collectors.toMap(AdMarkupTagVo::getRelationId, AdMarkupTagVo::getTagIds, (v1, v2) -> v2));
        }
        for (AdManageTagRelation adTagRelation : adTagRelations) {
            List<Long> tagList = map.get(adTagRelation.getRelationId());
            if (CollectionUtils.isNotEmpty(tagList) && !tagList.contains(adTagRelation.getTagId())) {
                tags.add(adTagRelation);
            } else if (CollectionUtils.isEmpty(tagList)) {
                tags.add(adTagRelation);
            }
        }
        try {
            if (tags.isEmpty()) {
                return ResultUtil.success("success");
            }
            //写入mysql
            List<AdManageTagRelation> tagRelations = adManageTagRelationDao.insertList(puid, type, tags);
            //写入doris
            // todo 并发可能会出现数据不一致的问题
            LocalDateTime now = LocalDateTime.now();
            Date date = Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
            List<OdsAdManageTagRelation> odsAdTagRelations = new ArrayList<>();
            tagRelations.forEach(i -> {
                OdsAdManageTagRelation odsAdManageTagRelation = new OdsAdManageTagRelation();
                BeanUtils.copyProperties(i, odsAdManageTagRelation);
                odsAdManageTagRelation.setType(type);
                odsAdManageTagRelation.setDelFlag(0L);
                odsAdManageTagRelation.setCreateTime(date);
                odsAdManageTagRelation.setUpdateTime(date);
                odsAdTagRelations.add(odsAdManageTagRelation);
            });
            dorisService.saveDoris(odsAdTagRelations);
        } catch (Exception e) {
            log.warn("广告管理-广告活动-标签系统-标记标签：puid：{}，标记失败，e: {}", puid, e);
        }
        return ResultUtil.success("success");
    }

    @Override
    public void updateGroupIdByTagId(int puid, int type, Long tagId, Long groupId) {
        adManageTagRelationDao.updateGroupIdByTagId(puid, type, tagId, groupId);
        List<AdManageTagRelation> relations = adManageTagRelationDao.listByTagId(puid, type, tagId);
        if (CollectionUtils.isNotEmpty(relations)) {
            List<OdsAdManageTagRelation> odsRelations = relations.stream().map(i -> {
                OdsAdManageTagRelation odsRelation = new OdsAdManageTagRelation();
                BeanUtils.copyProperties(i, odsRelation);
                return odsRelation;
            }).collect(Collectors.toList());
            dorisService.saveDoris(odsRelations);
        }
    }

    @Override
    public void deleteByTagGroupIds(int puid, List<Integer> shopIdList, List<Long> tagGroupIds) {
        if (CollectionUtils.isEmpty(tagGroupIds)) {
            return;
        }
        adManageTagRelationDao.deleteByTagGroupIds(puid, shopIdList, tagGroupIds);
        List<AdManageTagRelation> relations = adManageTagRelationDao.listDelByGroupIds(puid, tagGroupIds);
        if (CollectionUtils.isNotEmpty(relations)) {
            List<OdsAdManageTagRelation> odsRelations = relations.stream().map(i -> {
                OdsAdManageTagRelation odsRelation = new OdsAdManageTagRelation();
                BeanUtils.copyProperties(i, odsRelation);
                return odsRelation;
            }).collect(Collectors.toList());
            dorisService.saveDoris(odsRelations);
        }
    }

    @Override
    public void deleteByTagIds(int puid, List<Integer> shopIdList, List<Long> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) {
            return;
        }
        adManageTagRelationDao.deleteByTagIds(puid, shopIdList, tagIds);
        List<AdManageTagRelation> relations = adManageTagRelationDao.listDelByTagIds(puid, tagIds);
        if (CollectionUtils.isNotEmpty(relations)) {
            List<OdsAdManageTagRelation> odsRelations = relations.stream().map(i -> {
                OdsAdManageTagRelation odsRelation = new OdsAdManageTagRelation();
                BeanUtils.copyProperties(i, odsRelation);
                return odsRelation;
            }).collect(Collectors.toList());
            dorisService.saveDoris(odsRelations);
        }
    }

    @Override
    public Result clearMarkAdTag(Integer puid, MakeAdTagVo adTagVo, List<String> relationIds, List<Integer> shopIds) {
        if (CollectionUtils.isEmpty(relationIds)) {
            return ResultUtil.error("请求参数错误，要清除的id为空");
        }
        List<ShopAuth> shopAuth = shopAuthDao.getAuthShopByShopIdList(puid ,shopIds);
        if(shopAuth == null){
            return ResultUtil.error("店铺不存在");
        }
        //广告层级-广告活动
        Integer type = AdManageTagTypeEnum.getCodeByType(adTagVo.getType());
        if (type == null) {
            return ResultUtil.error("暂不支持该层级类型");
        }
        //清除标记
        List<AdManageTagRelation> tagRelations = adManageTagRelationDao.getListByTagIdAndRelations(puid, type, adTagVo.getAdTagIdList(), shopIds, relationIds);
        List<OdsAdManageTagRelation> odsAdTagRelations = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        Date date = Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
        tagRelations.forEach(i -> {
            OdsAdManageTagRelation odsAdManageTagRelation = new OdsAdManageTagRelation();
            BeanUtils.copyProperties(i, odsAdManageTagRelation);
            odsAdManageTagRelation.setDelFlag(i.getId());
            odsAdManageTagRelation.setUpdateTime(date);
            odsAdTagRelations.add(odsAdManageTagRelation);
        });
        try {
            //更新mysql
            adManageTagRelationDao.clearMarkAdTag(puid, type, tagRelations);
            BeanUtils.copyProperties(tagRelations, odsAdTagRelations);
            //更新doris
            dorisService.saveDoris(odsAdTagRelations);
        } catch (Exception e) {
            log.error(String.format("sp batch create submit error, batch traceId: %s",puid), e);
            return ResultUtil.error("标签标记失败");
        }
        return ResultUtil.success("success");
    }

    @Override
    public Map<String, List<AdTag>> getRelationTagInfo(Integer puid, Integer uid, Integer type, List<String> relationIdList) {
        Map<String, List<AdTag>> map = new HashMap<>();
        if(CollectionUtil.isEmpty(relationIdList)){
            return map;
        }
        // 获取用户可见标签组
        List<Long> groupIdList = adManageTagDaoGroupUser.getTagGroup(puid, uid, type);
        if(CollectionUtil.isEmpty(groupIdList)){
            return map;
        }
        // 获取活动id标签关联关系
        List<AdMarkupTagVo> relationVos = getTagIdByRelationId(puid, type, relationIdList, groupIdList);
        if(CollectionUtil.isEmpty(relationVos)){
            return map;
        }
        // 根据标签Ids获取相关信息
        List<Long> adIds = relationVos.stream().map(AdMarkupTagVo::getTagIds).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        List<AdManageTag> tagList = adManageTagDao.getListByIdList(puid, type, adIds);
        // 组装数据返回
        Map<String, AdMarkupTagVo> relationMap = StreamUtil.toMap(relationVos, AdMarkupTagVo::getRelationId);
        Map<Long, AdManageTag> tagMap = StreamUtil.toMap(tagList, AdManageTag::getId);
        for (String relationId : relationIdList) {
            AdMarkupTagVo adMarkupTagVo = relationMap.get(relationId);
            if(adMarkupTagVo == null){
                continue;
            }
            List<AdManageTag> relationTagList = adMarkupTagVo.getTagIds().stream().map(tagMap::get).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(relationTagList)) {
                List<AdTag> adTagList = BeanUtil.copyToList(relationTagList, AdTag.class);
                map.put(relationId, adTagList);
            }
        }
        return map;
    }
}
