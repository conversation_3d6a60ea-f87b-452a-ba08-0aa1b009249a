package com.meiyunji.sponsored.service.enums;

import com.google.common.collect.Maps;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: Nick
 * @create: 2020-07-30
 **/
public enum UserPositionEnum {

    ADMIN("admin", "管理员"),
    SALESMAN("salesman", "业务员"),
    DEVELOPER("developer", "开发员"),
    OTHER("other", "其它");

    private String name;
    private String chineseName;

    UserPositionEnum(String name, String chineseName) {
        this.name = name;
        this.chineseName = chineseName;
    }

    public String getName() {
        return name;
    }

    public String getChineseName() {
        return chineseName;
    }

    public static Map<String, String> getAllType(){
        HashMap<String, String> map = Maps.newHashMap();
        for (UserPositionEnum orderStatus : values()) {
            map.put(orderStatus.getName(), orderStatus.getChineseName());
        }
        return map;
    }

    public static UserPositionEnum fromValue(String value){
        for (UserPositionEnum orderStatus : values()) {
            if (orderStatus.getName().equals(value)) {
                return orderStatus;
            }
        }
        return null;
    }
}