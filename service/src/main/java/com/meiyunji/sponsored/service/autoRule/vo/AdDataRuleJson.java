package com.meiyunji.sponsored.service.autoRule.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 广告数据条件
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AdDataRuleJson implements Serializable {
    /**
     * 数据范围
     */
    @JsonProperty("day")
    private Integer day;
    /**
     * 数据范围
     */
    @JsonProperty("excludeDay")
    private Integer excludeDay;
    /**
     * 指标
     */
    @JsonProperty("ruleIndex")
    private String ruleIndex;
    /**
     * 统计方式
     */
    @JsonProperty("ruleStatisticalModeType")
    private String ruleStatisticalModeType;
    /**
     * 运算符
     */
    @JsonProperty("ruleOperator")
    private String ruleOperator;
    /**
     * 具体数值
     */
    @JsonProperty("ruleValue")
    private String ruleValue;
}
