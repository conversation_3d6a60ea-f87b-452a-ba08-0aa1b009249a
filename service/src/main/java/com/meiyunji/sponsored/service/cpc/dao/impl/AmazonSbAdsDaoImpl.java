package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.util.SelectBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdsDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAds;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdAdsPageParam;
import com.meiyunji.sponsored.service.cpc.vo.ListProductAsinParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by lm on 2021/7/29.
 */
@Repository
public class AmazonSbAdsDaoImpl extends BaseShardingDaoImpl<AmazonSbAds> implements IAmazonSbAdsDao {

    @Autowired
    private RedisService redisService;

    @Override
    public void batchAdd(int puid, List<AmazonSbAds> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;
        for (AmazonSbAds ad : list) {
            if (ad.getPuid() == null || ad.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(ad.getPuid());
            arg.add(ad.getShopId());
            arg.add(ad.getMarketplaceId());
            arg.add(ad.getProfileId());
            arg.add(ad.getCampaignId());
            arg.add(ad.getAdGroupId());
            arg.add(ad.getName());
            arg.add(ad.getCreateInAmzup());
            arg.add(ad.getCreationDate());
            arg.add(ad.getLastUpdatedDate());
            arg.add(ad.getServingStatus());
            arg.add(ad.getState());
            arg.add(ad.getLandingPage());
            arg.add(ad.getCreative());
            arg.add(ad.getCreativeType());
            arg.add(ad.getAdFormat());
            arg.add(ad.getIsOld());
            arg.add(ad.getAdId());
            arg.add(ad.getAsins());
            arg.add(ad.getQueryId());
            arg.add(ad.getBrandLogoUrl());
            arg.add(ad.getCustomImageUrl());
            argList.add(arg.toArray());
        }

        String sql = "insert into t_amazon_sb_ads (`puid`,`shop_id`,`marketplace_id`,`profile_id`,`campaign_id`,`ad_group_id`," +
                "`name`,`create_in_amzup`,`creation_date`,`last_updated_date`,`serving_status`,state," +
                " `landing_page`,`creative`,`creative_type`,`ad_format`, `is_old`,`ad_id`,`asins`, `query_id`, `brand_logo_url`, " +
                " `custom_image_url`, `create_time`,`update_time`) values (?,?,?, ?,?, ?,?, ?,?, ?,?,?,?,?,?,?,?,?,?,?,?,?, now(), now())";
        getJdbcTemplate(puid).batchUpdate(sql, argList);
    }

    @Override
    public void batchUpdate(int puid, List<AmazonSbAds> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;
        for (AmazonSbAds ad : list) {
            if (ad.getPuid() == null || ad.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(ad.getName());
            arg.add(ad.getState());
            arg.add(ad.getLastUpdatedDate());
            arg.add(ad.getServingStatus());
            arg.add(ad.getCreationDate());
            arg.add(ad.getLandingPage());
            arg.add(ad.getCreative());
            arg.add(ad.getCreativeType());
            arg.add(ad.getAdFormat());
            arg.add(ad.getIsOld());
            arg.add(ad.getAdId());
            arg.add(ad.getAsins());
            arg.add(ad.getBrandLogoUrl());
            arg.add(ad.getCustomImageUrl());
            arg.add(ad.getPuid());
            arg.add(ad.getShopId());
            arg.add(ad.getAdGroupId());
            arg.add(ad.getAdId());
            argList.add(arg.toArray());
        }

        String sql = "update `t_amazon_sb_ads` set `name` =?,`state`=?,`last_updated_date`=?,`serving_status`=?, " +
                " `creation_date` =?,`landing_page`=?,`creative`=?,`creative_type`=?,`ad_format`=?, `is_old`=?,`ad_id`=?,`asins`=?, "+
                "`brand_logo_url` = ?, `custom_image_url` = ? where `puid` =? and `shop_id`=? and `ad_group_id`=? and `ad_id` = ? ";
        getJdbcTemplate(puid).batchUpdate(sql, argList);
    }

    @Override
    public List<AmazonSbAds> listByGroupId(int puid, int shopId, List<String> groupIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .in("ad_group_id", groupIds.toArray())
                .build();
        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AmazonSbAds> getByCampaignId(int puid, Integer shopId, String campaignId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .build();
        return listByCondition(puid, builder);
    }

    private static final Map<String, List<String>> SB_TYPE_MAP = new HashMap<String, List<String>>() {{
        put("video", Lists.newArrayList("VIDEO", "BRAND_VIDEO"));
        put("productCollection", Lists.newArrayList("PRODUCT_COLLECTION"));
        put("storeSpotlight", Lists.newArrayList("STORE_SPOTLIGHT"));
    }};

    @Override
    public List<String> getCampaignIdBySbCreativeType(Integer puid, Integer shopId, String adType, Boolean bool) {
        List<String> creativeType = new ArrayList<>();
        if (bool == null || !bool || StringUtils.isBlank(adType)) {
            return creativeType;
        }
        List<String> types = StringUtil.splitStr(adType);
        for (String type : types) {
            if (SB_TYPE_MAP.containsKey(type)){
                creativeType.addAll(SB_TYPE_MAP.get(type));
            }
        }
        if (creativeType.size() == 0) {
            return creativeType;
        }
        StringBuilder sql = new StringBuilder();
        String sbSql = "select distinct campaign_id from t_amazon_sb_ads where ";
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId);
        conditionBuilder.in("creative_type", creativeType.toArray(new String[0]));
        Object[] values = conditionBuilder.build().getValues();
        sql.append(sbSql).append(conditionBuilder.build().getSql());
        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, values);
    }

    @Override
    public List<String> getCampaignIdBySbCreativeTypeMultiple(Integer puid, List<Integer> shopIdList, String adType, Boolean bool) {
        List<String> creativeType = new ArrayList<>();
        if (bool == null || !bool || StringUtils.isBlank(adType)) {
            return creativeType;
        }
        List<String> types = StringUtil.splitStr(adType);
        for (String type : types) {
            if (SB_TYPE_MAP.containsKey(type)){
                creativeType.addAll(SB_TYPE_MAP.get(type));
            }
        }
        if (creativeType.size() == 0) {
            return creativeType;
        }
        StringBuilder sql = new StringBuilder();
        String sbSql = "select distinct campaign_id from t_amazon_sb_ads where ";
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid);
        conditionBuilder.in("shop_id", shopIdList.toArray(new Integer[0]));
        conditionBuilder.in("creative_type", creativeType.toArray(new String[0]));
        Object[] values = conditionBuilder.build().getValues();
        sql.append(sbSql).append(conditionBuilder.build().getSql());
        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, values);
    }

    @Override
    public List<AmazonSbAds> getAdGroupByIds(Integer puid, Integer shopId, List<String> groupIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("ad_group_id", groupIds.toArray(new String[] {}))
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonSbAds> getByCampaignIds(int puid, Integer shopId, List<String> campaignIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("campaign_id", campaignIds.toArray())
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonSbAds> getAsinsByCampaignIds(int puid, List<Integer> shopId, List<String> campaignIds, String adGroupIds) {
        StringBuilder selectSql = new StringBuilder("select DISTINCT asins from t_amazon_sb_ads where puid = ?");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("shop_id", shopId, argsList));
        selectSql.append(" and asins is not null and asins !='' ");
        if (CollectionUtils.isNotEmpty(campaignIds)) {  //广告活动id
            selectSql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, argsList));
        }
        if (StringUtils.isNotBlank(adGroupIds)) {
            selectSql.append(SqlStringUtil.dealInList("ad_group_id", StringUtil.splitStr(adGroupIds), argsList));
        }
        return getJdbcTemplate(puid).query(selectSql.toString(), getRowMapper(), argsList.toArray());
    }

    @Override
    public String getSbFormatByGroupId(Integer puid, Integer shopId, String groupId) {
        String cacheKey = String.format(RedisConstant.SELLFOX_AD_SB_GROUP_FORMAT, puid, shopId, groupId);
        String value = redisService.getString(cacheKey);
        if (StringUtils.isBlank(value)) {
            ConditionBuilder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", shopId)
                    .equalTo("ad_group_id", groupId)
                    .limit(1)
                    .build();
            value = getByCondition(puid, "ad_format", String.class, builder);
            if (StringUtils.isNotBlank(value)) {
                redisService.set(cacheKey, value, 1, TimeUnit.DAYS);
            }
        }
        return value;
    }

    @Override
    public List<String> getAsinByGroup(int puid, Integer shopId, String campaignId, String adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("ad_group_id", adGroupId)
                .build();
        return listDistinctFieldByCondition(puid,"asins",builder,String.class);
    }

    @Override
    public List<AmazonSbAds> listByAdId(int puid, int shopId, List<String> adIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .in("ad_id", adIds.toArray())
                .build();
        return listByCondition(puid, conditionBuilder);
    }


    @Override
    public List<AmazonSbAds> listByQueryId(int puid, int shopId, List<String> queryIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .in("query_id", queryIds.toArray())
                .build();
        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public String getSbFormatByCampaignId(Integer puid, Integer shopId, String campaignId) {
        String cacheKey = String.format(RedisConstant.SELLFOX_AD_SB_CAMPAIGN_FORMAT, puid, shopId, campaignId);
        String value = redisService.getString(cacheKey);
        if (StringUtils.isBlank(value)) {
            ConditionBuilder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", shopId)
                    .equalTo("campaign_id", campaignId)
                    .limit(1)
                    .build();
            value = getByCondition(puid, "ad_format", String.class, builder);
            if (StringUtils.isNotBlank(value)) {
                redisService.set(cacheKey, value, 1, TimeUnit.DAYS);
            }
        }
        return value;
    }


    @Override
    public List<AmazonSbAds> getList(Integer puid, AdAdsPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId());


        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> list = StringUtil.splitStr(param.getCampaignId());
            builder.inStrList("campaign_id",list.toArray(new String[]{}));
        }
        //广告组合查询
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            builder.inStrList("campaign_id",param.getCampaignIdList().toArray(new String[]{}));
        }
        //广告组ID搜索
        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> list = StringUtil.splitStr(param.getGroupId());
            builder.inStrList("ad_group_id",list.toArray(new String[]{}));
        }


        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            builder.inStrList("state", statusList.toArray(new String[]{}));
        }

        if("name".equalsIgnoreCase(param.getSearchField())&& StringUtils.isNotBlank(param.getSearchValue())){
            builder.like("name",param.getSearchValue());
        }

        builder.orderByDesc("id");
        // 限制10万
        builder.limit(Constants.TOTALSIZELIMIT);

        return listByCondition(puid,builder.build());
    }

    @Override
    public Page getPageList(Integer puid, AdAdsPageParam param, Page page) {
        StringBuilder selectSql = new StringBuilder("select * from t_amazon_sb_ads ");
        StringBuilder countSql = new StringBuilder("select count(*)  FROM `t_amazon_sb_ads` ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=?  ");
        argsList.add(puid);

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            List<String> list = StringUtil.splitStr(param.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> list = StringUtil.splitStr(param.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }


        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, argsList));
        }

        if("name".equalsIgnoreCase(param.getSearchField())&& StringUtils.isNotBlank(param.getSearchValue())){
            whereSql.append(" and name like ?  ");
            argsList.add("%"+param.getSearchValue().trim()+"%");
        }

        selectSql.append(whereSql);
        countSql.append(whereSql);
        selectSql.append(" order by id desc ");

        Object[] args = argsList.toArray();
        return getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonSbAds.class);
    }


    @Override
    public List<String> getQueryIdByAds(Integer puid, AdAdsPageParam param){
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder(" select c.query_id from t_amazon_sb_ads c ");
        StringBuilder whereSql = new StringBuilder(" where c.puid = ? ");
        argsList.add(puid);

        if (param.getShopId() != null){
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }
        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            whereSql.append(" and c.campaign_id = ? ");
            argsList.add(param.getCampaignId());
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            whereSql.append(" and c.ad_group_id = ? ");
            argsList.add(param.getGroupId());
        }

        if("name".equalsIgnoreCase(param.getSearchField())&& StringUtils.isNotBlank(param.getSearchValue())){
            whereSql.append(" and c.name = ?  ");
            argsList.add(param.getSearchValue());
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }


        sql.append(whereSql);

        List<String> adQueryIds = getJdbcTemplate(puid).queryForList(sql.toString(),argsList.toArray(),String.class);
        return adQueryIds;
    }

    @Override
    public AmazonSbAds getAdsByQueryId(int puid, Integer shopId, String queryId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId)
                .equalTo("query_id", queryId)
                .build();
        return getByCondition(puid, builder);
    }

    @Override
    public AmazonSbAds getAdsByAdId(int puid, int shopId, String adId) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .equalTo("ad_id", adId)
                .build();
        return getByCondition(puid, conditionBuilder);
    }

    /**
     * @param puid
     * @param shopId
     * @param ids
     * @return
     */
    @Override
    public List<AmazonSbAds> getListByIds(Integer puid, Integer shopId, List<Long> ids) {

        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("id", ids.toArray());
        return listByCondition(puid, builder.build());
    }

    @Override
    public Map<String,String> getGroupIdFormatGroupByAdGroupId(Integer puid, Integer shopId, List<String> groupIds){
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder(" select ad_group_id,ad_format  from t_amazon_sb_ads  ");
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(shopId);

        if (CollectionUtils.isNotEmpty(groupIds)){
            whereSql.append(SqlStringUtil.dealInList("ad_group_id",groupIds,argsList));
        }
        sql.append(whereSql);
        sql.append(" group by puid,shop_id,ad_group_id ");
        List<AmazonSbAds> amazonSbAds = getJdbcTemplate(puid).query(sql.toString(),getMapper(),argsList.toArray());
        if(CollectionUtils.isEmpty(amazonSbAds)){
            return new HashMap<String,String>(1);
        }
        Map<String, String> collect = amazonSbAds.stream().filter(e -> e != null && StringUtils.isNotBlank(e.getAdGroupId()) && StringUtils.isNotBlank(e.getAdFormat())).collect(Collectors.toMap(AmazonSbAds::getAdGroupId, AmazonSbAds::getAdFormat, (v1, v2) -> v2));
        return collect;
    }

    @Override
    public List<AmazonSbAds> getAdsFieldsByAdGroups(Integer puid, Integer shopId, List<String> groupIds, List<String> fields) {
        SelectBuilder selectBuilder = new SelectBuilder();
        if (CollectionUtils.isEmpty(fields)) {
            selectBuilder.allColumn();
        } else {
            for (String field : fields) {
                selectBuilder.column(field);
            }
        }
        if (CollectionUtils.isEmpty(groupIds)) {
            return null;
        }
        selectBuilder.from("t_amazon_sb_ads")
                .where("puid", "=", puid)
                .andEq("shop_id", shopId)
                .andIn("ad_group_id", groupIds);
        return getJdbcTemplate(puid).query(selectBuilder.toSql(), selectBuilder.getQueryValues(), getMapper());
    }


    @Override
    public String getSbCreativeByQueryId(Integer puid, Integer shopId, String queryId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .equalTo("query_id", queryId)
                .build();
        return getByCondition(puid, "asins", String.class, builder);
    }


    @Override
    public List<AmazonSbAds> getAdsByQueryIds(int puid, Integer shopId, List<String> queryId) {

        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId)
                .in("query_id", queryId.toArray())
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonSbAds> getListByAdIds(int puid, Integer shopId, List<String> adIds) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("ad_id", adIds.toArray(new String[]{}));
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<String> getAdGroupIdBySbCreativeType(Integer puid, List<Integer> shopIdList, String deliveryType) {
        List<String> creativeType = new ArrayList<>();
        if (StringUtils.isBlank(deliveryType)) {
            return creativeType;
        }
        List<String> types = StringUtil.splitStr(deliveryType);
        for (String type : types) {
            if (SB_TYPE_MAP.containsKey(type)){
                creativeType.addAll(SB_TYPE_MAP.get(type));
            }
        }
        if (creativeType.isEmpty()) {
            return creativeType;
        }
        StringBuilder sql = new StringBuilder();
        String sbSql = "select distinct ad_group_id from t_amazon_sb_ads where ";
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid);
        conditionBuilder.in("shop_id", shopIdList.toArray(new Integer[0]));
        conditionBuilder.in("creative_type", creativeType.toArray(new String[0]));
        Object[] values = conditionBuilder.build().getValues();
        sql.append(sbSql).append(conditionBuilder.build().getSql());
        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, values);
    }

    @Override
    public List<String> getAsinsByCampaignIdsOrGroupIdsOrNames(int puid, List<Integer> shopId, List<String> campaignIds, List<String> adGroupIds, List<String> names) {
        StringBuilder selectSql = new StringBuilder("select DISTINCT asins from t_amazon_sb_ads where puid = ?");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("shop_id", shopId, argsList));
        selectSql.append(" and asins is not null and asins !='' ");
        if (CollectionUtils.isNotEmpty(campaignIds)) {  //广告活动id
            selectSql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, argsList));
        }
        if (CollectionUtils.isNotEmpty((adGroupIds))) {
            selectSql.append(SqlStringUtil.dealInList("ad_group_id", adGroupIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(names)) {
            selectSql.append(SqlStringUtil.dealInList("name", names, argsList));
        }
        return getJdbcTemplate(puid).queryForList(selectSql.toString(), String.class, argsList.toArray());
    }


    @Override
    public List<String> getListAsinByCampaignIdAndGroupIdAndEnabled(Integer puid, Integer shopId, String campaignId, String groupId) {
        StringBuilder sql = new StringBuilder(" SELECT DISTINCT(asins) FROM `t_amazon_sb_ads` WHERE puid = ? AND shop_id = ? AND campaign_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(campaignId);
        sql.append(" AND state in ('enabled','paused')  ");
        if (StringUtils.isNotBlank(groupId)) {
            sql.append(" and ad_group_id = ? ");
            argsList.add(groupId);
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, argsList.toArray());
    }

    @Override
    public List<String> listAsinByParam(Integer puid, Integer shopId, ListProductAsinParam listAsinParam) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder("select distinct asins from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid = ? ");
        sql.append(" and shop_id = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        if (listAsinParam.getCampaignId() != null) {
            sql.append(" and campaign_id = ? ");
            argsList.add(listAsinParam.getCampaignId());
        } else if (listAsinParam.getGroupId() != null) {
            sql.append(" and ad_group_id = ? ");
            argsList.add(listAsinParam.getGroupId());
        } else if (listAsinParam.getKeywordId() != null) {
            sql.append(" and ad_group_id = (select ad_group_id from t_amazon_ad_keyword_sb where puid = ? and shop_id = ? and keyword_id = ?) ");
            argsList.add(puid);
            argsList.add(shopId);
            argsList.add(listAsinParam.getKeywordId());
        } else if (listAsinParam.getTargetId() != null) {
            sql.append(" and ad_group_id = (select ad_group_id from t_amazon_ad_targeting_sb where puid = ? and shop_id = ? and target_id = ?) ");
            argsList.add(puid);
            argsList.add(shopId);
            argsList.add(listAsinParam.getTargetId());
        } else {
            return Collections.emptyList();
        }
        sql.append(" and (asins is not null or asins != '') and state in ('enabled','paused') ");

        List<String> asins = getJdbcTemplate(puid).queryForList(sql.toString(), String.class, argsList.toArray());
        // sb比较特殊，asins是由多个asin通过逗号分隔拼接而成的字段
        return asins.stream().map(asin -> Arrays.stream(StringUtils.split(asin, ",")).collect(Collectors.toList()))
                .flatMap(List::stream).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
    }
}
