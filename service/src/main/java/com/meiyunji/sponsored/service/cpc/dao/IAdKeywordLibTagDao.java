package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.po.AdKeywordLibTag;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSearchTag;

import java.util.List;

/**
 * AmazonAdCampaign
 * <AUTHOR>
 */
public interface IAdKeywordLibTagDao extends IBaseShardingDao<AdKeywordLibTag> {

    /**
     * 插入
     * @param adTag
     * @return
     */
    int insert(AdKeywordLibTag adTag);

    /**
     * 判断名称是否重复
     * @param puid
     * @param type
     * @param name
     * @return
     */
    boolean exist(Integer puid, Integer uid, String type, String name, String targetType);


    List<AdKeywordLibTag> getList(Integer puid, Integer uid, String type, String name);

    List<AdKeywordLibTag> getAllList(Integer puid, Integer uid, String type, String name);

    Integer getListCountByType(Integer puid, Integer uid, String type);

    /**
     * 获取最大排序值
     */
    Integer getMaxTagSort(Integer puid, Integer uid, String type);

    AdKeywordLibTag getByUidAndId(Integer puid, Integer uid, Long id);

    List<AdKeywordLibTag> listByUidAndId(Integer puid, Integer uid, List<Long> idList);
    List<AdKeywordLibTag> listByUidAndIdAndType(Integer puid, Integer uid, String type, List<Long> idList);

    List<AdKeywordLibTag> getByUidAndIdList(Integer puid, Integer uid, List<Long> idList);

    int getByAllUidAndIdList(Integer puid, Integer uid, List<Long> idList);

    int deleteByUidAndId(Integer puid, Integer uid, Long id);

    void batchInsert(Integer puid, List<AdKeywordLibTag> adKeywordLibTags);

    void batchUpdateSort(Integer puid, List<AdKeywordLibTag> list);
}