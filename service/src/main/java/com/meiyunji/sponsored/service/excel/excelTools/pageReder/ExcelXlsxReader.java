package com.meiyunji.sponsored.service.excel.excelTools.pageReder;

/* ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
==================================================================== */

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.service.excel.excelTools.ImportExcel;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.OpenXML4JException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.ss.usermodel.BuiltinFormats;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.model.SharedStringsTable;
import org.apache.poi.xssf.model.StylesTable;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.xml.sax.Attributes;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;
import org.xml.sax.XMLReader;
import org.xml.sax.helpers.DefaultHandler;
import org.xml.sax.helpers.XMLReaderFactory;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 《07版Excel》文件读取器
 * Created by DXM_0001 on 2018/2/10.
 * @see DefaultHandler
 */
public class ExcelXlsxReader extends DefaultHandler {

    private static final Logger log = LoggerFactory.getLogger(ExcelXlsxReader.class);

    private IExcelRowReader rowReader;
    List<List<List<String>>> dataList  = new LinkedList<>();
    List<List<String>> sheetList;
    List<String> rowsTemp;

    public void setRowReader(IExcelRowReader rowReader) {
        this.rowReader = rowReader;
    }

    /**
     * 共享字符串表  对应 Shared.xml
     */
    private SharedStringsTable sst;

    /**
     * 上一次的内容
     */
    private String lastContents;

    /**
     * 字符串标识
     */
    private boolean nextIsString;

    /**
     * 工作表索引
     */
    private int sheetIndex = 0;

    /**
     * 行集合
     */
    private List<String> rowlist = new LinkedList<String>();

    /**
     * 当前行
     */
    private int curRow = 0;

    /**
     * 当前列
     */
    private int curCol = 0;

    /**
     * T元素标识
     */
    private boolean isTElement;

    /**
     * 异常信息，如果为空则表示没有异常
     */
    private String exceptionMessage;

    /**
     * 单元格数据类型，默认为字符串类型
     */
    private CellDataType nextDataType = CellDataType.SSTINDEX;

    /**
     * 类型转换对象
     */
    private final DataFormatter formatter = new DataFormatter();
    /**
     * 下标
     */
    private short formatIndex;
    /**
     * 构造转换对象的字符串
     */
    private String formatString;

    /**
     * 标记一个没有值的空单元格
     * c标签代表一个单元格  r属性石列索引，s属性是样式，t=s表示这个单元格有值，
     * 里面的v标签即为值的id，id对应到sharedstring.xm里的id对应的值
     *  <c r="A2" s="1" t="s">
            <v>0</v>
        </c>
        <c r="B2" s="1"/>  没有t属性   这个单元格没有值
     */
    private boolean  cellNull;

    /**
     * 标记当前行的首个单元格没有值
     */
    private  boolean afertCellNull;
    /**
     * SAX解析器
     */
    private static final String SAXParser = "org.apache.xerces.parsers.SAXParser";

    private boolean thisRowAllColNull;

    // 定义前一个元素和当前元素的位置，用来计算其中空的单元格数量，如A6和A8等
    private String preRef = null, ref = null;

    // 定义该文档一行最大的单元格数，用来补全一行最后可能缺失的单元格
    private String maxRef = null;

    //当前行下标
    private int rowRefIndex = 0;

    /**
     * 单元格
     */
    private StylesTable stylesTable;

    /**
     * 开始行（获取结果集的时候需要传的参数）  默认最小值为 0
     */
    public int startRow = 0;
    /**
     * 结束行（获取结果集的时候需要传的参数） 默认值为5000
     */
    public int endRow = 5000;
    /**
     * 指定唯一的表头行索引（获取结果集的时候需要传的参数） 默认最小值为 1，代表第一行是表头
     */
    private int onlyHaderRowIndex = 1;

    /**
     *  指定最多读取多少列   默认50
     */
    private int maxReaderColumns= 50;
    /**
     * 指定最多读取多少个工作表   默认10
     */
    private int maxReaderSheets= 10;

    /**
     * 遍历工作簿中所有的电子表格
     * @param filename
     * @throws IOException
     * @throws OpenXML4JException
     * @throws SAXException
     * @throws Exception
     */
    public void process(String filename) throws IOException, OpenXML4JException, SAXException {
        InputStream ips = new FileInputStream(filename);
        process(ips);
    }

    /**
     * 遍历工作簿中所有的电子表格
     * @param ips
     * @throws IOException
     * @throws OpenXML4JException
     * @throws SAXException
     * @throws Exception
     */
    public void process(InputStream ips) throws IOException, OpenXML4JException, SAXException {
        OPCPackage pkg = OPCPackage.open(ips);
        XSSFReader xssfReader = new XSSFReader(pkg);
        stylesTable = xssfReader.getStylesTable();
        SharedStringsTable sst = xssfReader.getSharedStringsTable();
        XMLReader parser = this.fetchSheetParser(sst);
        Iterator<InputStream> sheets = xssfReader.getSheetsData();
        while (sheets.hasNext()) {
            curRow = 0;
            sheetIndex++;
            sheetList = new LinkedList<>();
            dataList.add(sheetIndex-1,sheetList);
            log.info(String.format("Sheet Create:[%s]",sheetIndex));
            InputStream sheet = sheets.next();
            InputSource sheetSource = new InputSource(sheet);
            parser.parse(sheetSource);
            sheet.close();
        }
    }

    /**
     * 加载  org.apache.xerces.parsers.SAXParser
     * @param sst
     * @return
     * @throws SAXException
     */
    public XMLReader fetchSheetParser(SharedStringsTable sst) throws SAXException {
        XMLReader parser = XMLReaderFactory.createXMLReader(SAXParser);
        log.info(String.format("XMLReader To Load Parser:[%s]",SAXParser));
        this.sst = sst;
        parser.setContentHandler(this);
        return parser;
    }

    @Override
    public void startDocument() throws SAXException{
        log.debug("readExcel-startDocument...");
    }
    @Override
    public void endDocument() throws SAXException{
        log.debug("readExcel-endDocument...");
    }

    @Override
    public void startElement(String uri, String localName, String name, Attributes attributes) throws SAXException {
        if (this.isAccess()){
            if("row".equals(name)){
                rowRefIndex = Integer.valueOf(attributes.getValue("r"));
                log.debug("rowRefIndex["+rowRefIndex+"] start");
            }

            // c => 单元格
            if ("c".equals(name)) {
                // 前一个单元格的位置
                if (preRef == null) {
                    preRef = attributes.getValue("r");
                    afertCellNull = true;
                } else {
                    preRef = ref;
                    afertCellNull = false;
                }
                // 当前单元格的位置
                ref = attributes.getValue("r");
                // 设定单元格类型
                this.setNextDataType(attributes);
                // Figure out if the value is an index in the SST
                String cellType = attributes.getValue("t");//t标签存在代表有值，没有代表该单元格没有值
                if ("s".equals(cellType)) {
                    nextIsString = true;
                    cellNull = false;
                } else {
                    nextIsString = false;
                    cellNull = true;
                }
            }

            // 当元素为t时
            if ("t".equals(name)) {
                isTElement = true;
            } else {
                isTElement = false;
            }

            // 置空
            lastContents = "";

            //
            thisRowAllColNull = false;
        }
    }

    @Override
    public void endElement(String uri, String localName, String name) throws SAXException {
        if (this.isAccess() && sheetIndex < maxReaderSheets) {
            if ("row".equals(name)) {
                log.debug("rowRefIndex[" + rowRefIndex + "] end");
            }
            //补全前面缺失的单元格
            if (preRef != null && ref != null && ref.equals(preRef) && afertCellNull) {
                int len = countNullCell(ref, ("A" + rowRefIndex));
                if (len == 0) {
                    len = 1;
                }else{
                    if(len == -1 && "".equals(lastContents) ){//首个单元格为空
                        len = 1;
                    }else{
                        len = len + 1;
                    }
                }
                for (int i = 0; i < len; i++) {
                    rowlist.add(curCol, null);
                    curCol++;
                    cellNull = false;
                }
                afertCellNull = false;
            }

            // 根据SST的索引值的到单元格的真正要存储的字符串
            // 这时characters()方法可能会被调用多次
            if (nextIsString && StringUtils.isNotEmpty(lastContents) && StringUtils.isNumeric(lastContents)) {
                try{
                    int idx = Integer.parseInt(lastContents);
                    lastContents = new XSSFRichTextString(sst.getEntryAt(idx)).toString();
                }catch (Exception eii){
                    lastContents = lastContents.toString();
                }
            }

            // t元素也包含字符串
            if (isTElement) {
                // 将单元格内容加入rowlist中，在这之前先去掉字符串前后的空白符
                String value = lastContents.trim();
                rowlist.add(curCol, value);
                curCol++;
                isTElement = false;
                cellNull = false;
            } else if ("v".equals(name)) {
                // v => 单元格的值，如果单元格是字符串则v标签的值为该字符串在SST中的索引
                String value = this.getDataValue(lastContents.trim(), "");
                //补全单元格之间的空单元格
                if (!ref.equals(preRef)) {
                    int len = countNullCell(ref, preRef);
                    for (int i = 0; i < len; i++) {
                        rowlist.add(curCol, null);
                        curCol++;
                    }
                }
                rowlist.add(curCol, value);
                curCol++;
                cellNull = false;
            } else if ("c".equals(name) && cellNull == true) {
                //补全单元格之间的空单元格
                if (!ref.equals(preRef)) {
                    int len = countNullCell(ref, preRef);
                    if (len == 0) {
                        rowlist.add(curCol, null);
                        curCol++;
                        cellNull = false;
                    } else {
                        for (int i = 0; i < len; i++) {
                            rowlist.add(curCol, null);
                            curCol++;
                            cellNull = false;
                        }
                    }
                }

            } else {
                // 如果标签名称为 row ，这说明已到行尾，调用 optRows() 方法
                if (name.equals("row")) {
                    // 默认第一行为表头，以该行单元格数目为最大数目
                    if (curRow == onlyHaderRowIndex -1) {
                        maxRef = ref;
                        if(colNameToIndex(maxRef)>maxReaderColumns){
                            maxRef = colIndexToName(maxReaderColumns);
                            log.info("第"+sheetIndex + "个sheet，列数超限，使用系统默认最大列数");
                        }
                    }
                    // 补全一行尾部可能缺失的单元格
                    if (maxRef != null) {
                        if (ref != null && preRef != null) {
                            int len = countNullCell(maxRef, ref);
                            for (int i = 0; i <= len; i++) {
                                rowlist.add(curCol, null);
                                curCol++;
                            }
                        }
                    }
                    if(rowlist != null && rowlist.size()>0 ){
                        thisRowAllColNull = rowlist.stream().filter(item ->item != null).count()>0;
                        if(thisRowAllColNull){
                            sheetList = dataList.get(sheetIndex-1);
                            rowsTemp = ImportExcel.deepCopy(rowlist);
                            if(sheetList.size() < curRow){
                                sheetList.add(Lists.newArrayList());
                            }
                            sheetList.add(curRow,rowsTemp);
    //                        rowReader.getRows(sheetIndex, curRow, rowlist);/**  对外提供的行级操作接口 */
                        }
                    }
                    rowlist.clear();
                    curRow++;
                    curCol = 0;
                    preRef = null;
                    ref = null;
                }
            }
        }else{
            throw new SAXException("找到所需数据，停止解析");
        }
    }

    @Override
    public void characters(char[] ch, int start, int length) throws SAXException {
        // 得到单元格内容的值
        lastContents += new String(ch, start, length);
    }

    /**
     * 单元格中的数据可能的数据类型
     */
    enum CellDataType {
        BOOL, ERROR, FORMULA, INLINESTR, SSTINDEX, NUMBER, DATE, NULL
    }

    /**
     * 处理数据类型
     * @param attributes
     */
    private void setNextDataType(Attributes attributes) {
        nextDataType = CellDataType.NUMBER;
        formatIndex = -1;
        formatString = null;
        String cellType = attributes.getValue("t");
        String cellStyleStr = attributes.getValue("s");
        String columData = attributes.getValue("r");

        if ("b".equals(cellType)) {
            nextDataType = CellDataType.BOOL;
        } else if ("e".equals(cellType)) {
            nextDataType = CellDataType.ERROR;
        } else if ("inlineStr".equals(cellType)) {
            nextDataType = CellDataType.INLINESTR;
        } else if ("s".equals(cellType)) {
            nextDataType = CellDataType.SSTINDEX;
        } else if ("str".equals(cellType)) {
            nextDataType = CellDataType.FORMULA;
        }

        if (cellStyleStr != null) {
            int styleIndex = Integer.parseInt(cellStyleStr);
            XSSFCellStyle style = stylesTable.getStyleAt(styleIndex);
            formatIndex = style.getDataFormat();
            formatString = style.getDataFormatString();

            if ("m/d/yy" == formatString) {
                nextDataType = CellDataType.DATE;
                formatString = "yyyy-MM-dd hh:mm:ss.SSS";
            }
//            if ("\\¥\\ #,##0.00_);\\(\\¥\\ #,##0.00\\)".equals(formatString)
//                    || "\"￥\"#,##0.00_);[Red]\\(\"￥\"#,##0.00\\)".equals(formatString)
//                    ||"\"￥\"#,##0.00;\"￥\"\\-#,##0.00".equals(formatString)
//                    ||"_-[$$-409]* #,##0.00_ ;_-[$$-409]* \\-#,##0.00\\ ;_-[$$-409]* \"-\"??_ ;_-@_ ".equals(formatString)
//                    ||"\\$#,##0.00;\\-\\$#,##0.00".equals(formatString)
//                    ||"\\$#,##0.00_);[Red]\\(\\$#,##0.00\\)".equals(formatString)
//                    ) {  /** 特别针对DXM系统里EXCEl中rmb与美元字符串的处理 */
            if (formatString != null &&  (formatString.contains("#,##0.00") || formatString.contains("#,##0.0"))) {  /** 特别针对DXM系统里EXCEl中rmb与美元字符串的处理 */
                nextDataType = CellDataType.NUMBER;
                formatString = "#,##0.00_);\\(#,##0.00\\)";
            }
            if (formatString == null) {
                nextDataType = CellDataType.NULL;
                formatString = BuiltinFormats.getBuiltinFormat(formatIndex);
            }
        }
    }

    /**
     * 对解析出来的数据进行类型处理
     * @param value 单元格的值（这时候是一串数字）
     * @param thisStr 一个空字符串
     * @return
     */
    private String getDataValue(String value, String thisStr) {
        switch (nextDataType) {//顺序不能随便交换
            case BOOL:
                char first = value.charAt(0);
                thisStr = first == '0' ? "FALSE" : "TRUE";
                break;
            case ERROR:
                thisStr = "\"ERROR:" + value.toString() + '"';
                break;
            case FORMULA:
                thisStr = '"' + value.toString() + '"';
                break;
            case INLINESTR:
                thisStr = new XSSFRichTextString(value.toString()).toString();
                break;
            case SSTINDEX:
                thisStr = value.toString();
                break;
            case NUMBER:
                if (formatString != null) {
                    Matcher m = Pattern.compile(".*\\d+.*").matcher(value);
                    if (m.matches()) {
                        thisStr = formatter.formatRawCellContents(Double.parseDouble(value), formatIndex, formatString).trim();
                    }else{
                        thisStr = value;
                    }
                } else {
                    thisStr = value;
                }
                thisStr = thisStr.replace("_", "").trim();
                break;
            case DATE:
                thisStr = formatter.formatRawCellContents(Double.parseDouble(value), formatIndex, formatString);
                // 对日期字符串作特殊处理
                thisStr = thisStr.replace(" ", "T");
                break;
            default:
                thisStr = " ";
                break;
        }
        return thisStr;
    }

    /**
     * 计算两个单元格之间的单元格数目(同一行)
     * @param ref
     * @param preRef
     * @return
     */
    public int countNullCell(String ref, String preRef) {
        // excel2007最大行数是1048576，最大列数是16384，最后一列列名是XFD
        String xfd = ref.replaceAll("\\d+", "");
        String xfd_1 = preRef.replaceAll("\\d+", "");

        xfd = fillChar(xfd, 3, '@', true);
        xfd_1 = fillChar(xfd_1, 3, '@', true);

        char[] letter = xfd.toCharArray();
        char[] letter_1 = xfd_1.toCharArray();
        int res = (letter[0] - letter_1[0]) * 26 * 26 + (letter[1] - letter_1[1]) * 26 + (letter[2] - letter_1[2]);
        return res - 1;
    }

    /**
     * 字符串的填充
     * @param str
     * @param len
     * @param let
     * @param isPre
     * @return
     */
    String fillChar(String str, int len, char let, boolean isPre) {
        int len_1 = str.length();
        if (len_1 < len) {
            if (isPre) {
                for (int i = 0; i < (len - len_1); i++) {
                    str = let + str;
                }
            } else {
                for (int i = 0; i < (len - len_1); i++) {
                    str = str + let;
                }
            }
        }
        return str;
    }

    /**
     * @return the exceptionMessage
     */
    public String getExceptionMessage() {
        return exceptionMessage;
    }

    /**
     * 列转数字下标
     * @param col
     * @return
     */
    private static int colNameToIndex(String col) { //  "AAA"
        if (col == null)
            return -1;
        char[] chrs = col.toUpperCase().toCharArray(); // 转为大写字母组成的 char数组
        int length = chrs.length;
        int ret = -1;
        for (int i = 0; i < length; i++) {
            ret += (chrs[i] - 'A' + 1) * Math.pow(26, length - i - 1); // 当做26进制来算 AAA=111 26^2+26^1+26^0
        }
        return ret;// 702; 从0开始的下标
    }
    /**
    * 数字下标转列
    * @param index
    * @return
    */
    private static String colIndexToName(int index) {
        int shang = 0;
        int yu = 0;
        List<Integer> list = new ArrayList<Integer>();   //10进制转26进制 倒序
        while (true) {
            shang = index / 26;
            yu = index % 26;
            index = shang;
            list.add(yu);
            if (shang == 0)
                break;
        }
        StringBuilder sb = new StringBuilder();
        for (int j = list.size() - 1; j >= 0; j--) {
            sb.append((char) (list.get(j) + 'A' - (j > 1 ? 1 : j)));     //倒序拼接  序号转字符 非末位 序号减去 1
        }
        return sb.toString();
    }

    private String getColName(String ref){
        ref = ref.replaceAll("\\d+","");
        return ref;
    }

    /**
     * get data by Row index Between startRow and endRow
     * @return
     */
    private boolean isAccess(){
        if(curRow>=startRow && curRow<endRow ){
            return true;
        }
        return false;
    }


    public int getStartRow() {
        return startRow;
    }

    public void setStartRow(int startRow) {
        this.startRow = startRow;
    }

    public int getEndRow() {
        return endRow;
    }

    public void setEndRow(int endRow) {
        this.endRow = endRow;
    }

    public int getOnlyHaderRowIndex() {
        return onlyHaderRowIndex;
    }

    public void setOnlyHaderRowIndex(int onlyHaderRowIndex) {
        this.onlyHaderRowIndex = onlyHaderRowIndex;
    }

    public List<List<List<String>>> getDataList() {
        return dataList;
    }

    public int getMaxReaderColumns() {
        return maxReaderColumns;
    }

    public void setMaxReaderColumns(int maxReaderColumns) {
        this.maxReaderColumns = maxReaderColumns;
    }

    public int getMaxReaderSheets() {
        return maxReaderSheets;
    }

    public void setMaxReaderSheets(int maxReaderSheets) {
        this.maxReaderSheets = maxReaderSheets;
    }
}
