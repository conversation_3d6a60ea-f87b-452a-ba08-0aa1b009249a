package com.meiyunji.sponsored.service.doris.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * amazon SB ads表(OdsAmazonSbAds)实体类
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:21
 */
@Data
@DbTable("ods_t_amazon_sb_ads")
public class OdsAmazonSbAds  implements Serializable {
	/**
     * 商户uid
     */    
	@DbColumn(value = "puid")
    private Integer puid;

	/**
     * 店铺ID
     */    
	@DbColumn(value = "shop_id")
    private Integer shopId;

	/**
     * adId
     */    
	@DbColumn(value = "ad_id")
    private String adId;

	/**
     * 广告组id
     */    
	@DbColumn(value = "ad_group_id")
    private String adGroupId;

	/**
     * 配置ID
     */    
	@DbColumn(value = "profile_id")
    private String profileId;

	/**
     * 站点
     */    
	@DbColumn(value = "marketplace_id")
    private String marketplaceId;

	/**
     * v3 版本sb活动没有adId并且不能编辑，所以此字段用于冗余查询使用，v3 填充groupId，v4填充adId
     */    
	@DbColumn(value = "query_id")
    private String queryId;

	/**
     * ad名称
     */    
	@DbColumn(value = "name")
    private String name;

	/**
     * 活动id
     */    
	@DbColumn(value = "campaign_id")
    private String campaignId;

	/**
     * ad创意类型PRODUCT_COLLECTION, AUTHOR_COLLECTION, STORE_SPOTLIGHT, VIDEO, BRAND_VIDEO
     */    
	@DbColumn(value = "creative_type")
    private String creativeType;

	/**
     * product_collection,video
     */    
	@DbColumn(value = "ad_format")
    private String adFormat;

	/**
     * ad状态（enabled，paused，archived）
     */    
	@DbColumn(value = "state")
    private String state;

	/**
     * asins 
     */    
	@DbColumn(value = "asins")
    private String asins;

	/**
     * 活动的具体状态,例:CAMPAIGN_ARCHIVED
     */    
	@DbColumn(value = "serving_status")
    private String servingStatus;

	/**
     * 创意
     */    
	@DbColumn(value = "creative")
    private String creative;

	/**
     * 创意品牌logo
     */    
	@DbColumn(value = "brand_logo_url")
    private String brandLogoUrl;

	/**
     * 创意自定义图片
     */    
	@DbColumn(value = "custom_image_url")
    private String customImageUrl;

	/**
     * 登陆页
     */    
	@DbColumn(value = "landing_page")
    private String landingPage;

	/**
     * 1在amzup创建，0从amazon同步
     */    
	@DbColumn(value = "create_in_amzup")
    private Integer createInAmzup;

	/**
     * v3sb ads 只读模式，不可修改，是否是老板ads，0：不是，1：是
     */    
	@DbColumn(value = "is_old")
    private Integer isOld;

	/**
     * 创建人id
     */    
	@DbColumn(value = "create_id")
    private Integer createId;

	/**
     * 更新人id
     */    
	@DbColumn(value = "update_id")
    private Integer updateId;

	/**
     * 平台创建时间
     */    
	@DbColumn(value = "creation_date")
    private LocalDateTime creationDate;

	/**
     * 平台上次更新时间
     */    
	@DbColumn(value = "last_updated_date")
    private LocalDateTime lastUpdatedDate;

	/**
     * 创建时间
     */    
	@DbColumn(value = "create_time")
    private Date createTime;

	/**
     * 更新的时间
     */    
	@DbColumn(value = "update_time")
    private Date updateTime;

	private List<String> asinList;

}

