package com.meiyunji.sponsored.service.cpc.manager;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.cpc.dto.GetFbaReviewInfoReq;
import com.meiyunji.sponsored.service.cpc.dto.GetFbaReviewInfoResp;
import com.meiyunji.sponsored.service.dataWarehouse.service.StatsNewClient;
import com.meiyunji.sponsored.service.post.request.GetAsinsRequest;
import com.meiyunji.sponsored.service.post.request.GetSuggestAsinRequest;
import com.meiyunji.sponsored.service.post.response.GetAsinsResponse;
import com.meiyunji.sponsored.service.post.response.GetSuggestAsinResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.stream.Collectors;

/**
 * 数据域manager
 *
 * @Author: hejh
 * @Date: 2024/9/2 16:10
 */
@Slf4j
@Component
public class DataDomainManager {
    @Autowired
    private StatsNewClient statsNewClient;
    private static final int MAX_COUNT = 5000;

    /**
     * 调用失败返回Collections.emptyList();
     *
     * @param puid
     * @param mskuShopIds
     * @return
     */
    public List<GetFbaReviewInfoResp.FbaInfo> getFbaReviewInfo(int puid, List<GetFbaReviewInfoReq.MskuShopId> mskuShopIds) {
        if (CollectionUtils.isEmpty(mskuShopIds)) {
            return Collections.emptyList();
        }
        //过滤不符合条件的参数
        List<GetFbaReviewInfoReq.MskuShopId> allMskuShopIds = mskuShopIds.stream().filter(key -> key != null && StringUtils.isNotBlank(key.getMsku()) && key.getShopId() != 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allMskuShopIds)) {
            return Collections.emptyList();
        }

        //多线程查询
        Vector<GetFbaReviewInfoResp.FbaInfo> fbaInfos = new Vector<>();
        List<List<GetFbaReviewInfoReq.MskuShopId>> partition = Lists.partition(allMskuShopIds, MAX_COUNT);
        List<CompletableFuture<Void>> futureList = new ArrayList<>(partition.size());
        for (List<GetFbaReviewInfoReq.MskuShopId> list : partition) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                GetFbaReviewInfoReq getFbaReviewInfoReq = new GetFbaReviewInfoReq();
                getFbaReviewInfoReq.setPuid(puid);
                getFbaReviewInfoReq.setMskuShopIds(list);
                //接口联调人：数据组，杨聪
                GetFbaReviewInfoResp resp = statsNewClient.postJson("/api/stat/outside/v1/msku/analysis/getFbaReviewInfo", JSON.toJSONString(getFbaReviewInfoReq), GetFbaReviewInfoResp.class);
                if (resp != null && resp.getCode() == 0 && CollectionUtils.isNotEmpty(resp.getData())) {
                    fbaInfos.addAll(resp.getData());
                }
            }, ThreadPoolUtil.getQueryDataDomainExecutor()).exceptionally((e) -> {
                log.warn("error for /api/stat/outside/v1/msku/analysis/getFbaReviewInfo", e);
                return null;
            });
            futureList.add(future);
        }
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        try {
            allOf.join();
        } catch (CompletionException e) {
            log.error("Error occurred while get CompletableFuture FbaInfo", e);
        }

        return fbaInfos;
    }

    /**
     * 调用数据组接口，查询建议ASIN
     * @param puid
     * @param shopIds
     * @param marketplaceIds
     * @param req
     * @return
     */
    public List<GetSuggestAsinResponse.SuggestAsinVo> getSuggestAsin(Integer puid, List<Integer> shopIds, List<String> marketplaceIds, GetSuggestAsinRequest req) {
        List<GetSuggestAsinResponse.SuggestAsinVo> list = new ArrayList<>();
        GetAsinsResponse response = new GetAsinsResponse();
        if (puid == null || CollectionUtils.isEmpty(shopIds)) {
            return list;
        }
        GetAsinsRequest request = new GetAsinsRequest();
        request.setPuid(puid);
        request.setShopIdList(shopIds);
        request.setMarketplaceIdList(marketplaceIds);
        if (StringUtils.isNotBlank(req.getOrderType())) {
            request.setOrderType(req.getOrderType());
        }
        if (StringUtils.isNotBlank(req.getOrderFiled())) {
            request.setOrderField(req.getOrderFiled());
        }
        if (Objects.nonNull(req.getOnlineStatus())) {
            if ("ACTIVE".equalsIgnoreCase(req.getOnlineStatus())) {
                request.setIsSaleable(true);
            } else if ("InActive".equalsIgnoreCase(req.getOnlineStatus())) {
                request.setIsSaleable(false);
            } else {
                request.setIsSaleable(null);
            }
        }
        log.info("suggestionAsin request:{}", JSON.toJSONString(request));
        try {
            response = statsNewClient.postJson("/api/stat/outside/v1/product/getAdSugesstionProduct", JSON.toJSONString(request), GetAsinsResponse.class);
        } catch (Exception e) {
            log.error("调用数据组接口查询建议ASIN 异常：puid: {}, shopId:{}", puid, shopIds);
        }
        if (response != null && response.getCode() == 0 && CollectionUtils.isNotEmpty(response.getData())) {
            list.addAll(response.getData());
        }
        return list;
    }


/*
    public static void main(String[] args) {
        String s = "{\"code\":0,\"msg\":\"success\",\"data\":[{\"msku\":\"GKR00017-Grey-FBA\",\"asin\":null,\"shopId\":134899,\"title\":\"Jabogrii Kitchen Floor Mat Set of 2, Cushioned Anti Fatigue Kitchen Mats Comfort Standing Mat PVC Waterproof Non Skid Kitchen Rugs with Runner Kitchen Carpet for Sink Laundry 17.3\\\"x48\\\"+17.3\\\"x28\\\"\",\"devIds\":null,\"mainDevId\":null,\"marketplaceId\":null,\"mskuList\":null,\"localInfoList\":null,\"currency\":\"USD\",\"imageUrl\":null,\"standardPrice\":21.990000,\"listingPricing\":0.000000,\"price\":21.990000000,\"available\":1,\"reservedTransfer\":0,\"reservedProcessing\":0,\"reservedCustomerorders\":0,\"inboundWorking\":0,\"inboundShipped\":0,\"inboundReceiving\":0,\"unfulfillable\":0,\"research\":0,\"totalInventory\":1,\"rating\":\"4.5\",\"ratingCount\":21}]}";
        GetFbaReviewInfoResp resp =  JSON.parseObject(s, GetFbaReviewInfoResp.class);
        System.out.println(resp);

    }*/
}
