package com.meiyunji.sponsored.service.newDashboard.service.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardNaturalOrderResponseVo;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopOrderDto;
import com.meiyunji.sponsored.service.doris.bo.AmazonAdCampaignAllReportBo;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.newDashboard.dto.CampaignOrGroupOrPortfolioDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTopDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardNaturalQueryTypeEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByRateEnum;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdNaturalOrderService;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardAdNaturalOrderReqVo;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardCampaignOrGroupOrPortfolioReqVo;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2024/4/9 15:52
 * @describe:
 */
@Service
@Slf4j
public class DashboardAdNaturalOrderServiceImpl implements IDashboardAdNaturalOrderService {

    @Autowired
    private IOdsAmazonAdCampaignAllReportDao odsAmazonAdCampaignAllReportDao;


    @Resource
    private CpcShopDataService cpcShopDataService;

    @Resource
    private IVcShopAuthDao vcShopAuthDao;


    private static final String shopKey = "shop";
    private static final String adKey = "ad";


    @Override
    public DashboardNaturalOrderResponseVo queryNaturalOrderCharts(DashboardAdNaturalOrderReqVo req) {
        return getNaturalOrderList(req);
    }


    private DashboardNaturalOrderResponseVo getNaturalOrderList(DashboardAdNaturalOrderReqVo req) {

        List<Integer> shopId = req.getShopIdList();
        List<VcShopAuth> listByIdList = vcShopAuthDao.getListByIdList(shopId);
        if (CollectionUtils.isNotEmpty(listByIdList)) {
            return null;
        }

        /**
         * 直接按天聚合再到内存计算，一年最多365天，无需担心数据量
         * 1.查出广告订单量，按天聚合；
         * 2.店铺订单量，按天聚合；
         * 3.根据日周月进行数据计算；
         *
         */

        DashboardNaturalQueryTypeEnum dashboardNaturalQueryTypeEnum = DashboardNaturalQueryTypeEnum.queryTypeMap.get(req.getQueryType());

        Integer puid = req.getPuid();
        List<String> marketplaceIdList = req.getMarketplaceIdList();
        List<Integer> shopIdList = req.getShopIdList();
        String currency = req.getCurrency();
        List<String> portfolioIds = req.getPortfolioIds();


        List<Object> argsList = Lists.newArrayList();

        List<String> siteToday = null;
        if (Boolean.TRUE.equals(req.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(marketplaceIdList);
        }
        //当期，环比最大最小日期
        LocalDate start = LocalDate.parse(req.getStartDate(), DateTimeFormatter.ISO_DATE);
        LocalDate momStart = LocalDate.parse(req.getMomStartDate(), DateTimeFormatter.ISO_DATE);
        LocalDate end = LocalDate.parse(req.getEndDate(), DateTimeFormatter.ISO_DATE);
        LocalDate momEnd = LocalDate.parse(req.getMomEndDate(), DateTimeFormatter.ISO_DATE);
        LocalDate min = Lists.newArrayList(start, momStart).stream().min(LocalDate::compareTo).get();
        LocalDate max = Lists.newArrayList(end, momEnd).stream().max(LocalDate::compareTo).get();

        //查询店铺广告订单数据

        List<AmazonAdCampaignAllReportBo> adOrderNumGroupByCountDayList = odsAmazonAdCampaignAllReportDao.getAdOrderNumGroupByCountDayList(puid, shopIdList, marketplaceIdList, req.getCampaignIds(), min.format(DateTimeFormatter.ISO_DATE), max.format(DateTimeFormatter.ISO_DATE), siteToday, req.getSiteToday(), portfolioIds);

        //查询店铺订单数据
        List<ShopOrderDto> shopOrderDtos = cpcShopDataService.shopOrderGroupByCountDay(puid, shopIdList, marketplaceIdList, min.format(DateTimeFormatter.ISO_DATE), max.format(DateTimeFormatter.ISO_DATE));

        Map<LocalDate, AmazonAdCampaignAllReportBo> adOrderNumMap = adOrderNumGroupByCountDayList.stream().collect(Collectors.toMap(AmazonAdCampaignAllReportBo::getCountDay, Function.identity()));

        Map<LocalDate, ShopOrderDto> shopOrderMap = shopOrderDtos.stream().collect(Collectors.toMap(ShopOrderDto::getNowDate, Function.identity()));


        //同比环比数据切割

        LocalDate index = LocalDate.ofEpochDay(min.toEpochDay());
        DashboardNaturalOrderResponseVo.Builder builder = DashboardNaturalOrderResponseVo.newBuilder();
        List<DashboardNaturalOrderResponseVo.Indicator> indicators = new ArrayList<>();
        List<DashboardNaturalOrderResponseVo.Record.RecordItem> dayAdOrderRateList = new ArrayList<>();
        List<DashboardNaturalOrderResponseVo.Record.RecordItem> dayNatureOrderNumList = new ArrayList<>();
        List<DashboardNaturalOrderResponseVo.Record.RecordItem> dayAdOrderNumList = new ArrayList<>();
        Map<String, Map<String, Integer>> weekMap = new LinkedHashMap<>();
        Map<String, Map<String, Integer>> monthMap = new LinkedHashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        //汇总
        int sumShopOrder = 0;
        int sumAdOrder = 0;
        int sumMomShopOrder = 0;
        int sumMomAdOrder = 0;
        while (index.compareTo(max) < 1) {

            AmazonAdCampaignAllReportBo amazonAdCampaignAllReportBo = adOrderNumMap.get(index);
            ShopOrderDto shopOrderDto = shopOrderMap.get(index);
            int shopOrder = 0;
            int adOrder = 0;
            if (amazonAdCampaignAllReportBo != null && amazonAdCampaignAllReportBo.getOrderNum() != null) {
                adOrder = amazonAdCampaignAllReportBo.getOrderNum();
            }
            if (shopOrderDto != null && shopOrderDto.getOrderNum() != null) {
                shopOrder = shopOrderDto.getOrderNum();
            }
            //当期数据
            if (LocalDateTimeUtil.isBetween(start, end, index)) {

                sumAdOrder = adOrder + sumAdOrder;
                sumShopOrder = shopOrder + sumShopOrder;

                //日 数据处理
                String day = index.format(DateTimeFormatter.ISO_LOCAL_DATE);
                if (amazonAdCampaignAllReportBo == null && shopOrderDto == null) {
                    dayAdOrderRateList.add(DashboardNaturalOrderResponseVo.Record.RecordItem.newBuilder().setDate(day).setValue("0").build());
                    dayAdOrderNumList.add(DashboardNaturalOrderResponseVo.Record.RecordItem.newBuilder().setDate(day).setValue("0").build());
                    dayNatureOrderNumList.add(DashboardNaturalOrderResponseVo.Record.RecordItem.newBuilder().setDate(day).setValue("0").build());
                } else {
                    dayAdOrderRateList.add(DashboardNaturalOrderResponseVo.Record.RecordItem.newBuilder().setDate(day).setValue(CalculateUtil.calPercentStr4IntNoPercentScale4(adOrder, shopOrder)).build());
                    dayAdOrderNumList.add(DashboardNaturalOrderResponseVo.Record.RecordItem.newBuilder().setDate(day).setValue(String.valueOf(adOrder)).build());
                    dayNatureOrderNumList.add(DashboardNaturalOrderResponseVo.Record.RecordItem.newBuilder().setDate(day).setValue(String.valueOf(shopOrder - adOrder)).build());
                }
                //周 数据处理
                LocalDate monday = index.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
                LocalDate sunday = index.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
                String weekKey = monday.format(DateTimeFormatter.ISO_LOCAL_DATE) + "~" + sunday.format(DateTimeFormatter.ISO_LOCAL_DATE);
                Map<String, Integer> w = weekMap.get(weekKey);
                if (w == null) {
                    HashMap<String, Integer> data = new HashMap<>();
                    data.put(shopKey, shopOrderDto == null || shopOrderDto.getOrderNum() == null ? 0 :  shopOrderDto.getOrderNum());
                    data.put(adKey, amazonAdCampaignAllReportBo == null || amazonAdCampaignAllReportBo.getOrderNum() == null ? 0 :  amazonAdCampaignAllReportBo.getOrderNum());
                    weekMap.put(weekKey, data);
                } else {
                    if (shopOrderDto != null) {
                        w.put(shopKey, MathUtil.add(w.getOrDefault(shopKey, 0), shopOrderDto.getOrderNum()));
                    }
                    if (amazonAdCampaignAllReportBo != null) {
                        w.put(adKey, MathUtil.add(w.getOrDefault(adKey, 0), amazonAdCampaignAllReportBo.getOrderNum()));
                    }
                }
                //月数据处理
                String mothKey = index.format(formatter);
                Map<String, Integer> m = monthMap.get(mothKey);
                if (m == null) {
                    HashMap<String, Integer> data = new HashMap<>();
                    data.put(shopKey, shopOrderDto == null || shopOrderDto.getOrderNum() == null ? 0 :  shopOrderDto.getOrderNum());
                    data.put(adKey, amazonAdCampaignAllReportBo == null || amazonAdCampaignAllReportBo.getOrderNum() == null ? 0 :  amazonAdCampaignAllReportBo.getOrderNum());
                    monthMap.put(mothKey, data);
                } else {
                    if (shopOrderDto != null) {
                        m.put(shopKey, MathUtil.add(m.getOrDefault(shopKey, 0), shopOrderDto.getOrderNum()));
                    }
                    if (amazonAdCampaignAllReportBo != null) {
                        m.put(adKey, MathUtil.add(m.getOrDefault(adKey, 0), amazonAdCampaignAllReportBo.getOrderNum()));
                    }
                }

            }
            //环比数据

            if (LocalDateTimeUtil.isBetween(momStart, momEnd, index)) {
                sumMomAdOrder = adOrder + sumMomAdOrder;
                sumMomShopOrder = shopOrder + sumMomShopOrder;
            }
            index = index.plusDays(1);
        }

        //开始组装数据
        //广告订单占比
        DashboardNaturalOrderResponseVo.Indicator adOrderNum = DashboardNaturalOrderResponseVo.Indicator.newBuilder()
                .setName("广告订单量")
                .setIndicator("adOrderNum")
                .setValue(String.valueOf(sumAdOrder))
                .setMomValue(String.valueOf(sumMomAdOrder))
                .setMomRate(CalculateUtil.calPercentStr4IntScale4((sumAdOrder - sumMomAdOrder), sumMomAdOrder))
                .build();
        indicators.add(adOrderNum);

        //自然订单
        int nature = sumShopOrder - sumAdOrder;
        int momNature = sumMomShopOrder - sumMomAdOrder;
        DashboardNaturalOrderResponseVo.Indicator natureOrderNum = DashboardNaturalOrderResponseVo.Indicator.newBuilder()
                .setName("自然订单量")
                .setIndicator("natureOrderNum")
                .setValue(String.valueOf(nature))
                .setMomValue(String.valueOf(momNature))
                .setMomRate(CalculateUtil.calPercentStr4IntScale4((nature - momNature), momNature))
                .build();
        indicators.add(natureOrderNum);

        //自然订单
        BigDecimal orderRate = CalculateUtil.calPercent4IntScale4(sumAdOrder, sumShopOrder);
        BigDecimal momOrderRate = CalculateUtil.calPercent4IntScale4(sumMomAdOrder, sumMomShopOrder);
        DashboardNaturalOrderResponseVo.Indicator adOrderRate = DashboardNaturalOrderResponseVo.Indicator.newBuilder()
                .setName("广告订单占比")
                .setIndicator("adOrderRate")
                .setValue(CalculateUtil.formatPercentNoPercent(orderRate))
                .setMomValue(CalculateUtil.formatPercentNoPercent(momOrderRate.setScale(4, RoundingMode.HALF_UP).toPlainString()))
                .setMomRate(CalculateUtil.calPercentStr4DecimalScale4(MathUtil.subtract(orderRate, momOrderRate), momOrderRate))
                .build();
        indicators.add(adOrderRate);
        builder.addAllIndicatorList(indicators);


        //day
        builder.addDay(builderDay(dayAdOrderNumList, "广告订单量", "adOrderNum"));
        builder.addDay(builderDay(dayNatureOrderNumList, "自然订单量", "natureOrderNum"));
        builder.addDay(builderDay(dayAdOrderRateList, "广告订单占比", "adOrderRate"));

        //week
        weekMap.size();
        Iterator<Map.Entry<String, Map<String, Integer>>> iterator = weekMap.entrySet().iterator();
        int size = weekMap.size();
        Map<String, Map<String, Integer>> week = new LinkedHashMap<>();
        int i = 1;
        while (iterator.hasNext()) {
            Map.Entry<String, Map<String, Integer>> entry = iterator.next();
            if (i == 1 && start.compareTo(end) != 0) {
                String[] split = entry.getKey().split("~");
                String firstKey = start.format(DateTimeFormatter.ISO_LOCAL_DATE) + "~" + split[1];
                week.put(firstKey, entry.getValue());
            } else if (i == size) {
                String[] split = entry.getKey().split("~");
                String lastKey = split[0] + "~" + end.format(DateTimeFormatter.ISO_LOCAL_DATE);
                week.put(lastKey, entry.getValue());
            } else if (start.compareTo(end) == 0) {
                String key = start.format(DateTimeFormatter.ISO_LOCAL_DATE) + "~" + end.format(DateTimeFormatter.ISO_LOCAL_DATE);
                week.put(key, entry.getValue());
            } else {
                week.put(entry.getKey(), entry.getValue());
            }
            i++;
        }

        builder.addAllWeek(builderRecord(week));

        //month
        builder.addAllMonth(builderRecord(monthMap));


        return builder.build();

    }


    private DashboardNaturalOrderResponseVo.Record builderDay(List<DashboardNaturalOrderResponseVo.Record.RecordItem> data, String description, String name) {
        //广告订单占比
        DashboardNaturalOrderResponseVo.Record record = DashboardNaturalOrderResponseVo.Record.newBuilder()
                .setDescription(description)
                .setName(name)
                .setTotal(data.size())
                .addAllRecords(data)
                .build();
        return record;
    }

    private List<DashboardNaturalOrderResponseVo.Record> builderRecord(Map<String, Map<String, Integer>> dataMap) {

        List<DashboardNaturalOrderResponseVo.Record.RecordItem> adOrderRateList = new ArrayList<>();
        List<DashboardNaturalOrderResponseVo.Record.RecordItem> natureOrderNumList = new ArrayList<>();
        List<DashboardNaturalOrderResponseVo.Record.RecordItem> adOrderNumList = new ArrayList<>();
        for (Map.Entry<String, Map<String, Integer>> entry : dataMap.entrySet()) {

            String key = entry.getKey();
            Integer ad = entry.getValue().get(adKey);
            Integer shop = entry.getValue().get(shopKey);
            //广告订单
            DashboardNaturalOrderResponseVo.Record.RecordItem adOrderNum = DashboardNaturalOrderResponseVo.Record.RecordItem.newBuilder()
                    .setDate(key)
                    .setValue(String.valueOf(ad))
                    .build();

            adOrderNumList.add(adOrderNum);

            //自然订单
            DashboardNaturalOrderResponseVo.Record.RecordItem natureNum = DashboardNaturalOrderResponseVo.Record.RecordItem.newBuilder()
                    .setDate(key)
                    .setValue(String.valueOf(shop - ad))
                    .build();

            natureOrderNumList.add(natureNum);

            //订单占比
            DashboardNaturalOrderResponseVo.Record.RecordItem orderRate = DashboardNaturalOrderResponseVo.Record.RecordItem.newBuilder()
                    .setDate(key)
                    .setValue(CalculateUtil.calPercentStr4IntNoPercentScale4(ad, shop))
                    .build();

            adOrderRateList.add(orderRate);
        }

        List<DashboardNaturalOrderResponseVo.Record> list = new ArrayList<>();
        list.add(builderDay(adOrderNumList, "广告订单量", "adOrderNum"));
        list.add(builderDay(natureOrderNumList, "自然订单量", "natureOrderNum"));
        list.add(builderDay(adOrderRateList, "广告订单占比", "adOrderRate"));
        return list;
    }

}
