package com.meiyunji.sponsored.service.monitor.dao;

import com.meiyunji.sponsored.common.springjdbc.IAdBaseDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdActiveMonitor;

public interface IAmazonAdActiveMonitorDao extends IAdBaseDao<AmazonAdActiveMonitor> {

    /**
     * 活跃的puid插入
     */
    int insertAll(AmazonAdActiveMonitor adActiveMonitor);

    /**
     * 更新puid访问次数
     */
    int updateTimeAndActiveCount(Long id, Long activeCount);

    /**
     * 获取puid
     * @param puid
     * @return
     */
    AmazonAdActiveMonitor findByPuid(Integer puid);
}
