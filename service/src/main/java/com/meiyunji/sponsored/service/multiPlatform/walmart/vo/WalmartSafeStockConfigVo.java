package com.meiyunji.sponsored.service.multiPlatform.walmart.vo;

public class WalmartSafeStockConfigVo {
    //启用安全库存 0不启用，1启用
    private Integer isEnable;

    //店铺Id
    private Long shopId;

    //配置模式  0未设置，1固定值，2销量权重模式，3备货天数
    private Integer optionMode;

    //安全库存数
    private Integer safeStockNum;

    //备货天数
    private Integer warningDay;

    //警戒值
    private Integer minSafeStockNum;

    //最小备货天数
    private Integer minWarningDay;

    //采购天数
    private Integer purchasingDay;

    //中转仓到海外仓天数
    private Integer transitDay;

    //权重json字符串
    private String weightSet;

    //状态参数：表示把配置应用到 所有商品 Or 未设置的商品
    private String applyConfigToAllOrPart;

    public Integer getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(Integer isEnable) {
        this.isEnable = isEnable;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Integer getOptionMode() {
        return optionMode;
    }

    public void setOptionMode(Integer optionMode) {
        this.optionMode = optionMode;
    }

    public Integer getSafeStockNum() {
        return safeStockNum;
    }

    public void setSafeStockNum(Integer safeStockNum) {
        this.safeStockNum = safeStockNum;
    }

    public Integer getWarningDay() {
        return warningDay;
    }

    public void setWarningDay(Integer warningDay) {
        this.warningDay = warningDay;
    }

    public Integer getMinSafeStockNum() {
        return minSafeStockNum;
    }

    public void setMinSafeStockNum(Integer minSafeStockNum) {
        this.minSafeStockNum = minSafeStockNum;
    }

    public Integer getMinWarningDay() {
        return minWarningDay;
    }

    public void setMinWarningDay(Integer minWarningDay) {
        this.minWarningDay = minWarningDay;
    }

    public Integer getPurchasingDay() {
        return purchasingDay;
    }

    public void setPurchasingDay(Integer purchasingDay) {
        this.purchasingDay = purchasingDay;
    }

    public Integer getTransitDay() {
        return transitDay;
    }

    public void setTransitDay(Integer transitDay) {
        this.transitDay = transitDay;
    }

    public String getWeightSet() {
        return weightSet;
    }

    public void setWeightSet(String weightSet) {
        this.weightSet = weightSet;
    }

    public String getApplyConfigToAllOrPart() {
        return applyConfigToAllOrPart;
    }

    public void setApplyConfigToAllOrPart(String applyConfigToAllOrPart) {
        this.applyConfigToAllOrPart = applyConfigToAllOrPart;
    }
}
