package com.meiyunji.sponsored.service.multiPlatform.walmart.qo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2025-02-25  13:43
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class KeywordPageAggregateQo {
    //用户id
    private Integer puid;
    //店铺id
    private List<Integer> shopIdList;
    //广告类型
    private String adType;
    //定向策略
    private String targetingType;
    //广告活动id
    private List<String> campaignIdList;
    //广告组id
    private List<String> groupIdList;
    //过滤关键词运行状态
    private List<String> state;
    //过滤关键词服务状态
    private List<String> reviewStatus;
    //报告开始时间
    private String startDate;
    //报告结束时间
    private String endDate;
    //时间归因
    private Integer attributeDayType;
    //exact精确，blur模糊
    private String searchType;
    //搜索关键词
    private String searchValue;
    //匹配类型
    private List<String> matchType;

}
