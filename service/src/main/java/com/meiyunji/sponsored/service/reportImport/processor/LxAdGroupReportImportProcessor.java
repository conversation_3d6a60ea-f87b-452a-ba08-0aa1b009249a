package com.meiyunji.sponsored.service.reportImport.processor;

import com.amazonaws.util.json.Jackson;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.reportImport.constanst.LxReportConstant;
import com.meiyunji.sponsored.service.reportImport.enums.LxExcelAdType;
import com.meiyunji.sponsored.service.reportImport.enums.ReportImportErrType;
import com.meiyunji.sponsored.service.reportImport.message.AdReportImportMessage;
import com.meiyunji.sponsored.service.reportImport.model.LxAdGroupReport;
import com.meiyunji.sponsored.service.reportImport.vo.DuplicationCampaignVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * lx广告组报告导入处理器
 *
 * <AUTHOR>
 * @date 2023/05/26
 */
@Service
@Slf4j
public class LxAdGroupReportImportProcessor extends AbstractLxReportImportProcessor<LxAdGroupReport> {

    private final IAmazonAdGroupReportDao amazonAdGroupReportDao;
    private final IAmazonAdSbGroupReportDao amazonAdSbGroupReportDao;
    private final IAmazonAdSdGroupReportDao amazonAdSdGroupReportDao;


    protected LxAdGroupReportImportProcessor(IAmazonAdCampaignAllDao amazonAdCampaignAllDao, IAmazonAdGroupDao amazonAdGroupDao, IAmazonSdAdGroupDao amazonSdAdGroupDao, IAmazonSbAdGroupDao amazonSbAdGroupDao, IAmazonAdGroupReportDao amazonAdGroupReportDao, IAmazonAdSbGroupReportDao amazonAdSbGroupReportDao, IAmazonAdSdGroupReportDao amazonAdSdGroupReportDao) {
        super(amazonAdCampaignAllDao, amazonAdGroupDao, amazonSdAdGroupDao, amazonSbAdGroupDao);
        this.amazonAdGroupReportDao = amazonAdGroupReportDao;
        this.amazonAdSbGroupReportDao = amazonAdSbGroupReportDao;
        this.amazonAdSdGroupReportDao = amazonAdSdGroupReportDao;
    }

    /**
     * 导入报告
     * @param importMessage           导入报告任务消息
     * @param reports                 解析报告数据列表
     * @param shopAuthMap
     */
    @Override
    public void importReport(AdReportImportMessage importMessage, List<LxAdGroupReport> reports, Map<String, ShopAuth> shopAuthMap) {
        Integer pUid = importMessage.getPuid();
        //Long taskId = importMessage.getScheduleId();

        List<Integer> shopIds = shopAuthMap.values().stream().map(ShopAuth::getId).collect(Collectors.toList());
        //按活动名称查询所有广告活动
        List<AmazonAdCampaignAll> allCampaigns = listByTypeAndCampaignNames(pUid, reports, shopIds);
        //记录重复活动
        Map<String, List<AmazonAdCampaignAll>> mapList = allCampaigns.stream()
                .collect(Collectors.groupingBy(k -> getCampaignKeyFormat(
                k.getShopId(), k.getType().toLowerCase(), k.getName())));
        for (Map.Entry<String, List<AmazonAdCampaignAll>> entry : mapList.entrySet()) {
            if (entry.getValue().size() > 1) {
                Map<Integer, ShopAuth> shopIdMap = shopAuthMap.values().stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity(), (a, b) -> a));
                Map<String, String> map = new HashMap<>();
                map.put("campaignName", entry.getValue().get(0).getName());
                map.put("country", Marketplace.fromId(shopIdMap.get(entry.getValue().get(0).getShopId()).getMarketplaceId()).getCountryCode());
                map.put("shopName", shopIdMap.get(entry.getValue().get(0).getShopId()).getName());
                throw new BizServiceException(ReportImportErrType.DUPLICATION_CAMPAIGN.name(), Jackson.toJsonString(map));
            }
        }

        Map<String, AmazonAdCampaignAll> campaignNameMap = getCampaignNameMap(allCampaigns);
        Map<String, AmazonAdCampaignAll> campaignIdMap = getCampaignIdMap(allCampaigns);

        Map<String, List<LxAdGroupReport>> lxAdGroupMap = reports.stream().collect(
                Collectors.groupingBy(LxAdGroupReport::getAdType));

        //填充campaignId,用于更精确查询ad-group
        lxAdGroupMap.forEach((k, v) -> {
            fillCampaignIdForReport(campaignNameMap, v);
        });

        List<AmazonAdGroupReport> spReports = new ArrayList<>();
        List<AmazonAdSbGroupReport> sbReports = new ArrayList<>();
        List<AmazonAdSdGroupReport> sdReports = new ArrayList<>();

        lxAdGroupMap.forEach((k, v) -> {
            if (CampaignTypeEnum.sp.name().equalsIgnoreCase(k)) {
                Map<String, AmazonAdGroup> spAdGroupMap = getSpAdGroupMap(pUid, reports);
                for (LxAdGroupReport report : v) {
                    if (report.getCampaignId() == null) {
                        throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "SP广告活动不存在 (活动名称: " + report.getCampaignName()+")");
                    }
                    String mapKey = getGroupKeyFormat( report.getShopId(), report.getCampaignId(), report.getAdGroupName());
                    if (!spAdGroupMap.containsKey(mapKey)) {
                        throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "SP广告组名称不存在 (广告组名称: " + report.getAdGroupName()+")");
                    }
                    AmazonAdGroup amazonAdGroup = spAdGroupMap.get(mapKey);

                    AmazonAdGroupReport amazonAdGroupReport = buildSpAdGroupReport(report, amazonAdGroup);
                    spReports.add(amazonAdGroupReport);
                }

            } else if (CampaignTypeEnum.sb.name().equalsIgnoreCase(k)) {
                Map<String, AmazonSbAdGroup> sbAdGroupMap = getSbAdGroupMap(pUid, reports);
                for (LxAdGroupReport report : v) {
                    if (report.getCampaignId() == null) {
                        throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "SB广告活动不存在 (活动名称: " + report.getCampaignName()+")");
                    }
                    String mapKey = getGroupKeyFormat( report.getShopId(), report.getCampaignId(), report.getAdGroupName());
                    if (!sbAdGroupMap.containsKey(mapKey)) {
                        throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "SB广告组名称不存在 (广告组名称: " + report.getAdGroupName()+")");
                    }
                    AmazonSbAdGroup amazonSbAdGroup = sbAdGroupMap.get(mapKey);

                    AmazonAdSbGroupReport sbGroupReport = buildSbAdGroupReport(report, amazonSbAdGroup);

                    sbReports.add(sbGroupReport);
                }
            } else if (CampaignTypeEnum.sd.name().equalsIgnoreCase(k)) {
                Map<String, AmazonSdAdGroup> sdAdGroupMap = getSdAdGroupMap(pUid, reports);
                for (LxAdGroupReport report : v) {
                    if (report.getCampaignId() == null) {
                        throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "SD广告活动不存在 (活动名称: " + report.getCampaignName()+")");
                    }
                    String mapKey = getGroupKeyFormat( report.getShopId(), report.getCampaignId(), report.getAdGroupName());
                    if (!sdAdGroupMap.containsKey(mapKey)) {
                        throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "SD广告组名称不存在 (广告组名称: " + report.getAdGroupName()+")");
                    }
                    AmazonSdAdGroup amazonSdAdGroup = sdAdGroupMap.get(mapKey);


                    String mapIdKey = getCampaignKeyFormat(report.getShopId(), report.getAdType().toLowerCase(), report.getCampaignId());
                    AmazonAdCampaignAll campaignAll = campaignIdMap.get(mapIdKey);

                    AmazonAdSdGroupReport sdGroupReport = buildSdGroupReport(report, amazonSdAdGroup, campaignAll);
                    sdReports.add(sdGroupReport);
                }
            }
        });

        //持久数据到数据库
        if (CollectionUtils.isNotEmpty(spReports)) {
            amazonAdGroupReportDao.insertList(pUid, spReports);
        }

        if (CollectionUtils.isNotEmpty(sbReports)) {
            amazonAdSbGroupReportDao.insertOrUpdateList(pUid, sbReports);
        }

        if (CollectionUtils.isNotEmpty(sdReports)) {
            amazonAdSdGroupReportDao.insertOrUpdateList(pUid, sdReports);
        }

    }

    /**
     * 构建sd组报告
     *
     * @param report          报告
     * @param amazonSdAdGroup 亚马逊sd广告组
     * @param campaignAll     活动所有
     * @return {@link AmazonAdSdGroupReport}
     */
    private AmazonAdSdGroupReport buildSdGroupReport(LxAdGroupReport report, AmazonSdAdGroup amazonSdAdGroup, AmazonAdCampaignAll campaignAll) {
        AmazonAdSdGroupReport sdGroupReport = new AmazonAdSdGroupReport();
        sdGroupReport.setPuid(report.getPuid());
        sdGroupReport.setShopId(report.getShopId());
        sdGroupReport.setMarketplaceId(report.getMarketplaceId());
        sdGroupReport.setCountDate(report.getSfCountDate());
        sdGroupReport.setTacticType(amazonSdAdGroup.getTactic());
        sdGroupReport.setCurrency(Marketplace.fromId(report.getMarketplaceId()).getCurrencyCode().name());
        sdGroupReport.setCampaignName(report.getCampaignName());
        sdGroupReport.setCampaignId(report.getCampaignId());
        sdGroupReport.setAdGroupName(report.getAdGroupName());
        sdGroupReport.setAdGroupId(amazonSdAdGroup.getAdGroupId());
        sdGroupReport.setClicks(isDxmNumeric(report.getClicks()) ? Integer.valueOf(report.getClicks()) : 0);
        sdGroupReport.setCost(isDxmNumeric(report.getAdCost()) ? new BigDecimal(report.getAdCost()) : BigDecimal.ZERO);

        sdGroupReport.setConversions14d(isDxmNumeric(report.getAdOrder()) ? Integer.valueOf(report.getAdOrder()) : 0);
        sdGroupReport.setConversions14dSameSKU(isDxmNumeric(report.getAdSelfOrder()) ? Integer.valueOf(report.getAdSelfOrder()) : 0);
        sdGroupReport.setSales14d(isDxmNumeric(report.getAdSales()) ? new BigDecimal(report.getAdSales()) : BigDecimal.ZERO);
        sdGroupReport.setSales14dSameSKU(isDxmNumeric(report.getAdSelfSales()) ? new BigDecimal(report.getAdSelfSales()) : BigDecimal.ZERO);

        sdGroupReport.setUnitsOrdered14d(isDxmNumeric(report.getAdSaleNum()) ? Integer.valueOf(report.getAdSaleNum()) : 0);
        sdGroupReport.setImpressions(isDxmNumeric(report.getImpressions()) ? Integer.valueOf(report.getImpressions()) : 0);
        sdGroupReport.setCostType(campaignAll.getCostType());

        sdGroupReport.setViewImpressions(isDxmNumeric(report.getViewImpressions()) ? Integer.parseInt(report.getViewImpressions()) : 0);
        return sdGroupReport;
    }

    /**
     * 构建sb广告组报告
     *
     * @param report          报告
     * @param amazonSbAdGroup 亚马逊sb广告组
     * @return {@link AmazonAdSbGroupReport}
     */
    private AmazonAdSbGroupReport buildSbAdGroupReport(LxAdGroupReport report, AmazonSbAdGroup amazonSbAdGroup) {
        AmazonAdSbGroupReport sbGroupReport = new AmazonAdSbGroupReport();
        sbGroupReport.setPuid(report.getPuid());
        sbGroupReport.setShopId(report.getShopId());
        sbGroupReport.setMarketplaceId(report.getMarketplaceId());
        sbGroupReport.setCountDate(report.getSfCountDate());
        sbGroupReport.setCurrency(Marketplace.fromId(report.getMarketplaceId()).getCurrencyCode().name());
        sbGroupReport.setAdFormat(Optional.ofNullable(amazonSbAdGroup.getAdFormat()).orElse("manual"));
        sbGroupReport.setCampaignName(report.getCampaignName());
        sbGroupReport.setCampaignId(report.getCampaignId());
        sbGroupReport.setAdGroupName(report.getAdGroupName());
        sbGroupReport.setAdGroupId(amazonSbAdGroup.getAdGroupId());
        sbGroupReport.setImpressions(isDxmNumeric(report.getImpressions()) ? Integer.valueOf(report.getImpressions()) : 0);
        sbGroupReport.setClicks(isDxmNumeric(report.getClicks()) ? Integer.valueOf(report.getClicks()) : 0);
        sbGroupReport.setCost(isDxmNumeric(report.getAdCost()) ? new BigDecimal(report.getAdCost()) : BigDecimal.ZERO);
        sbGroupReport.setSales14d(isDxmNumeric(report.getAdSales()) ? new BigDecimal(report.getAdSales()) : BigDecimal.ZERO);
        sbGroupReport.setSales14dSameSKU(isDxmNumeric(report.getAdSelfSales()) ? new BigDecimal(report.getAdSelfSales()) : BigDecimal.ZERO);
        sbGroupReport.setConversions14d(isDxmNumeric(report.getAdOrder()) ? Integer.valueOf(report.getAdOrder()) : 0);
        sbGroupReport.setConversions14dSameSKU(isDxmNumeric(report.getAdSelfOrder()) ? Integer.valueOf(report.getAdSelfOrder()) : 0);
        sbGroupReport.setUnitsSold14d(isDxmNumeric(report.getAdSaleNum()) ? Integer.valueOf(report.getAdSaleNum()) : 0);

        sbGroupReport.setDpv14d(isDxmNumeric(report.getDpv()) ? Integer.valueOf(report.getDpv()) : 0);
        try {
            sbGroupReport.setVideo5SecondViewRate(new BigDecimal(report.getVideo5SecondViewRate().replaceAll("%", "")).doubleValue());
        } catch (Exception exception) {
            log.error("buildSbAdGroupReport execute setVideo5SecondViewRate errorMsg: {}", exception.getMessage());
        }
        sbGroupReport.setVideo5SecondViews(isDxmNumeric(report.getVideo5SecondViews()) ? Integer.valueOf(report.getVideo5SecondViews()) : 0);
        sbGroupReport.setVideoFirstQuartileViews(isDxmNumeric(report.getVideoFirstQuartileViews()) ? Integer.valueOf(report.getVideoFirstQuartileViews()) : 0);
        sbGroupReport.setVideoMidpointViews(isDxmNumeric(report.getVideoMidpointViews()) ? Integer.valueOf(report.getVideoMidpointViews()) : 0);
        sbGroupReport.setVideoThirdQuartileViews(isDxmNumeric(report.getVideoThirdQuartileViews()) ? Integer.valueOf(report.getVideoThirdQuartileViews()) : 0);
        sbGroupReport.setVideoUnmutes(isDxmNumeric(report.getVideoUnmutes()) ? Integer.valueOf(report.getVideoUnmutes()) : 0);
        sbGroupReport.setViewableImpressions(isDxmNumeric(report.getViewImpressions()) ? Integer.valueOf(report.getViewImpressions()) : null);
        sbGroupReport.setVideoCompleteViews(isDxmNumeric(report.getVideoCompleteViews()) ? Integer.valueOf(report.getVideoCompleteViews()) : 0);


        try {
            sbGroupReport.setVctr(new BigDecimal(report.getVctr().replaceAll("%", "")).doubleValue());
        } catch (Exception exception) {
            log.error("buildSbAdGroupReport execute setVctr errorMsg: {}", exception.getMessage());
        }

        try {
            sbGroupReport.setVtr(new BigDecimal(report.getVtr().replaceAll("%", "")).doubleValue());
        } catch (Exception exception) {
            log.error("buildSbAdGroupReport execute setVtr errorMsg: {}", exception.getMessage());
        }

        return sbGroupReport;
    }

    /**
     * 构建sp广告组报告
     *
     * @param report        报告
     * @param amazonAdGroup 亚马逊广告组
     * @return {@link AmazonAdGroupReport}
     */
    private AmazonAdGroupReport buildSpAdGroupReport(LxAdGroupReport report, AmazonAdGroup amazonAdGroup) {
        AmazonAdGroupReport amazonAdGroupReport = new AmazonAdGroupReport();
        amazonAdGroupReport.setPuid(report.getPuid());
        amazonAdGroupReport.setShopId(report.getShopId());
        amazonAdGroupReport.setMarketplaceId(report.getMarketplaceId());
        amazonAdGroupReport.setCountDate(report.getSfCountDate());
        amazonAdGroupReport.setCampaignId(report.getCampaignId());
        amazonAdGroupReport.setAdGroupId(amazonAdGroup.getAdGroupId());
        amazonAdGroupReport.setAdGroupName(report.getAdGroupName());
        amazonAdGroupReport.setCampaignName(report.getCampaignName());

        amazonAdGroupReport.setImpressions(isDxmNumeric(report.getImpressions()) ? Integer.valueOf(report.getImpressions()) : 0);
        amazonAdGroupReport.setClicks(isDxmNumeric(report.getClicks()) ? Integer.valueOf(report.getClicks()) : 0);
        amazonAdGroupReport.setCost(isDxmNumeric(report.getAdCost()) ? new BigDecimal(report.getAdCost()) : BigDecimal.ZERO);
        amazonAdGroupReport.setTotalSales(isDxmNumeric(report.getAdSales()) ? new BigDecimal(report.getAdSales()) : BigDecimal.ZERO);
        amazonAdGroupReport.setAdSales(isDxmNumeric(report.getAdSelfSales()) ? new BigDecimal(report.getAdSelfSales()) : BigDecimal.ZERO);
        amazonAdGroupReport.setSaleNum(isDxmNumeric(report.getAdOrder()) ? Integer.valueOf(report.getAdOrder()) : 0);
        amazonAdGroupReport.setAdSaleNum(isDxmNumeric(report.getAdSelfOrder()) ? Integer.valueOf(report.getAdSelfOrder()) : 0);
        amazonAdGroupReport.setOrderNum(isDxmNumeric(report.getAdSaleNum()) ? Integer.valueOf(report.getAdSaleNum()) : 0);
        amazonAdGroupReport.setAdOrderNum(isDxmNumeric(report.getAdSelfSaleNum()) ? Integer.valueOf(report.getAdSelfSaleNum()) : 0);
        return amazonAdGroupReport;
    }


    /**
     * 填补活动id
     *
     * @param campaignNameMap 活动名称映射
     * @param list            列表
     */
    private void fillCampaignIdForReport(Map<String, AmazonAdCampaignAll> campaignNameMap, List<LxAdGroupReport> list) {
        for (LxAdGroupReport report : list) {
            String adType = report.getAdType().toLowerCase();
            if (!StringUtils.equals(LxExcelAdType.SB.getName(), adType) && LxExcelAdType.parse(adType)) {
                adType = LxExcelAdType.SB.getName();
            }
            String mapKey = getCampaignKeyFormat(report.getShopId(), adType, report.getCampaignName());
            if (!campaignNameMap.containsKey(mapKey)) {
                throw new BizServiceException("LxAdGroupReportImportProcessor execute [行号 "+ (report.getRowNumber() + 1) +"] " + "广告活动不存在 (活动名称: " + report.getCampaignName()+")");
            }
            AmazonAdCampaignAll campaignAll = campaignNameMap.get(mapKey);
            report.setCampaignId(campaignAll.getCampaignId());
        }
    }




}
