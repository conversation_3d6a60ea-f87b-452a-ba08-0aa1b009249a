package com.meiyunji.sponsored.service.syncAd.task.init.sb;

import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbCampaignApiService;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncAdTypeEnum;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncConstant;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncTaskTypeEnum;
import com.meiyunji.sponsored.service.syncAd.po.AmazonAdShopDataInitTask;
import com.meiyunji.sponsored.service.syncAd.task.init.AdShopDataSyncTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;


/**
 * @author: sunlin<PERSON>
 * @email: <EMAIL>
 * @date: 2024-01-31  17:14
 */

@Component(ShopDataSyncConstant.sb + ShopDataSyncConstant.campaign)
public class SbCampaignTask extends AdShopDataSyncTask {


    @Autowired
    private CpcSbCampaignApiService cpcSbCampaignApiService;

    @PostConstruct
    public void init() {
        setAdType(ShopDataSyncAdTypeEnum.sb);
        setTaskType(ShopDataSyncTaskTypeEnum.CAMPAIGN);
    }

    @Override
    protected final void doSync(ShopAuth shop, AmazonAdProfile profile, AmazonAdShopDataInitTask task) {
        //同步广告活动，查询所有状态
        cpcSbCampaignApiService.syncCampaignsV4(shop, null, null, false, true);
    }
}
