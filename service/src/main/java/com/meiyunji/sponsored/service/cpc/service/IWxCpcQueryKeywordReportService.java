package com.meiyunji.sponsored.service.cpc.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.rpc.adCommon.AllQueryWordAggregateDataResponse;
import com.meiyunji.sponsored.rpc.adCommon.AllQueryWordDataResponse;
import com.meiyunji.sponsored.service.cpc.vo.*;


/**
 * <AUTHOR>
 * @date 2023/1/3
 */
public interface IWxCpcQueryKeywordReportService {

    /**
     * 搜索词首页数据
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    AllQueryWordDataResponse.AdQueryWordsHomeVo getAllQueryWordData(Integer puid, CpcQueryWordDto dto, Page page);

    /**
     * 搜索词广告管理汇总数据
     * @param puid
     * @param dto
     * @return
     */
    AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo getAllQueryWordAggregateData(Integer puid, CpcQueryWordDto dto);


    /**
     * 组装搜索词Vo
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    Page getAdQueryWordsPageVo(Integer puid, CpcQueryWordDto dto, Page page);

}
