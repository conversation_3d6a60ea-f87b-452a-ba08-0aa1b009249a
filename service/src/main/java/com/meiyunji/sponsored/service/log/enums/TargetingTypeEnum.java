package com.meiyunji.sponsored.service.log.enums;


public enum TargetingTypeEnum {
    /**
     * targeting 产品投放 定位类型
     * */
    similar_products("asinSubstituteRelated","同类产品"),
    related_products("asinAccessoryRelated","关联产品"),
    broad_match("queryBroadRelMatches","宽泛匹配"),
    close_match("queryHighRelMatches","紧密匹配"),
    asin("asin","ASIN"),
    category_target("category","类目");

    TargetingTypeEnum(String tagetType, String targetvalue){
        this.tagetType = tagetType;
        this.targetvalue = targetvalue;
    }

    private String tagetType;
    private String targetvalue;

    public static String getTargetValue(String tagetType){
        TargetingTypeEnum[] values = values();
        for (TargetingTypeEnum value : values) {
            if (value.getTagetType().equals(tagetType)){
                return value.getTargetvalue();
            }
        }
        return "";
    }

    public String getTagetType() {
        return tagetType;
    }

    public void setTagetType(String tagetType) {
        this.tagetType = tagetType;
    }

    public String getTargetvalue() {
        return targetvalue;
    }

    public void setTargetvalue(String targetvalue) {
        this.targetvalue = targetvalue;
    }
}
