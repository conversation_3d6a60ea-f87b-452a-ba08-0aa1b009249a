package com.meiyunji.sponsored.service.cpc.service2.sb;

import com.amazon.advertising.sb.entity.targetingRecommendation.CategoryRecommendationResults;
import com.amazon.advertising.sb.entity.targetingRecommendation.RecommendedProducts;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.qo.TargetSuggestBidBatchQo;
import com.meiyunji.sponsored.service.cpc.vo.*;

import java.util.List;

/**
 * Created by lm on 2021/8/3.
 */
public interface ICpcSbTargetService {

    Result createTargeting(AddSbTargetingVo addTargetingVo);
    NewCreateResultResultVo<SBCommonErrorVo> createTargeting(AddSbTargetingVo addTargetingVo, ShopAuth shop, AmazonAdProfile profile);

    Result update(UpdateSdTargetingVo updateSdTargetingVo);

    Result archive(Integer puid,Integer shopId,Integer uid, String targetId, String ip);


    Result<List<CategoryRecommendationResults>> addTargets(Integer puid, Integer shopId, String campaignId, String groupId);

    Result<List<CategoryRecommendationResults>> getTargetsCategoryList(Integer puid, Integer shopId, List<String> asinList);
    Result<TargetRecommendsPageVo> getTargetsCategoryListNew(Integer puid,
                                                             Integer shopId,
                                                             List<String> asinList,
                                                             Integer pageNo,
                                                             Integer pageSize,
                                                             String campaignId,
                                                             String groupId);

    /**
     * 根据分类id或关键词取品牌
     *
     * @param puid:
     * @param shopId:
     * @param categoryId:
     */
    Result<List<SuggestCategoryBrandVo>> suggestBrand(int puid, Integer shopId, String categoryId, String keyword);

    /**
     * 取建议asin
     *
     * @param puid
     * @param shopId
     * @param asinList
     * @return
     */
    Result<List<RecommendedProducts>> getSuggestAsin(Integer puid, Integer shopId, List<String> asinList);

    Result<List<SuggestedTargetVo>> getTargetBids(Integer puid, Integer shopId, String campaignId, String adFormat, List<TargetingVo> targetingVoList);
    Result<List<SuggestedTargetVo>> getTargetBidsNew(Integer puid, Integer shopId,
                                                     String adFormat, List<TargetingVo> targetingVoList,
                                                     String goal, String costType);

    /**
     * 列表页按指定target获取指定投放的建议竞价
     *
     * @param puid:
     * @param shopId:
     * @param targetIds:
     */
    Result<List<SuggestedTargetVo>> getSuggestedBid(int puid, Integer shopId, List<String> targetIds);

    /**
     * 多店铺，列表页按指定target获取指定投放的建议竞价
     */
    Result<List<SuggestedTargetVo>> getSuggestedBidMultiShop(int puid, List<TargetSuggestBidBatchQo> qoList);

    Result<List<SuggestedTargetVo>> batchGetSuggestedBid(int puid, Integer shopId, List<Integer> sourceShopIds, List<TargetSuggestedBidDetail> details, String targetingType);

    Result updateBatch(List<UpdateBatchTargetVo> vos, String type, String ip);

    Result updateBatchMultiShop(List<UpdateBatchTargetVo> vos, String type, String loginIp);

    void saveDoris(Integer puid, Integer shopId, List<String> targetIdList);
}
