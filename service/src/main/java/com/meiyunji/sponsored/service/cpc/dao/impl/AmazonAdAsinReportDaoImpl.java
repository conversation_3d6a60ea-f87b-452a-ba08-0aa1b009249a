package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdAsinReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdAsinReport;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdAsinDto;
import com.meiyunji.sponsored.service.cpc.vo.AdAsinPageParam;
import com.meiyunji.sponsored.service.cpc.vo.AsinReportPageParam;
import com.meiyunji.sponsored.service.cpc.vo.SearchVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> on 2021/4/29
 */
@Repository
public class AmazonAdAsinReportDaoImpl extends BaseShardingDaoImpl<AmazonAdAsinReport> implements IAmazonAdAsinReportDao {

    private final int imgLimit = 500;

    @Autowired
    private DynamicRefreshNacosConfiguration nacosConfiguration;

    @Override
    public void insertList(Integer puid, List<AmazonAdAsinReport> list) {
        //插入原表
        insertListOriginAndHotTable(list, getJdbcHelper().getTable());
        if (nacosConfiguration.isHotTableWritePhase2Enable()) {
            //插入热表
            insertListOriginAndHotTable(list, getHotTableName());
        }
    }

    private void insertListOriginAndHotTable(List<AmazonAdAsinReport> list, String tableName) {
        StringBuilder sql = new StringBuilder("insert into ");
        sql.append(tableName);
        sql.append(" (`puid`, `shop_id`, `marketplace_id`, `count_date`, `asin`, " +
                "`other_asin`, `sku`, `campaign_id`, `campaign_name`, `ad_group_id`, `ad_group_name`, `target_id`, `targeting_text`, `target_type`," +
                "`currency`, `attributedUnitsOrdered1d`, `attributedUnitsOrdered7d`, `attributedUnitsOrdered14d`, `attributedUnitsOrdered30d`, " +
                "`attributedUnitsOrdered1dOtherSKU`, `attributedUnitsOrdered7dOtherSKU`, `attributedUnitsOrdered14dOtherSKU`,`attributedUnitsOrdered30dOtherSKU`, " +
                "`attributedSales1dOtherSKU`,`attributedSales7dOtherSKU`,`attributedSales14dOtherSKU`,`attributedSales30dOtherSKU`,`match_type`, " +
                " `status`, purchases_other_sku_7d, `create_time`, `update_time`) values");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdAsinReport report : list) {
            sql.append(" (?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?,?, ?, ?, now(3),now(3)),");
            argsList.add(report.getPuid());
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getCountDate());
            argsList.add(report.getAsin());
            argsList.add(report.getOtherAsin());
            argsList.add(report.getSku());
            argsList.add(report.getCampaignId());
            argsList.add(report.getCampaignName());
            argsList.add(report.getAdGroupId());
            argsList.add(report.getAdGroupName());
            argsList.add(report.getTargetId());
            argsList.add(report.getTargetingText());
            argsList.add(report.getTargetType());
            argsList.add(report.getCurrency());
            argsList.add(report.getAttributedUnitsOrdered1d());
            argsList.add(report.getAttributedUnitsOrdered7d());
            argsList.add(report.getAttributedUnitsOrdered14d());
            argsList.add(report.getAttributedUnitsOrdered30d());
            argsList.add(report.getAttributedUnitsOrdered1dOtherSKU());
            argsList.add(report.getAttributedUnitsOrdered7dOtherSKU());
            argsList.add(report.getAttributedUnitsOrdered14dOtherSKU());
            argsList.add(report.getAttributedUnitsOrdered30dOtherSKU());
            argsList.add(report.getAttributedSales1dOtherSKU());
            argsList.add(report.getAttributedSales7dOtherSKU());
            argsList.add(report.getAttributedSales14dOtherSKU());
            argsList.add(report.getAttributedSales30dOtherSKU());
            argsList.add(report.getMatchType());
            argsList.add(1);
            argsList.add(report.getPurchasesOtherSku7d());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `ad_group_name`=values(ad_group_name),`campaign_name`=values(campaign_name),attributedUnitsOrdered1d=values(attributedUnitsOrdered1d),")
                .append("`attributedUnitsOrdered7d`=values(attributedUnitsOrdered7d),attributedUnitsOrdered14d=values(attributedUnitsOrdered14d),attributedUnitsOrdered30d=values(attributedUnitsOrdered30d),")
                .append("`attributedUnitsOrdered1dOtherSKU`=values(attributedUnitsOrdered1dOtherSKU),`attributedUnitsOrdered7dOtherSKU`=values(attributedUnitsOrdered7dOtherSKU),")
                .append("`attributedUnitsOrdered14dOtherSKU`=values(attributedUnitsOrdered14dOtherSKU),`attributedUnitsOrdered30dOtherSKU`=values(attributedUnitsOrdered30dOtherSKU),")
                .append("`attributedSales1dOtherSKU`=values(attributedSales1dOtherSKU),`attributedSales7dOtherSKU`=values(attributedSales7dOtherSKU),`attributedSales14dOtherSKU`=values(attributedSales14dOtherSKU),")
                .append("`attributedSales30dOtherSKU`=values(attributedSales30dOtherSKU),`match_type`=values(match_type),`status`=values(status),`purchases_other_sku_7d`=values(purchases_other_sku_7d)");
        getJdbcTemplate(list.get(0).getPuid()).update(sql.toString(), argsList.toArray());
    }

    @Override
    public Page<AmazonAdAsinReport> pageList(Integer puid, AsinReportPageParam param) {
        String tableName = getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD));
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("campaign_id", param.getCampaignId())
                .equalTo("ad_group_id", param.getGroupId())
                .greaterThanOrEqualTo("count_date", param.getStartDate())
                .lessThanOrEqualTo("count_date", param.getEndDate())
                .groupBy("asin", "other_asin", "sku", "target_id")
                .build();

        String sql = "select puid, shop_id, marketplace_id, campaign_id, ad_group_id, target_id, targeting_text, target_type," +
                " asin, asin_image, other_asin, other_asin_image, sku," +
                "sum(attributedUnitsOrdered7d) attributedUnitsOrdered7d, sum(attributedUnitsOrdered7dOtherSKU) attributedUnitsOrdered7dOtherSKU," +
                "sum(attributedSales7dOtherSKU) attributedSales7dOtherSKU from " + tableName + " where " + conditionBuilder.getSql();

        String countSql = "select count(*) from (select id from " + tableName + " where " + conditionBuilder.getSql() + ") t";

        return this.getPageResult(puid, param.getPageNo(), param.getPageSize(), countSql,
                conditionBuilder.getValues(), sql, conditionBuilder.getValues(), AmazonAdAsinReport.class);
    }

    @Override
    public Page<AmazonAdAsinReport> getPageList(Integer puid, SearchVo search, Page page) {
        String tableName = getTableNameByStartDate(search.getStart());
        String unionTableName = getTableNameByStartDateAndTableName(search.getStart(), "t_amazon_ad_asin_report_keyword");
        StringBuilder sql = new StringBuilder("SELECT puid,shop_id,marketplace_id,count_date,asin,sku,target_id,keyword_id,campaign_id,");
        sql.append("match_type,targeting_text,keyword_text,GROUP_CONCAT(other_asin) other_asin,campaign_name,ad_group_name,sum(`attributedUnitsOrdered7dOtherSKU`) attributedUnitsOrdered7dOtherSKU,sum(`attributedSales7dOtherSKU`) attributedSales7dOtherSKU");
        sql.append(" FROM ").append(tableName).append(" ");
        StringBuilder keywordSql = new StringBuilder(" UNION ALL SELECT puid,shop_id,marketplace_id,count_date,asin,sku,NULL as target_id,keyword_id,campaign_id,");
        keywordSql.append("match_type,NUll as targeting_text,keyword_text,GROUP_CONCAT(other_asin) other_asin,campaign_name,ad_group_name,sum(`attributed_units_ordered_7d_other_sku`) attributedUnitsOrdered7dOtherSKU,sum(`attributed_sales_7d_other_sku`) attributedSales7dOtherSKU");
        keywordSql.append(" FROM ").append(unionTableName).append(" ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");

        StringBuilder whereSql = new StringBuilder(" where puid=?");
        if (CollectionUtils.isNotEmpty(search.getShopIds())) {
            whereSql.append(" and shop_id in ('").append(StringUtils.join(search.getShopIds(), "','")).append("') ");
        }
        whereSql.append(" and count_date>=? and count_date<=? ");

        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(DateUtil.dateToStrWithFormat(search.getStart(), "yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(search.getEnd(), "yyyyMMdd"));
        argsList.add(puid);
        argsList.add(DateUtil.dateToStrWithFormat(search.getStart(), "yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(search.getEnd(), "yyyyMMdd"));
        whereSql.append(" group by shop_id,campaign_id,ad_group_id,`asin`");
        if ("daily".equals(search.getTabType())) {
            whereSql.append(", count_date");
        }
        Object[] args = argsList.toArray();
        sql.append(whereSql);
        keywordSql.append(whereSql);
        sql.append(keywordSql);
        countSql.append(sql).append(") c");
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, sql.toString(), args, AmazonAdAsinReport.class);
    }

    @Override
    public List<AmazonAdAsinReport> listNoAsinImage(Integer puid, Integer shopId, String startDate, String campaignId, long offset, int limit) {
        //不能使用热表查询，需要总表id用于更新字段
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .greaterThanOrEqualTo("count_date", startDate)
                .equalToWithoutCheck("asin_image", "");

        if (StringUtils.isNotBlank(campaignId)) {
            builder.equalTo("campaign_id", campaignId);
        }

        builder.greaterThan("id", offset);
        builder.orderBy("id");
        builder.limit(limit);

        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdAsinReport> listNoOtherAsinImage(Integer puid, Integer shopId, String startDate, String campaignId, long offset, int limit) {
        //不能使用热表查询，需要总表id用于更新字段
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .greaterThanOrEqualTo("count_date", startDate)
                .equalToWithoutCheck("other_asin_image", "");

        if (StringUtils.isNotBlank(campaignId)) {
            builder.equalTo("campaign_id", campaignId);
        }

        builder.greaterThan("id", offset);
        builder.orderBy("id");
        builder.limit(limit);

        return listByCondition(puid, builder.build());
    }

    @Override
    public void batchSetAsinImage(Integer puid, List<AmazonAdAsinReport> needUpdateList) {
        if (CollectionUtils.isNotEmpty(needUpdateList)) {
            batchSetAsinImageOrigin(puid, needUpdateList);
            if (nacosConfiguration.isHotTableWritePhase2Enable()) {
                batchSetAsinImageHot(puid, needUpdateList);
            }
        }
    }

    private void batchSetAsinImageOrigin(Integer puid, List<AmazonAdAsinReport> needUpdateList) {
        String sql = "update t_amazon_ad_asin_report set asin_image=?, update_time=now(3) where id=? and puid = ? ";
        List<Object[]> argsList = new ArrayList<>();
        List<Object> args;
        for (AmazonAdAsinReport amazonAdTargeting : needUpdateList) {
            args = new ArrayList<>();
            args.add(amazonAdTargeting.getAsinImage() != null
                    && amazonAdTargeting.getAsinImage().length() > imgLimit ?
                    amazonAdTargeting.getAsinImage().substring(0, imgLimit) : amazonAdTargeting.getAsinImage());
            args.add(amazonAdTargeting.getId());
            args.add(puid);
            argsList.add(args.toArray());
        }
        getJdbcTemplate(puid).batchUpdate(sql, argsList);
    }

    private void batchSetAsinImageHot(Integer puid, List<AmazonAdAsinReport> needUpdateList) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(getHotTableName());
        sql.append(" (`puid`,`shop_id`,`count_date`,`asin`,`other_asin`, `sku`, `target_id`, `asin_image`, `update_time`) values ");
        List<Object> batchArgs = Lists.newArrayList();

        for (AmazonAdAsinReport report : needUpdateList) {
            sql.append(" (?, ?, ?, ?, ?, ?, ?, ?, now(3)),");
            String asinImage = report.getAsinImage() != null && report.getAsinImage().length() > imgLimit ?
                    report.getAsinImage().substring(0, imgLimit) : report.getAsinImage();
            batchArgs.add(puid);
            batchArgs.add(report.getShopId());
            batchArgs.add(report.getCountDate());
            batchArgs.add(report.getAsin());
            batchArgs.add(report.getOtherAsin());
            batchArgs.add(report.getSku());
            batchArgs.add(report.getTargetId());
            batchArgs.add(asinImage);
        }

        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `asin_image`=values(asin_image), `update_time`=now(3)");
        getJdbcTemplate(puid).update(sql.toString(), batchArgs.toArray());
    }


    @Override
    public void batchSetOtherAsinImage(Integer puid, List<AmazonAdAsinReport> needUpdateList) {
        if (CollectionUtils.isNotEmpty(needUpdateList)) {
            batchSetOtherAsinImageOrigin(puid, needUpdateList);
            if (nacosConfiguration.isHotTableWritePhase2Enable()) {
                batchSetOtherAsinImageHot(puid, needUpdateList);
            }
        }
    }

    private void batchSetOtherAsinImageOrigin(Integer puid, List<AmazonAdAsinReport> needUpdateList) {
        String sql = "update t_amazon_ad_asin_report set other_asin_image=?, update_time=now(3) where id=? and puid = ? ";
        List<Object[]> argsList = new ArrayList<>();
        List<Object> args;
        for (AmazonAdAsinReport amazonAdTargeting : needUpdateList) {
            args = new ArrayList<>();
            args.add(amazonAdTargeting.getOtherAsinImage() != null
                    && amazonAdTargeting.getOtherAsinImage().length() > imgLimit ?
                    amazonAdTargeting.getOtherAsinImage().substring(0, imgLimit) : amazonAdTargeting.getOtherAsinImage());
            args.add(amazonAdTargeting.getId());
            args.add(puid);
            argsList.add(args.toArray());
        }
        getJdbcTemplate(puid).batchUpdate(sql, argsList);
    }

    private void batchSetOtherAsinImageHot(Integer puid, List<AmazonAdAsinReport> needUpdateList) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(getHotTableName());
        sql.append(" (`puid`,`shop_id`,`count_date`,`asin`,`other_asin`, `sku`, `target_id`, `other_asin_image`, `update_time`) values ");
        List<Object> batchArgs = Lists.newArrayList();

        for (AmazonAdAsinReport report : needUpdateList) {
            sql.append(" (?, ?, ?, ?, ?, ?, ?, ?, now(3)),");
            String otherAsinImage = report.getOtherAsinImage() != null && report.getOtherAsinImage().length() > imgLimit ?
                    report.getOtherAsinImage().substring(0, imgLimit) : report.getOtherAsinImage();
            batchArgs.add(puid);
            batchArgs.add(report.getShopId());
            batchArgs.add(report.getCountDate());
            batchArgs.add(report.getAsin());
            batchArgs.add(report.getOtherAsin());
            batchArgs.add(report.getSku());
            batchArgs.add(report.getTargetId());
            batchArgs.add(otherAsinImage);
        }

        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `other_asin_image`=values(other_asin_image), `update_time`=now(3)");
        getJdbcTemplate(puid).update(sql.toString(), batchArgs.toArray());
    }


    @Override
    public Page getAsinSizeData(Integer puid, AdAsinPageParam param, Page page) {
        String tableName = getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD));
        String unionTableName = getTableNameByStartDateAndTableName(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD), "t_amazon_ad_asin_report_keyword");
        StringBuilder selectSql = new StringBuilder("SELECT asin,SUM(attributedUnitsOrdered7dOtherSKU) as adOtherSaleNum FROM ");
        selectSql.append(tableName).append(" ");
        StringBuilder keywordSql = new StringBuilder(" UNION ALL SELECT asin,SUM(attributed_units_ordered_7d_other_sku) as adOtherSaleNum FROM ");
        keywordSql.append(unionTableName).append(" ");
        StringBuilder countSql = new StringBuilder("SELECT count(*) FROM ( ");
        List<Object> args = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" WHERE puid = ? and shop_id = ? and count_date >= ? and count_date <= ?  ");
        args.add(puid);
        args.add(param.getShopId());
        args.add(param.getStartDate());
        args.add(param.getEndDate());
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            whereSql.append(" and campaign_id = ? ");
            args.add(param.getCampaignId());
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            whereSql.append(" and campaign_id in ('").append(StringUtils.join(param.getCampaignIdList(), "','")).append("') ");
        }
        if (StringUtils.isNotBlank(param.getAdGroupId())) {
            whereSql.append(" and ad_group_id = ? ");
            args.add(param.getAdGroupId());
        }

        List<String> searchValues = Collections.emptyList();
        if (StringUtils.isNotBlank(param.getSearchValue()) && "parentAsin".equals(param.getSearchField())) {
            if (CollectionUtils.isEmpty(param.getAsinList())) {
                return new Page();
            }
            whereSql.append(SqlStringUtil.dealInList("asin", param.getAsinList(), args));
            searchValues = param.getAsinList();
        }
        if (StringUtils.isNotBlank(param.getSearchValue()) && "asin".equalsIgnoreCase(param.getSearchField())) {
            whereSql.append(SqlStringUtil.dealInList("asin", param.getListSearchValue(), args));
            searchValues = param.getListSearchValue();
        }
        if (StringUtils.isNotBlank(param.getSearchValue()) && "sku".equalsIgnoreCase(param.getSearchField())) {
            whereSql.append(SqlStringUtil.dealInList("sku", param.getListSearchValue(), args));
            searchValues = param.getListSearchValue();
        }
        whereSql.append(" and asin <> '' ");
        whereSql.append(" GROUP BY asin, campaign_id, ad_group_id ");
        whereSql.append(" HAVING adOtherSaleNum > 0 ");

        args.add(puid);
        args.add(param.getShopId());
        args.add(param.getStartDate());
        args.add(param.getEndDate());
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            args.add(param.getCampaignId());
        }
        if (StringUtils.isNotBlank(param.getAdGroupId())) {
            args.add(param.getAdGroupId());
        }
        if (CollectionUtils.isNotEmpty(searchValues)) {
            args.addAll(searchValues);
        }

        selectSql.append(whereSql).append(keywordSql.append(whereSql));
        countSql.append(selectSql).append(") t");

        Object[] argsList = args.toArray();

        return getPageByMapper(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), argsList, selectSql.toString(), argsList, new RowMapper<String>() {
            @Override
            public String mapRow(ResultSet rs, int rowNum) throws SQLException {
                return rs.getString("asin");
            }
        });
    }


    @Override
    public List<AdAsinDto> getAsinPageList(Integer puid, AdAsinPageParam param, List<String> asinList) {
        if (CollectionUtils.isEmpty(asinList)) {
            return new ArrayList<>();
        }
        String tableName = getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD));
        String unionTableName = getTableNameByStartDateAndTableName(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD), "t_amazon_ad_asin_report_keyword");
        StringBuilder selectSql = new StringBuilder("SELECT asin, asin_image as imgUrl, sku,campaign_id, campaign_name, ad_group_id, ad_group_name, other_asin, other_asin_image as otherImgUrl, sum(attributedUnitsOrdered7dOtherSKU) as adOtherSaleNum FROM ");
        selectSql.append(tableName).append(" ");
        StringBuilder keywordSql = new StringBuilder(" UNION ALL SELECT asin, asin_image as imgUrl, sku,campaign_id, campaign_name, ad_group_id, ad_group_name, other_asin, other_asin_image as otherImgUrl, sum(attributed_units_ordered_7d_other_sku) as adOtherSaleNum FROM ");
        keywordSql.append(unionTableName).append(" ");
        List<Object> args = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" WHERE puid = ? and shop_id = ? and count_date >= ? and count_date <= ?  ");
        args.add(puid);
        args.add(param.getShopId());
        args.add(param.getStartDate());
        args.add(param.getEndDate());
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            whereSql.append(" and campaign_id = ? ");
            args.add(param.getCampaignId());
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            whereSql.append(" and campaign_id in ('").append(StringUtils.join(param.getCampaignIdList(), "','")).append("') ");
        }
        if (StringUtils.isNotBlank(param.getAdGroupId())) {
            whereSql.append(" and ad_group_id = ? ");
            args.add(param.getAdGroupId());
        }
        whereSql.append(SqlStringUtil.dealInList("asin", asinList, args));

        args.add(puid);
        args.add(param.getShopId());
        args.add(param.getStartDate());
        args.add(param.getEndDate());
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            args.add(param.getCampaignId());
        }
        if (StringUtils.isNotBlank(param.getAdGroupId())) {
            args.add(param.getAdGroupId());
        }
        args.addAll(asinList);

        whereSql.append(" GROUP BY asin,other_asin,campaign_id,ad_group_id");
        whereSql.append(" HAVING adOtherSaleNum > 0 ");
        selectSql.append(whereSql);
        keywordSql.append(whereSql);
        selectSql.append(keywordSql);

        return getJdbcTemplate(param.getPuid()).query(selectSql.toString(), new RowMapper<AdAsinDto>() {
            @Override
            public AdAsinDto mapRow(ResultSet re, int i) throws SQLException {
                AdAsinDto dto = AdAsinDto.builder()
                        .asin(re.getString("asin"))
                        .imgUrl(re.getString("imgUrl"))
                        .sku(re.getString("sku"))
                        .campaignId(re.getString("campaign_id"))
                        .campaignName(re.getString("campaign_name"))
                        .adGroupId(re.getString("ad_group_id"))
                        .adGroupName(re.getString("ad_group_name"))
                        .otherAsin(re.getString("other_asin"))
                        .otherImgUrl(re.getString("otherImgUrl"))
                        .adOtherSaleNum(Optional.ofNullable(re.getInt("adOtherSaleNum")).orElse(0))
                        .build();
                return dto;
            }
        }, args.toArray());
    }


    @Override
    public List<String> getAsinListByDate(Integer puid, AdAsinPageParam param) {
        StringBuilder sql = new StringBuilder("SELECT DISTINCT asin FROM ( ");
        String tableName = getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD));
        String unionTableName = getTableNameByStartDateAndTableName(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD), "t_amazon_ad_asin_report_keyword");
        StringBuilder selectsql = new StringBuilder("SELECT asin FROM ");
        selectsql.append(tableName).append(" ");
        StringBuilder keywordSql = new StringBuilder(" UNION ALL SELECT asin FROM ");
        keywordSql.append(unionTableName).append(" ");
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? and count_date >= ? and count_date <= ? ");

        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(param.getShopId());
        args.add(param.getStartDate());
        args.add(param.getEndDate());
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            whereSql.append(" and campaign_id = ? ");
            args.add(param.getCampaignId());
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            whereSql.append(" and campaign_id in ('").append(StringUtils.join(param.getCampaignIdList(), "','")).append("') ");
        }
        if (StringUtils.isNotBlank(param.getAdGroupId())) {
            whereSql.append(" and ad_group_id = ? ");
            args.add(param.getAdGroupId());
        }
        List<String> searchValues = Collections.emptyList();
        if (StringUtils.isNotBlank(param.getSearchValue()) && "parentAsin".equals(param.getSearchField())) {
            if (CollectionUtils.isEmpty(param.getAsinList())) {
                return new ArrayList<>();
            }
            whereSql.append(SqlStringUtil.dealInList("asin", param.getAsinList(), args));
            searchValues = param.getAsinList();
        }
        if (StringUtils.isNotBlank(param.getSearchValue()) && "asin".equalsIgnoreCase(param.getSearchField())) {
            whereSql.append(SqlStringUtil.dealInList("asin", param.getListSearchValue(), args));
            searchValues = param.getListSearchValue();
        }
        if (StringUtils.isNotBlank(param.getSearchValue()) && "sku".equalsIgnoreCase(param.getSearchField())) {
            whereSql.append(SqlStringUtil.dealInList("sku", param.getListSearchValue(), args));
            searchValues = param.getListSearchValue();
        }
        whereSql.append(" and asin <> '' ");

        args.add(puid);
        args.add(param.getShopId());
        args.add(param.getStartDate());
        args.add(param.getEndDate());
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            args.add(param.getCampaignId());
        }
        if (StringUtils.isNotBlank(param.getAdGroupId())) {
            args.add(param.getAdGroupId());
        }
        if (CollectionUtils.isNotEmpty(searchValues)) {
            args.addAll(searchValues);
        }

        selectsql.append(whereSql.append(keywordSql.append(whereSql)));
        sql.append(selectsql).append(" ) t");


        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, args.toArray());
    }


    @Override
    public List<AdAsinDto> getAsinDataList(Integer puid, AdAsinPageParam param) {
        if (StringUtils.isBlank(param.getAsin()) || StringUtils.isBlank(param.getAdGroupId())) {
            return new ArrayList<>();
        }
        String tableName = getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD));
        String unionTableName = getTableNameByStartDateAndTableName(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD), "t_amazon_ad_asin_report_keyword");
        StringBuilder sql = new StringBuilder("SELECT asin, asin_image as imgUrl, sku, campaign_id, campaign_name, ad_group_id, ad_group_name, other_asin, other_asin_image as otherImgUrl, sum(attributedUnitsOrdered7dOtherSKU) as adOtherSaleNum FROM ");
        sql.append(tableName).append(" ");
        StringBuilder keywordSql = new StringBuilder(" UNION ALL SELECT asin, asin_image as imgUrl, sku,campaign_id, campaign_name, ad_group_id, ad_group_name, other_asin, other_asin_image as otherImgUrl, sum(attributed_units_ordered_7d_other_sku) as adOtherSaleNum FROM ");
        keywordSql.append(unionTableName).append(" ");
        StringBuilder whereSql = new StringBuilder(" WHERE puid = ? and shop_id = ? and count_date >= ? and count_date <= ? and asin = ? ");
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(param.getShopId());
        args.add(param.getStartDate());
        args.add(param.getEndDate());
        args.add(param.getAsin());
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            whereSql.append(" and campaign_id = ? ");
            args.add(param.getCampaignId());
        }
        if (StringUtils.isNotBlank(param.getAdGroupId())) {
            whereSql.append(" and ad_group_id = ? ");
            args.add(param.getAdGroupId());
        }
        args.add(puid);
        args.add(param.getShopId());
        args.add(param.getStartDate());
        args.add(param.getEndDate());
        args.add(param.getAsin());
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            args.add(param.getCampaignId());
        }
        if (StringUtils.isNotBlank(param.getAdGroupId())) {
            args.add(param.getAdGroupId());
        }

        whereSql.append("GROUP BY asin,other_asin HAVING adOtherSaleNum > 0 ");

        sql.append(whereSql);
        keywordSql.append(whereSql);
        sql.append(keywordSql);

        return getJdbcTemplate(param.getPuid()).query(sql.toString(), new RowMapper<AdAsinDto>() {
            @Override
            public AdAsinDto mapRow(ResultSet re, int i) throws SQLException {
                AdAsinDto dto = AdAsinDto.builder()
                        .asin(re.getString("asin"))
                        .imgUrl(re.getString("imgUrl"))
                        .sku(re.getString("sku"))
                        .campaignId(re.getString("campaign_id"))
                        .campaignName(re.getString("campaign_name"))
                        .adGroupId(re.getString("ad_group_id"))
                        .adGroupName(re.getString("ad_group_name"))
                        .otherAsin(re.getString("other_asin"))
                        .otherImgUrl(re.getString("otherImgUrl"))
                        .adOtherSaleNum(re.getInt("adOtherSaleNum"))
                        .build();
                return dto;
            }
        }, args.toArray());
    }

    @Override
    public List<AdAsinDto> getAsinDataListByDate(Integer puid, AdAsinPageParam param) {
        String tableName = getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD));
        String unionTableName = getTableNameByStartDateAndTableName(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD), "t_amazon_ad_asin_report_keyword");
        StringBuilder sql = new StringBuilder("SELECT asin, asin_image as imgUrl, sku, campaign_id, campaign_name, ad_group_id, ad_group_name, other_asin, other_asin_image as otherImgUrl, sum(attributedUnitsOrdered7dOtherSKU) as adOtherSaleNum FROM ");
        sql.append(tableName).append(" ");
        StringBuilder keywordSql = new StringBuilder(" UNION ALL SELECT asin, asin_image as imgUrl, sku, campaign_id, campaign_name, ad_group_id, ad_group_name, other_asin, other_asin_image as otherImgUrl, sum(attributed_units_ordered_7d_other_sku) as adOtherSaleNum FROM ");
        keywordSql.append(unionTableName).append(" ");
        StringBuilder whereSql = new StringBuilder(" WHERE puid = ? and shop_id = ? and count_date >= ? and count_date <= ? ");
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(param.getShopId());
        args.add(param.getStartDate());
        args.add(param.getEndDate());
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            whereSql.append(" and campaign_id = ? ");
            args.add(param.getCampaignId());
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            whereSql.append(" and campaign_id in ('").append(StringUtils.join(param.getCampaignIdList(), "','")).append("') ");
        }
        if (StringUtils.isNotBlank(param.getAdGroupId())) {
            whereSql.append(" and ad_group_id = ? ");
            args.add(param.getAdGroupId());
        }
        List<String> searchValues = Collections.emptyList();
        if (StringUtils.isNotBlank(param.getSearchValue()) && "parentAsin".equals(param.getSearchField())) {
            if (CollectionUtils.isEmpty(param.getAsinList())) {
                return new ArrayList<>();
            }
            whereSql.append(SqlStringUtil.dealInList("asin", param.getAsinList(), args));
            searchValues = param.getAsinList();
        }
        if (StringUtils.isNotBlank(param.getSearchValue()) && "asin".equalsIgnoreCase(param.getSearchField())) {
            whereSql.append(SqlStringUtil.dealInList("asin", param.getListSearchValue(), args));
            searchValues = param.getListSearchValue();
        }
        if (StringUtils.isNotBlank(param.getSearchValue()) && "sku".equalsIgnoreCase(param.getSearchField())) {
            whereSql.append(SqlStringUtil.dealInList("sku", param.getListSearchValue(), args));
            searchValues = param.getListSearchValue();
        }
        if (StringUtils.isNotBlank(param.getMarketplaceId())) {
            whereSql.append(" and marketplace_id = ? ");
            args.add(param.getMarketplaceId());
        }
        // 参数添加
        args.add(puid);
        args.add(param.getShopId());
        args.add(param.getStartDate());
        args.add(param.getEndDate());
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            args.add(param.getCampaignId());
        }
        if (StringUtils.isNotBlank(param.getAdGroupId())) {
            args.add(param.getAdGroupId());
        }
        if (CollectionUtils.isNotEmpty(searchValues)) {
            args.addAll(searchValues);
        }
        if (StringUtils.isNotBlank(param.getMarketplaceId())) {
            args.add(param.getMarketplaceId());
        }

        whereSql.append(" and asin <> '' GROUP BY asin,other_asin,campaign_id,ad_group_id ");
        whereSql.append(" HAVING adOtherSaleNum > 0 ");
        sql.append(whereSql).append(keywordSql.append(whereSql));
        sql.append(" LIMIT ").append(Constants.FILE_MAX_SIZE);

        return getJdbcTemplate(param.getPuid()).query(sql.toString(), new RowMapper<AdAsinDto>() {
            @Override
            public AdAsinDto mapRow(ResultSet re, int i) throws SQLException {
                AdAsinDto dto = AdAsinDto.builder()
                        .asin(re.getString("asin"))
                        .imgUrl(re.getString("imgUrl"))
                        .sku(re.getString("sku"))
                        .campaignId(re.getString("campaign_id"))
                        .campaignName(re.getString("campaign_name"))
                        .adGroupId(re.getString("ad_group_id"))
                        .adGroupName(re.getString("ad_group_name"))
                        .otherAsin(re.getString("other_asin"))
                        .otherImgUrl(re.getString("otherImgUrl"))
                        .adOtherSaleNum((re.getInt("adOtherSaleNum")))
                        .build();
                return dto;
            }
        }, args.toArray());
    }

}