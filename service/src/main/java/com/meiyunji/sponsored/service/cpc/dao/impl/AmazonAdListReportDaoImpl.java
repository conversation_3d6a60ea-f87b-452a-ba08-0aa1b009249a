package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.BaseDaoImpl;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdListReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdListReport;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * AmazonAdListReport
 * <AUTHOR>
 */
@Repository
public class  AmazonAdListReportDaoImpl extends BaseDaoImpl<AmazonAdListReport> implements IAmazonAdListReportDao {
    @Override
    public void insertOrUpdate(Integer puid, List<AmazonAdListReport> list) {
        StringBuilder sql = new StringBuilder(" INSERT INTO `t_amazon_ad_list_report` (`unique_key`,`puid`,`shop_id`,`marketplace_id`,`item_type`,")
                .append("`item_id`,`days`,`cpc`,`cpc_usd`,`cpc_rmb`,`cost`,`cost_usd`,`cost_rmb`,`sales`, `sales_usd`,`sales_rmb`,`acos`,`impressions`,")
                .append("`clicks`,`order_num`,`ad_order_num`,`sale_num`, `click_rate`,`sales_conversion_rate`,`create_time`,`update_time` ) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for(AmazonAdListReport report : list){
            sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(report.getUniqueKey());
            argsList.add(report.getPuid());
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getItemType());
            argsList.add(report.getItemId());
            argsList.add(report.getDays());
            argsList.add(report.getCpc());
            argsList.add(report.getCpcUsd());
            argsList.add(report.getCpcRmb());
            argsList.add(report.getCost());
            argsList.add(report.getCostUsd());
            argsList.add(report.getCostRmb());
            argsList.add(report.getSales());
            argsList.add(report.getSalesUsd());
            argsList.add(report.getSalesRmb());
            argsList.add(report.getAcos());
            argsList.add(report.getImpressions());
            argsList.add(report.getClicks());
            argsList.add(report.getOrderNum());
            argsList.add(report.getAdOrderNum());
            argsList.add(report.getSaleNum());
            argsList.add(report.getClickRate());
            argsList.add(report.getSalesConversionRate());
        }
        sql.deleteCharAt(sql.length()-1);
        sql.append(" on duplicate key update `cpc`=values(cpc),`cpc_usd`=values(cpc_usd),`cpc_rmb`=values(cpc_rmb),");
        sql.append("`cost`=values(cost),cost_rmb=values(cost_rmb),cost_usd=values(cost_usd),`sales`=values(sales),`sales_usd`=values(sales_usd),`sales_rmb`=values(sales_rmb),");
        sql.append("`acos`=values(acos),`impressions`=values(impressions),`clicks`=values(clicks),`order_num`=values(order_num),`ad_order_num`=values(ad_order_num),`sale_num`=values(sale_num),");
        sql.append("`click_rate`=values(click_rate),`sales_conversion_rate`=values(sales_conversion_rate),`update_time`=now()");
        getJdbcTemplate().update(sql.toString(),argsList.toArray());
    }
}