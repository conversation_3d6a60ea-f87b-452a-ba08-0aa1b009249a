package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sellfox.aadras.types.schedule.AdvertiseRuleTaskExecuteRecordV2Message;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.autoRule.vo.AdKeywordTargetAutoRuleParam;
import com.meiyunji.sponsored.service.autoRule.vo.SbRandomTargetBid;
import com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam;
import com.meiyunji.sponsored.service.cpc.bo.AdTargetBo;
import com.meiyunji.sponsored.service.export.dto.DownloadCenterBaseDataBO;
import com.meiyunji.sponsored.service.cpc.bo.AdOrderBo;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdTargeting;
import com.meiyunji.sponsored.service.cpc.qo.TargetSuggestBidBatchQo;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.pricing.ScheduleTaskFinishedVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.DiagnoseCountParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.TargetViewParam;
import com.meiyunji.sponsored.service.strategy.vo.AdTargetStrategyParam;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * Created by lm on 2021/7/29.
 */
public interface IAmazonSbAdTargetingDao extends IBaseShardingDao<AmazonSbAdTargeting> {

    void insertOrUpdate(int puid, List<AmazonSbAdTargeting> list);

    void batchAdd(int puid, List<AmazonSbAdTargeting> list);

    void batchUpdate(int puid, List<AmazonSbAdTargeting> list);

    void batchUpdateSuggestValue(int puid, List<AmazonSbAdTargeting> targetingList);

    List<AmazonSbAdTargeting> listByTargetId(int puid, int shopId, List<String> targetIds);

    List<AmazonSbAdTargeting> getByTargetSuggestBidBatchQo(int puid, List<TargetSuggestBidBatchQo> targetList);

    List<AmazonSbAdTargeting> listByTargetId(int puid, List<String> targetIds);

    List<AmazonSbAdTargeting> listByCondition(int puid, SbTargetingPageParam param);

    Page<AmazonSbAdTargeting> pageList(int puid, SbTargetingPageParam param);

    AmazonSbAdTargeting getByTargetId(int puid, Integer shopId, String targetId);

    Page getPageList(Integer puid, TargetingPageParam param, Page page);

    List<AmazonSbAdTargeting> getTargetViewList(Integer puid, TargetViewParam param);

    List<String> getTargetViewIdList(Integer puid, TargetViewParam param);

    List<AmazonSbAdTargeting> getList(Integer puid, TargetingPageParam param);

    List<AdTargetBo> getAdTargetBoList(Integer puid, TargetingPageParam param, Integer limit);

    /**
     * 修改报告数据最新更新时间
     * @param puid
     * @param shopId
     * @param targetId
     * @param localDate
     */
    void updateDataUpdateTime(Integer puid, Integer shopId, String targetId, LocalDate localDate);

    void updateList(Integer puid, List<AmazonSbAdTargeting> list, String type);

    List<String> getAsinsByCampaignId(Integer puid, Integer shopId, String campaignId);

    List<String> getTargetIdsByTarget(Integer puid, TargetingPageParam param);

    List<String> getTargetIds(Integer puid, Integer shopId, String marketPlaceId, List<String> onlineTargetId);

    boolean exist(Integer puid, Integer shopId, String campaignId);

    List<AmazonSbAdTargeting> listNoAsinImage(Integer puid, Integer shopId, long offset, int limit);

    void batchSetAsinImage(Integer puid, List<AmazonSbAdTargeting> needUpdateList);

    boolean existByGroupId(Integer puid, Integer shopId, String groupId);

    Page<AmazonSbAdTargeting> queryAdSbCommodityTarget(AdTargetStrategyParam param);

    /**
     * 查询SB商品投放
     *
     * @return
     */
    List<String> getByTargetIdList(Integer puid, Integer shopId,
                                   List<String> campaignIds,List<String> groupIds,String state,String targetType,List<String> targetIdList);

    /**
     * 修改状态
     *
     * @param puid
     * @param shopId
     * @param targetId
     * @param isPricing
     * @param pricingState
     * @param updateId
     */
    void updatePricing(Integer puid, Integer shopId, String targetId, Integer isPricing, Integer pricingState, int updateId);

    void strategyUpdate(int puid, int shopId, ScheduleTaskFinishedVo message);

    List<AmazonSbAdTargeting>  listByGroupIdsAndTargetText(Integer puid, List<String> groupIds, List<String> targetTexts);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

    List<AdGroupTargetCountDto> countByGroupIdList(Integer puid, Integer shopId, List<String> groupIdList);

    Page<SbTargetPageVo> getTargetPage(Integer puid, TargetingPageParam param);

    List<SbTargetPageVo> getTargetPageVoListByTargetIdList(Integer puid, TargetingPageParam param, List<String> targetIdList);

    List<String> getTargetIdListByParam(Integer puid, TargetingPageParam param, List<String> targetIdList);

    List<AdOrderBo> getTargetIdAndOrderFieldList(Integer puid, TargetingPageParam param, List<String> TargetIdList, String orderField);

    List<AmazonSbAdTargeting> listByGroupIdList(Integer puid, Integer shopId, List<String> groupIdList);

    List<AmazonSbAdTargeting> listByGroupId(Integer puid, Integer shopId, String adGroupId);

    List<AmazonSbAdTargeting> listByGroupIdAndItemIdList(Integer puid, Integer shopId, String adGroupId, List<String> itemIdList);

    void autoUpdate(int puid, int shopId, AdvertiseRuleTaskExecuteRecordV2Message message);


    Page<AmazonSbAdTargeting> queryAutoRuleAdSbTarget(AdKeywordTargetAutoRuleParam param, List<String> itemIds, List<String> similarItemIdList);

    List<AmazonSbAdTargeting> autoRuleTargetIdList(Integer puid, Integer shopId, List<String> campaignIds, List<String> groupIds, String state, String matchType, String searchValue, List<String> spTargetList);

    List<SbRandomTargetBid> listByGroupIdListRandomOne(Integer puid, Integer shopId, List<String> sbKeywordGroupIdList);

    Collection<String> queryAutoRuleTargetIdList(ProcessTaskParam param);

    List<String> getDiagnoseCountTargetId(DiagnoseCountParam param);

    List<AmazonSbAdTargeting> listByShopIdsAndTargetIds(int puid, List<Integer> shopIdList, List<String> targetIds);

    /**
     * 获取商品投放基础信息
     * @param puid
     * @param param
     * @param sbTargetIds
     * @return
     */
    List<AsinLibsDetailVo> getSbAsinDetailDataByTargetId(Integer puid, AsinLibsDetailParam param, List<String> sbTargetIds);

    List<AmazonSbAdTargeting> getArchivedTargetingByAdTargetIds(Integer puid, Integer id, List<String> targetItemIdList);

    List<DownloadCenterBaseDataBO> queryBaseData4DownloadByTargetIdList(Integer puid, Integer shopId, List<String> targetIdList);
}
